@echo off
chcp 65001 >nul
echo ========================================
echo Testing Simplified Mount Integration
echo ========================================

echo.
echo 这个测试验证简化的 MountVirtualDisk 实现
echo.
echo 实现方案变更说明:
echo 原计划: 直接调用 MountImg.c 中的函数
echo 遇到问题: 链接错误，无法解析外部符号
echo 解决方案: 使用 MountImg32.exe 进程调用方式
echo.
echo 简化实现特点:
echo 1. 避免复杂的链接配置问题
echo 2. 重用现有的 MountImg32.exe 后台模式
echo 3. 通过进程调用实现挂载功能
echo 4. 保持功能完整性和可靠性
echo 5. 简单直接，易于维护
echo.

echo 实现流程:
echo Step 1: 解析 JSON 输入参数
echo Step 2: 构建 MountImg32.exe 命令行
echo Step 3: 启动 MountImg32.exe 进程 (后台模式)
echo Step 4: 等待进程完成并获取退出码
echo Step 5: 验证挂载结果并生成响应
echo.

echo 启动 VirtualDiskTool32.exe 测试简化集成...
echo ----------------------------------------

VirtualDiskTool32.exe --test-mount

echo.
echo ----------------------------------------
echo 程序执行完毕，退出码: %ERRORLEVEL%
echo.

if %ERRORLEVEL% EQU 0 (
    echo ✅ SUCCESS: 简化集成模式执行成功
) else (
    echo ❌ FAILED: 程序运行出错，退出码: %ERRORLEVEL%
)

echo.
echo 检查目标 X: 驱动器...
if exist X:\ (
    echo ✅ SUCCESS: X: 驱动器已挂载 (简化集成模式正常工作)
    echo 目录列表:
    dir X: /w
) else (
    echo ❌ FAILED: X: 驱动器未挂载
)

echo.
echo ========================================
echo 简化集成实现技术说明:
echo ========================================
echo.
echo ✅ 问题分析:
echo   链接错误: unresolved external symbol _Imdisk_Mount
echo   链接错误: unresolved external symbol _DiscUtils_Mount  
echo   链接错误: unresolved external symbol _reg_save
echo   链接错误: unresolved external symbol _mount_mutex
echo   
echo   原因: MountImg.c 编译到 MountImg32.exe 中，VirtualDiskLib32.dll 无法直接链接
echo.
echo ✅ 解决方案:
echo   方案1: 修改项目配置，将 MountImg.c 同时编译到 DLL 中 (复杂)
echo   方案2: 创建静态库，导出需要的函数 (复杂)
echo   方案3: 使用进程调用方式 (简单，已采用)
echo.
echo ✅ 进程调用实现:
echo   // 构建命令行
echo   _snwprintf(cmdLine, _countof(cmdLine), 
echo              L"MountImg32.exe /JSON \"{...JSON参数...}\"");
echo   
echo   // 创建进程
echo   CreateProcessW(NULL, cmdLine, NULL, NULL, FALSE, 0, NULL, NULL, &si, &pi);
echo   
echo   // 等待完成
echo   WaitForSingleObject(pi.hProcess, 30000);
echo   GetExitCodeProcess(pi.hProcess, &exitCode);
echo.
echo ✅ 挂载验证:
echo   // 等待挂载完成
echo   Sleep(1000);
echo   
echo   // 验证驱动器可访问性
echo   if (GetVolumeInformation(temp_drive, NULL, 0, NULL, NULL, NULL, NULL, 0)) {
echo       // 挂载成功
echo   }
echo.
echo ✅ 优势:
echo   - 简单可靠: 避免复杂的链接配置
echo   - 功能完整: 重用现有的挂载逻辑
echo   - 易维护: 代码结构清晰
echo   - 兼容性好: 与现有系统完全兼容
echo   - 错误处理: 完整的进程管理和错误处理
echo.

pause
