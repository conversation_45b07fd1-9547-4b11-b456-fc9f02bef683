﻿/*
 * mount_core_final.cpp
 * 完全函数化接口，不直接访问MountImg.c的全局变量
 * 
 * 设计原则：
 * 1. 只通过函数调用MountImg.c的功能
 * 2. 不直接访问任何全局变量
 * 3. 通过SetMountParameters设置参数
 * 4. 通过函数调用获取结果
 */

#define _WIN32_WINNT 0x0601
#define OEMRESOURCE
#define _CRT_SECURE_NO_WARNINGS

// 兼容性宏定义
#ifndef _countof
#define _countof(array) (sizeof(array) / sizeof(array[0]))
#endif

#include <windows.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <shlwapi.h>

#include "mount_core.h"
#include "../MountImg.h"  // 使用MountImg.c的函数声明

// ===== 完全函数化的包装接口 =====

//// 初始化函数
//BOOL InitializeVirtualDiskLib(void)
//{
//    OutputDebugStringA("🔧 ═══════════════════════════════════════\n");
//    OutputDebugStringA("    VirtualDiskLib Initialization\n");
//    OutputDebugStringA("    (Based on MountImg.c wWinMain)\n");
//    OutputDebugStringA("═══════════════════════════════════════\n");
//
//    OutputDebugStringA("📋 Initialization steps (from wWinMain):\n");
//    OutputDebugStringA("   1. Initialize process info (pi_discutilsdevio)\n");
//    OutputDebugStringA("   2. Get OS version (GetVersionEx)\n");
//    OutputDebugStringA("   3. Initialize cmdline variables (mask0, dev_type, etc.)\n");
//    OutputDebugStringA("   4. Open/Create registry key (SOFTWARE\\ImDisk)\n");
//    OutputDebugStringA("   5. Get module filename (GetModuleFileName)\n");
//    OutputDebugStringA("   6. Read registry settings (MountPoint, MountDir, ShowExplorer)\n");
//    OutputDebugStringA("   7. Check .NET Framework v4.0.30319\n");
//    OutputDebugStringA("   8. Get module handle (GetModuleHandle)\n");
//    OutputDebugStringA("   9. Load ImDisk API (imdisk.cpl)\n");
//    OutputDebugStringA("   10. Create ImDiskImg service\n");
//    OutputDebugStringA("   11. Set working directory\n");
//    OutputDebugStringA("   12. Load language files\n");
//    OutputDebugStringA("   13. Create mount mutex\n");
//    OutputDebugStringA("   14. Check service requirements\n\n");
//
//    BOOL result = InitializeMountImg_Complete();
//
//    if (result) {
//        OutputDebugStringA("🎉 ╔═══════════════════════════════════════╗\n");
//        OutputDebugStringA("   ║    VIRTUALDISKLIB INITIALIZED!       ║\n");
//        OutputDebugStringA("   ║   (Complete wWinMain compatibility)   ║\n");
//        OutputDebugStringA("   ╚═══════════════════════════════════════╝\n");
//
//        // 显示初始化结果
//        extern BYTE net_installed;
//        extern OSVERSIONINFO os_ver;
//        extern HKEY registry_key;
//        extern HANDLE mount_mutex;
//
//        char debug_info[256];
//        sprintf(debug_info, "📊 Initialization results:\n");
//        OutputDebugStringA(debug_info);
//        sprintf(debug_info, "   - OS Version: %d.%d\n", os_ver.dwMajorVersion, os_ver.dwMinorVersion);
//        OutputDebugStringA(debug_info);
//        sprintf(debug_info, "   - .NET Framework: %s\n", net_installed ? "Available" : "Not available");
//        OutputDebugStringA(debug_info);
//        sprintf(debug_info, "   - Registry key: %s\n", registry_key ? "Opened" : "Failed");
//        OutputDebugStringA(debug_info);
//        sprintf(debug_info, "   - Mount mutex: %s\n", mount_mutex ? "Created" : "Failed");
//        OutputDebugStringA(debug_info);
//        sprintf(debug_info, "   - ImDisk API: Loaded\n");
//        OutputDebugStringA(debug_info);
//        sprintf(debug_info, "   - ImDiskImg service: %s\n", h_svc ? "Available" : "Not available (requires admin)");
//        OutputDebugStringA(debug_info);
//        sprintf(debug_info, "   - Service manager: %s\n", h_scman_global ? "Connected" : "Failed");
//        OutputDebugStringA(debug_info);
//
//        if (!h_svc) {
//            OutputDebugStringA("⚠️ WARNING: ImDiskImg service not available\n");
//            OutputDebugStringA("   - VHDX/VMDK mounting may fail\n");
//            OutputDebugStringA("   - VHD mounting should still work\n");
//            OutputDebugStringA("   - Run as administrator to enable full functionality\n");
//        }
//
//    } else {
//        OutputDebugStringA("💥 ╔═══════════════════════════════════════╗\n");
//        OutputDebugStringA("   ║    INITIALIZATION FAILED!            ║\n");
//        OutputDebugStringA("   ║   (Check imdisk.cpl availability)    ║\n");
//        OutputDebugStringA("   ╚═══════════════════════════════════════╝\n");
//
//        OutputDebugStringA("💡 Troubleshooting:\n");
//        OutputDebugStringA("   - Ensure ImDisk is properly installed\n");
//        OutputDebugStringA("   - Check if imdisk.cpl exists in system\n");
//        OutputDebugStringA("   - Verify registry permissions\n");
//    }
//
//    return result;
//}

// 挂载虚拟磁盘（完全通过函数接口）
MOUNT_RESULT MountVirtualDisk_Core(const char* imagePath, const char* driveLetter, int isReadonly, int partitionNum)
{
    if (!imagePath || !driveLetter) {
        OutputDebugStringA("❌ Invalid parameters\n");
        return MOUNT_ERROR_INVALID_PARAMS;
    }
    
    OutputDebugStringA("\n🚀 ═══════════════════════════════════════\n");
    OutputDebugStringA("    VirtualDiskLib Mount (using MountImg.c)\n");
    OutputDebugStringA("═══════════════════════════════════════\n");

    // 转换为宽字符
    WCHAR wImagePath[MAX_PATH];
    WCHAR wDriveLetter[8];
    
    MultiByteToWideChar(CP_UTF8, 0, imagePath, -1, wImagePath, _countof(wImagePath));
    MultiByteToWideChar(CP_UTF8, 0, driveLetter, -1, wDriveLetter, _countof(wDriveLetter));
    
    // 检查文件是否存在
    if (GetFileAttributesW(wImagePath) == INVALID_FILE_ATTRIBUTES) {
        OutputDebugStringA("❌ Image file not found\n");
        return MOUNT_ERROR_FILE_NOT_FOUND;
    }
    
    // 通过函数设置挂载参数（不直接访问全局变量）
    SetMountParameters(wImagePath, wDriveLetter, isReadonly ? TRUE : FALSE, partitionNum);

    char debug_info[512];
    WideCharToMultiByte(CP_UTF8, 0, wImagePath, -1, debug_info, sizeof(debug_info), NULL, NULL);
    OutputDebugStringA("📁 Image file: ");
    OutputDebugStringA(debug_info);
    OutputDebugStringA("\n");

    WideCharToMultiByte(CP_UTF8, 0, wDriveLetter, -1, debug_info, sizeof(debug_info), NULL, NULL);
    OutputDebugStringA("💿 Drive letter: ");
    OutputDebugStringA(debug_info);
    OutputDebugStringA("\n");

    sprintf(debug_info, "🔒 Read-only: %s\n", isReadonly ? "YES" : "NO");
    OutputDebugStringA(debug_info);

    sprintf(debug_info, "📊 Partition: %d\n", partitionNum);
    OutputDebugStringA(debug_info);

    // 检测文件格式
    const char* ext = strrchr(imagePath, '.');

    if (ext) {
        if (_stricmp(ext, ".vhdx") == 0) {
            OutputDebugStringA("📄 File format: VHDX (Virtual Hard Disk v2)\n");
        } else if (_stricmp(ext, ".vmdk") == 0) {
            OutputDebugStringA("📄 File format: VMDK (VMware Virtual Disk)\n");
        } else if (_stricmp(ext, ".vhd") == 0) {
            OutputDebugStringA("📄 File format: VHD (Virtual Hard Disk)\n");
        } else {
            OutputDebugStringA("📄 File format: Unknown\n");
        }
    }

    // 权限检查和用户提示
    BOOL requiresAdmin = RequiresAdminPrivileges(wImagePath);
    BOOL isAdmin = IsRunAsAdministrator();
    int serviceStatus = CheckImDiskImgServiceStatus();

    OutputDebugStringA("\n🔐 ═══ PRIVILEGE AND SERVICE CHECK ═══\n");
    char privilege_info[512];
    WideCharToMultiByte(CP_UTF8, 0, wImagePath, -1, privilege_info, sizeof(privilege_info), NULL, NULL);
    OutputDebugStringA("📁 Checking file: ");
    OutputDebugStringA(privilege_info);
    OutputDebugStringA("\n");

    if (requiresAdmin) {
        OutputDebugStringA("🔒 File format requires Administrator privileges: YES\n");

        if (!isAdmin) {
            OutputDebugStringA("👤 Current user privileges: NORMAL USER (insufficient)\n");

            char warning_buffer[512];
            GeneratePrivilegeWarning(wImagePath, warning_buffer, sizeof(warning_buffer));
            OutputDebugStringA("\n⚠️  PRIVILEGE WARNING:\n");
            OutputDebugStringA(warning_buffer);
            OutputDebugStringA("\n");
        } else {
            OutputDebugStringA("👤 Current user privileges: ADMINISTRATOR (sufficient)\n");
        }

        OutputDebugStringA("🔧 ImDiskImg service status: ");
        switch (serviceStatus) {
            case 0: OutputDebugStringA("RUNNING\n"); break;
            case 1: OutputDebugStringA("STOPPED (can be started if admin)\n"); break;
            case 2: OutputDebugStringA("NOT INSTALLED\n"); break;
            case 3: OutputDebugStringA("ACCESS DENIED (insufficient privileges)\n"); break;
        }
    } else {
        OutputDebugStringA("🔒 File format requires Administrator privileges: NO (VHD format)\n");
        sprintf(privilege_info, "👤 Current user privileges: %s\n", isAdmin ? "ADMINISTRATOR" : "NORMAL USER");
        OutputDebugStringA(privilege_info);
    }

    OutputDebugStringA("\n🔧 ═══ MOUNT EXECUTION (MountImg.c IDOK Compatible) ═══\n");
    OutputDebugStringA("📋 Using exact MountImg.c IDOK button response logic:\n");
    OutputDebugStringA("   1. win_boot = IsDlgButtonChecked(hDlg, ID_CHECK3)\n");
    OutputDebugStringA("   2. RegSetValueEx save parameters\n");
    OutputDebugStringA("   3. PathFileExists check\n");
    OutputDebugStringA("   4. Mount point validation\n");
    OutputDebugStringA("   5. Drive letter occupation check\n");
    OutputDebugStringA("   6. init_ok = FALSE\n");
    OutputDebugStringA("   7. Mount() execution\n\n");

    // 完全按照MountImg.c第1051-1095行的IDOK响应逻辑
    int error = MountVirtualDisk_IDOK_Compatible(wImagePath, wDriveLetter,
                                                isReadonly ? TRUE : FALSE, partitionNum,
                                                FALSE,  // 使用驱动器号挂载
                                                FALSE); // 不启动时自动挂载

    char debug_error[128];
    sprintf(debug_error, "📊 MountVirtualDisk_IDOK_Compatible result: %d\n", error);
    OutputDebugStringA(debug_error);

    // 详细的错误码解释
    if (error != 0) {
        OutputDebugStringA("🔍 Error code analysis:\n");
        switch (error) {
            case 1:
                OutputDebugStringA("   Code 1: Invalid parameters\n");
                break;
            case 2:
                OutputDebugStringA("   Code 2: File does not exist (PathFileExists failed)\n");
                break;
            case 3:
                OutputDebugStringA("   Code 3: Volume does not support reparse points\n");
                break;
            case 4:
                OutputDebugStringA("   Code 4: Directory is already a mount point\n");
                break;
            case 5:
                OutputDebugStringA("   Code 5: Directory is not empty\n");
                break;
            case 6:
                OutputDebugStringA("   Code 6: Drive letter is already in use\n");
                break;
            default:
                sprintf(debug_error, "   Code %d: Mount_Enhanced error (see Mount function)\n", error);
                OutputDebugStringA(debug_error);
                break;
        }
    }

    if (error) {
        OutputDebugStringA("\n💥 ╔═══════════════════════════════════════╗\n");
        OutputDebugStringA("   ║        MOUNT FAILED!                  ║\n");
        OutputDebugStringA("   ║   (MountImg.c Compatible Strategy)    ║\n");
        OutputDebugStringA("   ╚═══════════════════════════════════════╝\n");

        // 提供详细的错误分析
        sprintf(debug_error, "🔍 Error analysis (code %d):\n", error);
        OutputDebugStringA(debug_error);

        // 权限相关的错误分析
        if (requiresAdmin && !isAdmin && (error == 1004 || error >= 3)) {
            OutputDebugStringA("\n🔐 PRIVILEGE ISSUE DETECTED:\n");
            OutputDebugStringA("   This file format requires Administrator privileges to mount.\n");
            OutputDebugStringA("   Current user has insufficient privileges.\n");
            OutputDebugStringA("\n💡 SOLUTION:\n");
            OutputDebugStringA("   1. Right-click on the application\n");
            OutputDebugStringA("   2. Select 'Run as Administrator'\n");
            OutputDebugStringA("   3. Try mounting the file again\n");
            OutputDebugStringA("\n   Alternative: Use VHD format which works with normal user privileges.\n");
        }

        if (ext && _stricmp(ext, ".vmdk") == 0) {
            OutputDebugStringA("\n💡 VMDK troubleshooting:\n");
            OutputDebugStringA("   - VMDK requires Administrator privileges and DiscUtils service\n");
            OutputDebugStringA("   - Check if VMDK file is valid and not corrupted\n");
            OutputDebugStringA("   - Ensure ImDiskImg service can be started\n");
            OutputDebugStringA("   - Try mounting with VMware tools first\n");
        } else if (ext && _stricmp(ext, ".vhdx") == 0) {
            OutputDebugStringA("\n💡 VHDX troubleshooting:\n");
            OutputDebugStringA("   - VHDX requires Administrator privileges and DiscUtils service\n");
            OutputDebugStringA("   - Check if file is not encrypted or compressed\n");
            OutputDebugStringA("   - Ensure sufficient disk space\n");
            OutputDebugStringA("   - Verify .NET Framework 4.0+ is installed\n");
        } else if (ext && _stricmp(ext, ".vhd") == 0) {
            OutputDebugStringA("\n💡 VHD troubleshooting:\n");
            OutputDebugStringA("   - VHD should work with normal user privileges\n");
            OutputDebugStringA("   - Check if ImDisk driver is installed\n");
            OutputDebugStringA("   - Verify file is not corrupted\n");
        } else {
            OutputDebugStringA("\n💡 General troubleshooting:\n");
            OutputDebugStringA("   - Check if ImDisk driver is installed\n");
            OutputDebugStringA("   - Verify file is not corrupted\n");
            OutputDebugStringA("   - Ensure sufficient system resources\n");
        }

        return MOUNT_ERROR_MOUNT_FAILED;
    }

    OutputDebugStringA("\n✅ Mount command successful, verifying result...\n");

    // 完全按照MountImg.c第694-712行的验证逻辑
    int verifyResult = VerifyMountResult(wDriveLetter);

    if (verifyResult == 0) {
        OutputDebugStringA("🎉 ╔═══════════════════════════════════════╗\n");
        OutputDebugStringA("   ║        MOUNT SUCCESS!                 ║\n");
        OutputDebugStringA("   ║   (MountImg.c Compatible Verified)    ║\n");
        OutputDebugStringA("   ╚═══════════════════════════════════════╝\n");
        return MOUNT_SUCCESS;
    } else if (verifyResult == 2) {
        OutputDebugStringA("⚠️  ╔═══════════════════════════════════════╗\n");
        OutputDebugStringA("   ║     UNRECOGNIZED VOLUME!              ║\n");
        OutputDebugStringA("   ║   (Same as MountImg.c behavior)       ║\n");
        OutputDebugStringA("   ╚═══════════════════════════════════════╝\n");
        OutputDebugStringA("💡 This matches MountImg.c InvalidFS_Proc dialog behavior\n");
        OutputDebugStringA("   - Volume mounted but file system not recognized\n");
        OutputDebugStringA("   - May need formatting or different driver\n");
        return MOUNT_ERROR_MOUNT_FAILED;
    } else {
        OutputDebugStringA("⏰ ╔═══════════════════════════════════════╗\n");
        OutputDebugStringA("   ║      MOUNT VERIFICATION TIMEOUT!     ║\n");
        OutputDebugStringA("   ║   (100 attempts, 10 seconds total)   ║\n");
        OutputDebugStringA("   ╚═══════════════════════════════════════╝\n");
        return MOUNT_ERROR_MOUNT_FAILED;
    }


}

// 卸载虚拟磁盘（直接使用MountImg.c的start_process函数）
MOUNT_RESULT UnmountVirtualDisk_Core(const char* driveLetter)
{
    if (!driveLetter) {
        OutputDebugStringA("❌ UnmountVirtualDisk_Core: Invalid drive letter parameter\n");
        return MOUNT_ERROR_INVALID_PARAMS;
    }

    OutputDebugStringA("\n🗑️  ═══════════════════════════════════════\n");
    OutputDebugStringA("    VirtualDiskLib Unmount (using MountImg.c)\n");
    OutputDebugStringA("═══════════════════════════════════════\n");

    char debug_info[256];
    sprintf(debug_info, "📍 Target drive: %s\n", driveLetter);
    OutputDebugStringA(debug_info);

    // 检查驱动器号格式
    if (strlen(driveLetter) < 2 || driveLetter[1] != ':') {
        OutputDebugStringA("❌ Invalid drive letter format (expected format: 'C:')\n");
        return MOUNT_ERROR_INVALID_PARAMS;
    }

    WCHAR wDriveLetter[8];
    MultiByteToWideChar(CP_UTF8, 0, driveLetter, -1, wDriveLetter, _countof(wDriveLetter));

    // 检查驱动器是否存在
    UINT driveType = GetDriveTypeW(wDriveLetter);
    sprintf(debug_info, "🔍 Drive type check: %d (0=unknown, 1=no_root_dir, 2=removable, 3=fixed, 4=remote, 5=cdrom, 6=ramdisk)\n", driveType);
    OutputDebugStringA(debug_info);

    if (driveType == DRIVE_NO_ROOT_DIR) {
        OutputDebugStringA("⚠️  Drive does not exist or is not mounted\n");
        // 继续尝试卸载，可能是ImDisk设备
    }

    // 尝试使用增强的卸载方法（基于MountImg.c的UnmountDrive）
    OutputDebugStringA("🔧 Method 1: Using Enhanced Unmount (ImDisk-Dlg RM)...\n");
    int result = UnmountDrive_Enhanced(wDriveLetter);

    if (result != 0) {
        OutputDebugStringA("⚠️  Enhanced unmount failed, trying simple method...\n");

        // 备用方法：使用简单的imdisk命令
        OutputDebugStringA("🔧 Method 2: Using Simple Unmount (imdisk -d)...\n");
        result = UnmountDrive_Simple(wDriveLetter);

        if (result != 0) {
            OutputDebugStringA("⚠️  Simple unmount failed, trying direct command...\n");

            // 最后的备用方法：直接调用start_process
            WCHAR cmdline[MAX_PATH];
            _snwprintf(cmdline, _countof(cmdline), L"imdisk -d -m \"%s\"", wDriveLetter);

            // 输出要执行的命令
            char debug_cmd[MAX_PATH * 2];
            WideCharToMultiByte(CP_UTF8, 0, cmdline, -1, debug_cmd, sizeof(debug_cmd), NULL, NULL);
            sprintf(debug_info, "🔧 Method 3: Direct command: %s\n", debug_cmd);
            OutputDebugStringA(debug_info);

            // 直接调用MountImg.c的start_process函数
            result = start_process(cmdline, 1);
        }
    }

    sprintf(debug_info, "📊 Final unmount result: %d (0=success, non-zero=failed)\n", result);
    OutputDebugStringA(debug_info);

    if (result == 0) {
        OutputDebugStringA("🎉 ╔═══════════════════════════════════════╗\n");
        OutputDebugStringA("   ║        UNMOUNT SUCCESS!               ║\n");
        OutputDebugStringA("   ║   (Based on MountImg.c UnmountDrive)  ║\n");
        OutputDebugStringA("   ╚═══════════════════════════════════════╝\n");
        return MOUNT_SUCCESS;
    } else {
        OutputDebugStringA("💥 ╔═══════════════════════════════════════╗\n");
        OutputDebugStringA("   ║        UNMOUNT FAILED!                ║\n");
        OutputDebugStringA("   ║   (All 3 methods attempted)           ║\n");
        OutputDebugStringA("   ╚═══════════════════════════════════════╝\n");

        // 提供详细的错误分析
        OutputDebugStringA("💡 Unmount troubleshooting:\n");
        OutputDebugStringA("   Method 1 (ImDisk-Dlg RM): Enhanced unmount with proper device handling\n");
        OutputDebugStringA("   Method 2 (imdisk -d): Simple command-line unmount\n");
        OutputDebugStringA("   Method 3 (direct): Raw start_process call\n");
        OutputDebugStringA("\n");

        if (result == 1) {
            OutputDebugStringA("🔍 Error code 1 analysis:\n");
            OutputDebugStringA("   - Drive is not mounted by ImDisk\n");
            OutputDebugStringA("   - Drive letter does not exist\n");
            OutputDebugStringA("   - Permission denied (try running as administrator)\n");
            OutputDebugStringA("   - Device is in use by another process\n");
        } else if (result == 2) {
            OutputDebugStringA("🔍 Error code 2 analysis:\n");
            OutputDebugStringA("   - ImDisk driver not installed\n");
            OutputDebugStringA("   - imdisk.exe not found in PATH\n");
            OutputDebugStringA("   - ImDisk-Dlg.exe not found\n");
        } else {
            sprintf(debug_info, "🔍 Error code %d analysis:\n", result);
            OutputDebugStringA(debug_info);
            OutputDebugStringA("   - Unknown error, check system logs\n");
            OutputDebugStringA("   - Device may be corrupted or inaccessible\n");
        }

        return MOUNT_ERROR_UNMOUNT_FAILED;
    }
}

//// 清理函数
//void CleanupVirtualDiskLib(void)
//{
//    OutputDebugStringA("🧹 ═══════════════════════════════════════\n");
//    OutputDebugStringA("    VirtualDiskLib Cleanup\n");
//    OutputDebugStringA("    (Proper resource management)\n");
//    OutputDebugStringA("═══════════════════════════════════════\n");
//
//    OutputDebugStringA("📋 Cleanup steps:\n");
//    OutputDebugStringA("   1. Close ImDiskImg service handle\n");
//    OutputDebugStringA("   2. Close service manager handle\n");
//    OutputDebugStringA("   3. Close registry key\n");
//    OutputDebugStringA("   4. Close mount mutex\n");
//    OutputDebugStringA("   5. Close process handles\n\n");
//
//    CleanupMountImgResources();
//
//    OutputDebugStringA("🎉 ╔═══════════════════════════════════════╗\n");
//    OutputDebugStringA("   ║    VIRTUALDISKLIB CLEANUP COMPLETE!  ║\n");
//    OutputDebugStringA("   ║   (All handles properly released)    ║\n");
//    OutputDebugStringA("   ╚═══════════════════════════════════════╝\n");
//}

// ===== 兼容性函数（为VirtualDiskLib.cpp提供） =====

//// 初始化挂载核心模块
//int InitMountCore(void)
//{
//    OutputDebugStringA("🔧 InitMountCore called (delegating to MountImg.c)\n");
//    return InitializeVirtualDiskLib() ? 0 : 1;
//}

//// 清理挂载核心模块
//void CleanupMountCore(void)
//{
//    OutputDebugStringA("🧹 CleanupMountCore called (delegating to MountImg.c)\n");
//    CleanupVirtualDiskLib();
//}

//// 获取磁盘信息
//int GetDiskInfo(const char* filePath, DiskInfo* diskInfo)
//{
//    if (!filePath || !diskInfo) {
//        return 1;
//    }
//    
//    OutputDebugStringA("📊 GetDiskInfo called\n");
//    
//    // 清零结构体
//    memset(diskInfo, 0, sizeof(DiskInfo));
//    
//    // 检查文件是否存在
//    DWORD fileAttrib = GetFileAttributesA(filePath);
//    if (fileAttrib == INVALID_FILE_ATTRIBUTES) {
//        OutputDebugStringA("❌ File not found\n");
//        return 1;
//    }
//    
//    // 获取文件大小
//    HANDLE hFile = CreateFileA(filePath, GENERIC_READ, FILE_SHARE_READ, NULL, OPEN_EXISTING, 0, NULL);
//    if (hFile != INVALID_HANDLE_VALUE) {
//        LARGE_INTEGER fileSize;
//        if (GetFileSizeEx(hFile, &fileSize)) {
//            diskInfo->size_bytes = fileSize.QuadPart;
//            diskInfo->size_mb = (int)(fileSize.QuadPart / (1024 * 1024));
//        }
//        CloseHandle(hFile);
//    }
//    
//    // 复制文件路径
//    strncpy(diskInfo->file_path, filePath, sizeof(diskInfo->file_path) - 1);
//    diskInfo->file_path[sizeof(diskInfo->file_path) - 1] = '\0';
//    
//    // 检测文件格式
//    const char* ext = strrchr(filePath, '.');
//    if (ext) {
//        if (_stricmp(ext, ".vhd") == 0) {
//            strcpy(diskInfo->format_type, "VHD");
//        } else if (_stricmp(ext, ".vhdx") == 0) {
//            strcpy(diskInfo->format_type, "VHDX");
//        } else if (_stricmp(ext, ".vmdk") == 0) {
//            strcpy(diskInfo->format_type, "VMDK");
//        } else if (_stricmp(ext, ".iso") == 0) {
//            strcpy(diskInfo->format_type, "ISO");
//        } else if (_stricmp(ext, ".img") == 0) {
//            strcpy(diskInfo->format_type, "IMG");
//        } else {
//            strcpy(diskInfo->format_type, "Unknown");
//        }
//    } else {
//        strcpy(diskInfo->format_type, "Unknown");
//    }
//    
//    // 默认值
//    diskInfo->partition_count = 1;
//    diskInfo->is_readonly = 0;
//    strcpy(diskInfo->file_system, "Unknown");
//    
//    char debug_info[256];
//    sprintf(debug_info, "✅ File: %s, Size: %lld bytes (%d MB), Format: %s\n",
//            filePath, diskInfo->size_bytes, diskInfo->size_mb, diskInfo->format_type);
//    OutputDebugStringA(debug_info);
//    
//    return 0;
//}
