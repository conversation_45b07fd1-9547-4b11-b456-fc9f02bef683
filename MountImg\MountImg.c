#define _WIN32_WINNT 0x0601
#define OEMRESOURCE
#include <windows.h>
#include <shlobj.h>
#include <shlwapi.h>
#include <wtsapi32.h>
#include <stdio.h>
#include <locale.h>
#include <winternl.h>
// #include <intrin.h>  // 不再需要，已使用Windows API替代_rdtsc
// #pragma intrinsic(_rdtsc)
#include "resource.h"
#include "..\inc\imdisk.h"

// GetTickCount64运行时检测支持
typedef ULONGLONG (WINAPI *PGetTickCount64)(void);
static PGetTickCount64 g_pGetTickCount64 = NULL;
static BOOL g_bTickCountInitialized = FALSE;

static const char* GetImDiskStartServiceErrorDescription(DWORD errorCode);

// 初始化GetTickCount64函数指针
static void InitTickCountFunction(void)
{
	if (!g_bTickCountInitialized) {
		HMODULE hKernel32 = GetModuleHandle(L"kernel32.dll");
		if (hKernel32) {
			g_pGetTickCount64 = (PGetTickCount64)GetProcAddress(hKernel32, "GetTickCount64");
		}
		g_bTickCountInitialized = TRUE;
	}
}

// 兼容的GetTickCount函数，自动选择64位或32位版本
static __int64 GetTickCount_Compatible(void)
{
	InitTickCountFunction();

	if (g_pGetTickCount64) {
		// Vista+系统：使用64位版本
		return (__int64)g_pGetTickCount64();
	} else {
		// XP系统：使用32位版本
		return (__int64)GetTickCount();
	}
}

static SERVICE_STATUS_HANDLE SvcStatusHandle;
static SERVICE_STATUS SvcStatus = {SERVICE_WIN32_OWN_PROCESS, SERVICE_RUNNING, SERVICE_ACCEPT_STOP, NO_ERROR, 0, 0, 600000};
SC_HANDLE h_svc;

static HINSTANCE hinst;
static HICON hIcon;
static RECT icon_coord;
static HWND hDialog;
static OSVERSIONINFO os_ver;
static DWORD mask0 = 0, show_explorer = 1;
static HKEY registry_key;
FARPROC ImDisk_GetDeviceListEx, ImDisk_ForceRemoveDevice, ImDisk_RemoveRegistrySettings, ImDisk_NotifyShellDriveLetter;
HMODULE h_cpl = NULL;

static WCHAR dev_list[] = {'h', 'c', 'f'};
static WCHAR ro_list[] = {'w', 'o'};
static WCHAR *ro_discutils_list[] = {L"", L" /readonly"};
static WCHAR *rm_list[] = {L"fix", L"rem"};
static WCHAR *boot_list[] = {L"", L" -P"};
static DWORD floppy_size[] = {160 << 10, 180 << 10, 320 << 10, 360 << 10, 640 << 10, 1200 << 10, 720 << 10, 820 << 10, 1440 << 10, 1680 << 10, 1722 << 10, 2880 << 10, 123264 << 10, 234752 << 10};

WCHAR filename[MAX_PATH] = { L"" }, mountdir[MAX_PATH] = { L"" };
WCHAR drive_list[27][4] = { L"" }, drive[MAX_PATH + 2] = { L"" };
static WCHAR module_name[MAX_PATH + 16], txt[1024] = { L"" };

BOOL g_isJSON = FALSE;
int g_iCurDriveIndex = -1;

// 后台执行相关全局变量
BOOL g_operationCompleted = FALSE;  // 操作完成标志
HWND g_hiddenDialog = NULL;         // 隐藏对话框句柄

BYTE init_ok = FALSE, net_installed = FALSE, partition_changed;
UINT cmdline_dev_type = 0, dev_type, cmdline_partition = 1, partition;
static BOOL/*_Bool*/ cmdline_partition_exist = FALSE, cmdline_mount = FALSE;
BOOL mount_point = FALSE, readonly = FALSE, removable = FALSE, new_file, win_boot;
long device_number;
static WCHAR cmdline_drive_letter = 0;

static int list_partition;
static __int64 list_size;
static WCHAR list_filesystem[MAX_PATH + 1], list_label[MAX_PATH + 1];
static ULONG list_device[64000];
static int lv_itemindex = 0;
HANDLE mount_mutex;
static PROCESS_INFORMATION pi_discutilsdevio;

static TOOLINFO ti;
static HWND hTTip, h_listview;
static HWND hwnd_edit1, hwnd_combo2, hwnd_pbutton2, hwnd_pbutton3, hwnd_pbutton4, hwnd_check1, hwnd_check2, hwnd_check3, hwnd_rb3, hwnd_rb4, hwnd_rb5, hwnd_updown, hwnd_ok;
static COMBOBOXINFO combo1;
static WCHAR *lv_titles[] = {L"", L"Size", L"File System", L"Label"};
static LONG lv_width[] = {17, 38, 50, 73};


enum {
	TITLE,
	CTRL_1, CTRL_2, CTRL_3, CTRL_4, CTRL_5, CTRL_6, CTRL_7, CTRL_8, CTRL_9, CTRL_10, CTRL_11, CTRL_12, CTRL_13, CTRL_14, CTRL_15, CTRL_16, CTRL_17,
	LV_1, LV_2, LV_3,
	TTIP_1, TTIP_2, TTIP_3, TTIP_4, TTIP_5, TTIP_6, TTIP_7, TTIP_8,
	ERR_1, ERR_2, ERR_3, ERR_4, ERR_5, ERR_6,
	INV_0, INV_1, INV_2, INV_3, INV_4,
	CREA_0, CREA_1, CREA_2, CREA_3, CREA_4, CREA_5, CREA_6, CREA_7,
	UNIT_1, UNIT_2, UNIT_3, UNIT_4, UNIT_5, UNIT_6,
	NB_TXT
};
static WCHAR *t[NB_TXT] = { "" };

static void load_lang()
{
	HANDLE h;
	LARGE_INTEGER size;
	WCHAR *buf, *current_str;
	DWORD dw;
	int i;

	if ((h = CreateFile(L"lang.txt", GENERIC_READ, FILE_SHARE_READ | FILE_SHARE_WRITE, NULL, OPEN_EXISTING, 0, NULL)) == INVALID_HANDLE_VALUE) return;
	GetFileSizeEx(h, &size);
	if (size.QuadPart > 1 << 17) {
		CloseHandle(h);
		return;
	}
	buf = VirtualAlloc(NULL, size.QuadPart + sizeof(WCHAR), MEM_COMMIT | MEM_RESERVE, PAGE_READWRITE);
	ReadFile(h, buf, size.LowPart, &dw, NULL);
	CloseHandle(h);
	if (!(current_str = wcsstr(buf, L"[MountImg]"))) return;
	wchar_t *context = NULL;
	wcstok(current_str, L"\r\n", &context);
	for (i = 0; i < NB_TXT; i++)
		t[i] = wcstok(NULL, L"\r\n", &context);
	size.LowPart /= sizeof(WCHAR);
	for (i = 0; i < size.LowPart; i++)
		if (buf[i] == '#') buf[i] = '\n';
	setlocale(LC_ALL, "");
}


DWORD start_process(WCHAR *cmd, BYTE flag)
{
	STARTUPINFO si = {sizeof si};
	PROCESS_INFORMATION pi;
	TOKEN_LINKED_TOKEN lt = { 0 };
	HANDLE token;
	DWORD dw;
	BOOL result;

	if (flag == 2 && (dw = WTSGetActiveConsoleSessionId()) != -1 && WTSQueryUserToken(dw, &token)) {
		if (!GetTokenInformation(token, TokenLinkedToken, &lt, sizeof lt, &dw) ||
			!(result = CreateProcessAsUser(lt.LinkedToken, NULL, cmd, NULL, NULL, FALSE, CREATE_NO_WINDOW, NULL, NULL, &si, &pi)))
			result = CreateProcessAsUser(token, NULL, cmd, NULL, NULL, FALSE, CREATE_NO_WINDOW, NULL, NULL, &si, &pi);
		CloseHandle(token);
		CloseHandle(lt.LinkedToken);
	} else
		result = CreateProcess(NULL, cmd, NULL, NULL, FALSE, CREATE_NO_WINDOW, NULL, NULL, &si, &pi);

	dw = 1;
	if (result) {
		if (flag) {
			WaitForSingleObject(pi.hProcess, INFINITE);
			GetExitCodeProcess(pi.hProcess, &dw);
		}
		CloseHandle(pi.hProcess);
		CloseHandle(pi.hThread);
	}
	return (dw != 0);
}


static void wait_discutilsdevio()
{
	if (pi_discutilsdevio.hProcess == INVALID_HANDLE_VALUE) return;
	CloseHandle(pi_discutilsdevio.hThread);
	WaitForSingleObject(pi_discutilsdevio.hProcess, INFINITE);
	CloseHandle(pi_discutilsdevio.hProcess);
	pi_discutilsdevio.hProcess = INVALID_HANDLE_VALUE;
}

static long get_imdisk_unit()
{
	long i, j;

	// 安全检查：确保 ImDisk_GetDeviceListEx 函数指针有效
	if (!ImDisk_GetDeviceListEx) {
		// 函数指针为空，可能是 imdisk.cpl 加载失败
		return -1;
	}

	// 安全检查：确保 list_device 数组有效
	if (!list_device) {
		return -1;
	}

	// 调用 ImDisk API 获取设备列表
	if (!ImDisk_GetDeviceListEx(_countof(list_device), list_device)) {
		// API 调用失败
		return -1;
	}

	i = j = 0;
	while (++j <= list_device[0])
		if (list_device[j] == i) { j = 0; i++; }
	return i;
}

static BOOL get_volume_param(int list_unit)
{
	WCHAR volume[24];
	HANDLE h;
	DWORD dw;
	BOOL fs_ok;

	_snwprintf(volume, _countof(volume), L"\\\\?\\ImDisk%d", list_unit);
	list_size = -1;
	h = CreateFile(volume, GENERIC_READ, FILE_SHARE_READ | FILE_SHARE_WRITE, NULL, OPEN_EXISTING, 0, NULL);
	DeviceIoControl(h, IOCTL_DISK_GET_LENGTH_INFO, NULL, 0, &list_size, sizeof list_size, &dw, NULL);
	wcscat(volume, L"\\");
	list_filesystem[0] = list_label[0] = 0;
	fs_ok = GetVolumeInformation(volume, list_label, _countof(list_label), NULL, NULL, NULL, list_filesystem, _countof(list_filesystem));
	if (!DeviceIoControl(h, FSCTL_DISMOUNT_VOLUME, NULL, 0, NULL, 0, &dw, NULL) || !DeviceIoControl(h, IOCTL_STORAGE_EJECT_MEDIA, NULL, 0, NULL, 0, &dw, NULL))
		ImDisk_ForceRemoveDevice(h, 0);
	CloseHandle(h);

	return fs_ok;
}

static BOOL imdisk_check()
{
	long list_unit;
	WCHAR cmdline[MAX_PATH + 80], txt_partition[16];
	BYTE retry = 0;
	BOOL fs_ok = FALSE;

	_snwprintf(txt_partition, _countof(txt_partition), list_partition > 1 ? L" -v %d" : L"", list_partition);
	do {
		if ((list_unit = get_imdisk_unit()) < 0) return FALSE;
		_snwprintf(cmdline, _countof(cmdline), L"imdisk -a -u %d -o %cd,ro,%s -f \"%s\"%s%s", list_unit, dev_list[dev_type], rm_list[removable], filename, retry ? L"" : L" -b auto", txt_partition);
		if (start_process(cmdline, TRUE)) break;
		fs_ok = get_volume_param(list_unit);
	} while (!fs_ok && ++retry < 2);

	return fs_ok;
}

static BOOL discutils_check()
{
	STARTUPINFO si = {sizeof si};
	DWORD ExitCode;
	long list_unit;
	WCHAR cmdline[MAX_PATH + 100], txt_partition[24];
	__int64 pipe;
	int i;

	list_size = -1;
	pipe = (GetTickCount_Compatible() << 16) | (GetCurrentProcessId() & 0xFFFF);
	_snwprintf(txt_partition, _countof(txt_partition), list_partition != 1 ? L" /partition=%d" : L"", list_partition);
	_snwprintf(cmdline, _countof(cmdline), L"DiscUtilsDevio /name=ImDisk%I64x%s /filename=\"%s\" /readonly", pipe, txt_partition, filename);
	if (!CreateProcess(NULL, cmdline, NULL, NULL, FALSE, CREATE_NO_WINDOW, NULL, NULL, &si, &pi_discutilsdevio)) return FALSE;
	if ((list_unit = get_imdisk_unit()) < 0) return FALSE;
	_snwprintf(cmdline, _countof(cmdline), L"imdisk -a -t proxy -u %d -o shm,%cd,ro,%s -f ImDisk%I64x", list_unit, dev_list[dev_type], rm_list[removable], pipe);
	for (i = 0; i < 100; i++) {
		Sleep(50);
		// check if DiscUtilsDevio is still active
		GetExitCodeProcess(pi_discutilsdevio.hProcess, &ExitCode);
		if (ExitCode != STILL_ACTIVE) break;
		if (!start_process(cmdline, TRUE)) {
			get_volume_param(list_unit);
			break;
		}
	}
	return list_size != -1;
}

static DWORD __stdcall list_thread(LPVOID lpParam)
{
	LVITEM lv_item;
	BOOL volume_ok, discutils_pref = FALSE;
	WCHAR text[16];
	__int64 max_list_size = 0;
	int max_partition = 1;
	double d;
	int i;

	partition_changed = FALSE;
	lv_item.mask = LVIF_TEXT;
	lv_item.iItem = list_partition = 0;
	for (;;) {
		WaitForSingleObject(mount_mutex, INFINITE);
		if (list_partition != lv_item.iItem) break;
		if (++list_partition > 128) break;
		if (discutils_pref) {
			volume_ok = discutils_check();
			if (!volume_ok) volume_ok = imdisk_check();
		} else {
			volume_ok = imdisk_check();
			if (!volume_ok && net_installed) discutils_pref = volume_ok = discutils_check();
		}
		if (!volume_ok) {
			if (cmdline_partition_exist)
				cmdline_partition_exist = FALSE;
			else if (!partition_changed) {
				lv_itemindex = max_partition - 1;
				ListView_SetItemState(h_listview, lv_itemindex, LVIS_SELECTED, LVIS_SELECTED);
				ListView_EnsureVisible(h_listview, lv_itemindex, FALSE);
			}
			break;
		}
		lv_item.iSubItem = 0;
		lv_item.pszText = text;
		_snwprintf(text, _countof(text), L"%u", list_partition);
		if (list_partition - 1 != lv_item.iItem) break;
		ListView_InsertItem(h_listview, &lv_item);
		lv_item.iSubItem = 1;
		d = list_size;
		i = UNIT_1;
		while (d >= 999.5) d /= 1024, i++;
		_snwprintf(text, _countof(text) - 1, L"%.3g %s", d, t[i]);
		text[_countof(text) - 1] = 0;
		ListView_SetItem(h_listview, &lv_item);
		lv_item.iSubItem = 2;
		lv_item.pszText = list_filesystem;
		ListView_SetItem(h_listview, &lv_item);
		lv_item.iSubItem = 3;
		lv_item.pszText = list_label;
		ListView_SetItem(h_listview, &lv_item);
		lv_item.iItem++;
		if (list_size > max_list_size && list_filesystem[0]) {
			max_list_size = list_size;
			max_partition = list_partition;
		}
		wait_discutilsdevio();
		ReleaseMutex(mount_mutex);
	}
	wait_discutilsdevio();
	return 0;
}

/*
 * JSON 解析函数
 * 解析 JSON 字符串中的指定键值
 */
static BOOL parseJsonValue(const WCHAR* json, const WCHAR* key, WCHAR* output, size_t outputSize, BOOL* isBool, int* intValue)
{
	WCHAR searchKey1[256], searchKey2[256];
	swprintf_s(searchKey1, 256, L"\"%s\"", key);  // 标准格式: "key"
	swprintf_s(searchKey2, 256, L"%s", key);      // 无引号格式: key

	WCHAR* keyPos = wcsstr(json, searchKey1);
	if (!keyPos) {
		// 尝试无引号格式
		keyPos = wcsstr(json, searchKey2);
		if (!keyPos) return FALSE;
	}

	// 查找冒号
	WCHAR* colonPos = wcschr(keyPos, L':');
	if (!colonPos) return FALSE;

	// 跳过空格
	colonPos++;
	while (*colonPos == L' ' || *colonPos == L'\t') colonPos++;

	// 判断值类型并解析
	if (*colonPos == L'"') {
		// 标准字符串值 (带引号)
		colonPos++; // 跳过开始引号
		WCHAR* endQuote = wcschr(colonPos, L'"');
		if (!endQuote) return FALSE;

		size_t len = endQuote - colonPos;
		if (output && len < outputSize) {
			wcsncpy_s(output, outputSize, colonPos, len);
			output[len] = L'\0';
		}
		return TRUE;
	}
	else if (wcsncmp(colonPos, L"true", 4) == 0) {
		// 布尔值 true
		if (isBool) *isBool = TRUE;
		return TRUE;
	}
	else if (wcsncmp(colonPos, L"false", 5) == 0) {
		// 布尔值 false
		if (isBool) *isBool = FALSE;
		return TRUE;
	}
	else if (iswdigit(*colonPos) || *colonPos == L'-') {
		// 数字值
		if (intValue) *intValue = _wtoi(colonPos);
		return TRUE;
	}
	else if (output) {
		// 无引号字符串值 (兼容模式)
		WCHAR* endPos = colonPos;
		// 查找字符串结束位置 (逗号、右大括号或字符串末尾)
		while (*endPos && *endPos != L',' && *endPos != L'}' && *endPos != L'\r' && *endPos != L'\n') {
			endPos++;
		}

		// 移除尾部空格
		while (endPos > colonPos && (*(endPos-1) == L' ' || *(endPos-1) == L'\t')) {
			endPos--;
		}

		size_t len = endPos - colonPos;
		if (len > 0 && len < outputSize) {
			wcsncpy_s(output, outputSize, colonPos, len);
			output[len] = L'\0';
			return TRUE;
		}
	}

	return FALSE;
}

static void file_check()
{
	WCHAR *ext;
	WIN32_FILE_ATTRIBUTE_DATA file_data;
	BOOL/*_Bool*/ floppy_ok = FALSE;
	BOOL file_exist;
	int i;

	ext = PathFindExtension(filename);
	dev_type = !_wcsicmp(ext, L".iso") || !_wcsicmp(ext, L".nrg") || !_wcsicmp(ext, L".bin");
	CheckRadioButton(hDialog, ID_RB3, ID_RB5, ID_RB3 + dev_type);

	if ((file_exist = GetFileAttributesEx(filename, GetFileExInfoStandard, &file_data)) && !file_data.nFileSizeHigh)
		for (i = 0; i < _countof(floppy_size); i++)
			if (floppy_size[i] == file_data.nFileSizeLow) floppy_ok = TRUE;
	if (floppy_ok || !_wcsicmp(ext, L".vfd")) {
		dev_type = 2;
		CheckRadioButton(hDialog, ID_RB3, ID_RB5, ID_RB5);
		if (drive_list[0][0] == 'A' || drive_list[0][0] == 'B')
			SendMessage(hwnd_combo2, CB_SETCURSEL, 0, 0);
	}

	ListView_DeleteAllItems(h_listview);
	if (!cmdline_mount && file_exist && !(file_data.dwFileAttributes & FILE_ATTRIBUTE_DIRECTORY))
		CreateThread(NULL, 0, list_thread, NULL, 0, NULL);
}

static int get_reg_max()
{
	int max = -1;
	DWORD data_size = sizeof max;

	RegQueryValueEx(registry_key, L"ImMaxReg", NULL, NULL, (void*)&max, &data_size);
	return max;
}

static int reg_remove()
{
	WCHAR param_name[16], *param_name_ptr, reg_filename[MAX_PATH];
	DWORD data_size, device;
	int i, first_free, last_valid, max;

	first_free = last_valid = -1;
	max = get_reg_max();
	for (i = 0;; i++) {
		param_name_ptr = param_name + _snwprintf(param_name, _countof(param_name), L"Im%d", i);
		wcscpy(param_name_ptr, L"FileName");
		data_size = sizeof reg_filename;
		if (RegQueryValueEx(registry_key, param_name, NULL, NULL, (void*)reg_filename, &data_size) == ERROR_SUCCESS) {
			if (wcscmp(reg_filename, filename))
				last_valid = i;
			else {
				RegDeleteValue(registry_key, param_name);
				wcscpy(param_name_ptr, L"MountPoint");
				RegDeleteValue(registry_key, param_name);
				wcscpy(param_name_ptr, L"Param");
				RegDeleteValue(registry_key, param_name);
				wcscpy(param_name_ptr, L"Device");
				data_size = sizeof device;
				if (RegQueryValueEx(registry_key, param_name, NULL, NULL, (void*)&device, &data_size) == ERROR_SUCCESS)
					ImDisk_RemoveRegistrySettings(device);
				RegDeleteValue(registry_key, param_name);
				if (first_free == -1) first_free = i;
			}
		} else {
			if (first_free == -1) first_free = i;
			if (i > max) break;
		}
	}
	RegSetValueEx(registry_key, L"ImMaxReg", 0, REG_DWORD, (void*)&last_valid, sizeof last_valid);

	return first_free;
}

void reg_save()
{
	WCHAR param_name[16], *param_name_ptr;
	ULARGE_INTEGER param;
	int i, max;

	i = reg_remove();
	max = get_reg_max();
	if (i > max) RegSetValueEx(registry_key, L"ImMaxReg", 0, REG_DWORD, (void*)&i, sizeof i);
	param_name_ptr = param_name + _snwprintf(param_name, _countof(param_name), L"Im%d", i);
	wcscpy(param_name_ptr, L"FileName");
	RegSetValueEx(registry_key, param_name, 0, REG_SZ, (void*)filename, (wcslen(filename) + 1) * sizeof(WCHAR));
	wcscpy(param_name_ptr, L"MountPoint");
	RegSetValueEx(registry_key, param_name, 0, REG_SZ, (void*)drive, (wcslen(drive) + 1) * sizeof(WCHAR));
	param.HighPart = partition;
	param.LowPart = (dev_type << 16) + (removable << 1) + readonly;
	wcscpy(param_name_ptr, L"Param");
	RegSetValueEx(registry_key, param_name, 0, REG_QWORD, (void*)&param, sizeof param);
	if (device_number >= 0) {
		wcscpy(param_name_ptr, L"Device");
		RegSetValueEx(registry_key, param_name, 0, REG_DWORD, (void*)&device_number, sizeof device_number);
	}
}

static void disp_controls()
{
	ShowWindow(hwnd_combo2, !mount_point);
	ShowWindow(hwnd_edit1, mount_point);
	ShowWindow(hwnd_pbutton2, mount_point);
	EnableWindow(hwnd_ok, filename[0] && (!mount_point || mountdir[0]));
}

static DWORD __stdcall UnmountDrive(LPVOID lpParam)
{
	WCHAR cmd_line[MAX_PATH + 20];

	_snwprintf(cmd_line, _countof(cmd_line), L"ImDisk-Dlg RM \"%s\"", lpParam);
	start_process(cmd_line, TRUE);
	init_ok = TRUE;
	mount_point = IsDlgButtonChecked(hDialog, ID_RB2);
	disp_controls();
	EnableWindow(hwnd_pbutton3, TRUE);
	ListView_DeleteAllItems(h_listview);
	if (PathFileExists(filename) && !PathIsDirectory(filename))
		list_thread(NULL);
	return 0;
}

static void draw_icon(HWND hDlg)
{
	PAINTSTRUCT paint;

	DrawIcon(BeginPaint(hDlg, &paint), icon_coord.left, icon_coord.top, hIcon);
	EndPaint(hDlg, &paint);
}

static INT_PTR __stdcall CreateFile_Proc(HWND hDlg, UINT Msg, WPARAM wParam, LPARAM lParam)
{
	HANDLE h;
	WCHAR txt_size[18];
	__int64 size;
	int i, unit;

	switch (Msg)
	{
		case WM_INITDIALOG:
			if (t[0]) {
				SetWindowText(hDlg, t[CREA_0]);
				SetDlgItemText(hDlg, ID_TEXT1, t[CREA_1]);
				SetDlgItemText(hDlg, ID_TEXT2, t[CREA_2]);
				SetDlgItemText(hDlg, ID_CHECK21, t[CREA_3]);
				SetDlgItemText(hDlg, IDOK, t[CREA_4]);
				SetDlgItemText(hDlg, IDCANCEL, t[CREA_5]);
				SetDlgItemText(hDlg, ID_RB21, t[UNIT_1]);
				SetDlgItemText(hDlg, ID_RB22, t[UNIT_2]);
				SetDlgItemText(hDlg, ID_RB23, t[UNIT_3]);
				SetDlgItemText(hDlg, ID_RB24, t[UNIT_4]);
			}
			hIcon = LoadImage(NULL, MAKEINTRESOURCE(OIC_QUES), IMAGE_ICON, 0, 0, LR_DEFAULTSIZE | LR_SHARED);
			icon_coord.left = 13;
			icon_coord.top = 11;
			icon_coord.right = 0;
			icon_coord.bottom = 0;
			MapDialogRect(hDlg, &icon_coord);

			CheckRadioButton(hDlg, ID_RB21, ID_RB24, ID_RB21);
			CheckDlgButton(hDlg, ID_CHECK21, !mount_point);

			return TRUE;

		case WM_PAINT:
			draw_icon(hDlg);
			return TRUE;

		case WM_COMMAND:
			unit = 0;
			for (i = 0; i < 4; i++)
				if (IsDlgButtonChecked(hDlg, ID_RB21 + i)) unit = 10 * i;

			GetDlgItemText(hDlg, ID_EDIT21, txt_size, _countof(txt_size));
			size = min(_wtoi64(txt_size), _I64_MAX >> unit) << unit;
			EnableWindow(GetDlgItem(hDlg, IDOK), size >= 65537);

			if (LOWORD(wParam) == IDOK) {
				if ((h = CreateFile(filename, GENERIC_WRITE, 0, NULL, CREATE_NEW, FILE_ATTRIBUTE_NORMAL, NULL)) == INVALID_HANDLE_VALUE) {
					MessageBox(hDlg, t[CREA_6], L"ImDisk", MB_ICONERROR);
					return TRUE;
				}
				//SetFilePointerEx(h, (LARGE_INTEGER)size, NULL, FILE_BEGIN
				LARGE_INTEGER liSize;
				liSize.QuadPart = size;
				SetFilePointerEx(h, liSize, NULL, FILE_BEGIN);
				if (!SetEndOfFile(h)) {
					CloseHandle(h);
					DeleteFile(filename);
					MessageBox(hDlg, t[CREA_7], L"ImDisk", MB_ICONERROR);
					return TRUE;
				}
				CloseHandle(h);
				new_file = TRUE;
				if (!(i = IsDlgButtonChecked(hDlg, ID_CHECK21))) file_check();
				EndDialog(hDlg, i);
			}

			if (LOWORD(wParam) == IDCANCEL)
				EndDialog(hDlg, FALSE);

			return TRUE;

		default:
			return FALSE;
	}
}

static DWORD __stdcall invalid_fs_unmount(LPVOID lpParam)
{
	WCHAR cmdline[MAX_PATH + 20];

	_snwprintf(cmdline, _countof(cmdline), L"ImDisk-Dlg RM \"%s\"", drive);
	start_process(cmdline, TRUE);
	EndDialog(lpParam, 0);
	return 0;
}

static INT_PTR __stdcall InvalidFS_Proc(HWND hDlg, UINT Msg, WPARAM wParam, LPARAM lParam)
{
	switch (Msg)
	{
		case WM_INITDIALOG:
			if (t[0]) {
				SetWindowText(hDlg, t[INV_0]);
				SetDlgItemText(hDlg, ID_TEXT1, t[INV_1]);
				SetDlgItemText(hDlg, ID_PBUTTON11, t[INV_2]);
				SetDlgItemText(hDlg, ID_PBUTTON12, t[INV_3]);
				SetDlgItemText(hDlg, ID_PBUTTON13, t[INV_4]);
			}
			hIcon = LoadImage(NULL, MAKEINTRESOURCE(OIC_WARNING), IMAGE_ICON, 0, 0, LR_DEFAULTSIZE | LR_SHARED);
			icon_coord.left = 14;
			icon_coord.top = 18;
			icon_coord.right = 0;
			icon_coord.bottom = 0;
			MapDialogRect(hDlg, &icon_coord);
			EnableWindow(GetDlgItem(hDlg, ID_PBUTTON12), !mount_point);
			if (!mount_point) SetTimer(hDlg, 1, 1000, NULL);
			MessageBeep(MB_ICONWARNING);
			return TRUE;

		case WM_PAINT:
			draw_icon(hDlg);
			return TRUE;

		case WM_TIMER:
			if (GetVolumeInformation(drive, NULL, 0, NULL, NULL, NULL, NULL, 0)) {
				EndDialog(hDlg, 0);
				EndDialog(hDialog, 0);
			}
			if (GetLastError() == ERROR_PATH_NOT_FOUND) {
				reg_remove();
				EndDialog(hDlg, 0);
			}
			return TRUE;

		case WM_COMMAND:
			if (LOWORD(wParam) == ID_PBUTTON11) {
				EnableWindow(GetDlgItem(hDlg, ID_PBUTTON11), FALSE);
				EnableWindow(GetDlgItem(hDlg, ID_PBUTTON12), FALSE);
				CreateThread(NULL, 0, invalid_fs_unmount, hDlg, 0, NULL);
				reg_remove();
			}

			if (LOWORD(wParam) == ID_PBUTTON12) {
				ShowWindow(hDlg, SW_HIDE);
				ShowWindow(hDialog, SW_HIDE);
				SHFormatDrive(hDialog, drive[0] - 'A', SHFMT_ID_DEFAULT, SHFMT_OPT_FULL);
				EndDialog(hDlg, 0);
				EndDialog(hDialog, 0);
			}

			if (LOWORD(wParam) == ID_PBUTTON13) {
				EndDialog(hDlg, 0);
				EndDialog(hDialog, 0);
			}

			return TRUE;

		default:
			return FALSE;
	}
}


int Imdisk_Mount(BYTE no_check_fs)
{
	WCHAR cmdline[MAX_PATH * 2 + 80], txt_partition[16];
	BOOL fs_ok = FALSE;
	BYTE retry = !partition;

	_snwprintf(txt_partition, _countof(txt_partition), partition > 1 ? L" -v %d" : L"", partition);
	do {
		if ((device_number = get_imdisk_unit()) < 0) return 1;
		_snwprintf(cmdline, _countof(cmdline), L"imdisk -a -u %d -o %cd,ro,%s -f \"%s\"%s%s", device_number, dev_list[dev_type], rm_list[removable], filename, retry ? L"" : L" -b auto", txt_partition);
		if (start_process(cmdline, TRUE)) return 1;
		_snwprintf(cmdline, _countof(cmdline), L"\\\\?\\ImDisk%d\\", device_number);
		fs_ok = GetVolumeInformation(cmdline, NULL, 0, NULL, NULL, NULL, NULL, 0);
		_snwprintf(cmdline, _countof(cmdline), L"imdisk -D -u %d", device_number);
		start_process(cmdline, TRUE);
	} while (!fs_ok && ++retry < 2);
	if (fs_ok || no_check_fs) {
		if ((device_number = get_imdisk_unit()) < 0) return 1;
		_snwprintf(cmdline, _countof(cmdline), L"imdisk -a -u %d -m \"%s\" -o %cd,r%c,%s -f \"%s\"%s%s%s",
				 device_number, drive, dev_list[dev_type], ro_list[readonly], rm_list[removable], filename, retry ? L"" : L" -b auto", txt_partition, boot_list[win_boot]);
		return start_process(cmdline, TRUE);
	} else return 1;
}

int DiscUtils_Mount()
{
	WCHAR cmdline1[MAX_PATH + 70], cmdline2[MAX_PATH + 70], txt_partition[24];
	WCHAR *cmdline_ptr[3] = {cmdline1, cmdline2, drive};
	HANDLE h;
	int error;
	__int64 pipe;

	// 检查 h_svc 是否有效
	if (!h_svc) {
		// h_svc 为空，DiscUtils 挂载无法进行
		return 1;
	}

	pipe = (GetTickCount_Compatible() << 16) | (GetCurrentProcessId() & 0xFFFF);
	_snwprintf(txt_partition, _countof(txt_partition), partition != 1 ? L" /partition=%d" : L"", partition);
	_snwprintf(cmdline1, _countof(cmdline1), L"/name=ImDisk%I64x%s /filename=\"%s\"%s", pipe, txt_partition, filename, ro_discutils_list[readonly]);
	_snwprintf(cmdline2, _countof(cmdline2), L"-o shm,%cd,r%c,%s -f ImDisk%I64x", dev_list[dev_type], ro_list[readonly], rm_list[removable], pipe);
	h = CreateSemaphoreA(NULL, 0, 2, "Global\\MountImgSvcSema");

	// 尝试启动服务
	if (!StartService(h_svc, 3, (void*)cmdline_ptr)) {
		// StartService 失败，获取详细错误信息
		DWORD lastError = GetLastError();
		WCHAR errorMsg[512];

		// 获取系统错误消息
		if (FormatMessageW(FORMAT_MESSAGE_FROM_SYSTEM | FORMAT_MESSAGE_IGNORE_INSERTS,
						   NULL, lastError, MAKELANGID(LANG_NEUTRAL, SUBLANG_DEFAULT),
						   errorMsg, _countof(errorMsg), NULL)) {
			// 转换为多字节字符串用于调试输出
			char debugMsg[1024];
			WideCharToMultiByte(CP_UTF8, 0, errorMsg, -1, debugMsg, sizeof(debugMsg), NULL, NULL);

			// 输出详细错误信息
			char fullMsg[2048];
			sprintf(fullMsg, "ERROR: StartService failed\n"
							 "  Error Code: %d (0x%08X)\n"
							 "  System Message: %s"
							 "  Error Description: %s\n"
							 "  Service Handle: 0x%p\n"
							 "  Arguments Count: 3\n"
							 "  Command Line 1: %S\n"
							 "  Command Line 2: %S\n"
							 "  Drive: %S\n",
					lastError, lastError, debugMsg,
					GetImDiskStartServiceErrorDescription(lastError), h_svc,
					cmdline_ptr[0], cmdline_ptr[1], cmdline_ptr[2]);
			OutputDebugStringA(fullMsg);
		} else {
			// 无法获取错误消息，输出基本信息
			char basicMsg[512];
			sprintf(basicMsg, "ERROR: StartService failed\n"
							  "  Error Code: %d (0x%08X)\n"
							  "  Service Handle: 0x%p\n"
							  "  Unable to get error message (FormatMessage failed: %d)\n",
					lastError, lastError, h_svc, GetLastError());
			OutputDebugStringA(basicMsg);
		}

		CloseHandle(h);
		return 1;
	}
	error = WaitForSingleObject(h, 15000) != WAIT_OBJECT_0 || WaitForSingleObject(h, 0) == WAIT_OBJECT_0;
	CloseHandle(h);
	if (!error && !mount_point)
		ImDisk_NotifyShellDriveLetter(NULL, drive);

	return error;
}

/*
 * 错误描述函数类型定义
 */
typedef const char* (*ErrorDescriptionFunc)(DWORD);

/*
 * 错误描述宏定义 (备选方案)
 */
#define GET_ERROR_DESC_ACCESS_DENIED "Access denied. The service requires administrator privileges."
#define GET_ERROR_DESC_INVALID_HANDLE "Invalid service handle. The service may not exist or handle is corrupted."
#define GET_ERROR_DESC_SERVICE_RUNNING "Service is already running."
#define GET_ERROR_DESC_SERVICE_DISABLED "Service is disabled and cannot be started."
#define GET_ERROR_DESC_SERVICE_NOT_EXIST "Service does not exist in the system."
#define GET_ERROR_DESC_PATH_NOT_FOUND "Service executable path not found."
#define GET_ERROR_DESC_UNKNOWN "Unknown error. Check Windows Event Log for more details."

/*
 * 获取 StartService 错误的详细说明
 */
static const char* GetImDiskStartServiceErrorDescription(DWORD errorCode)
{
	switch (errorCode) {
		case ERROR_ACCESS_DENIED:
			return "Access denied. The service requires administrator privileges.";
		case ERROR_INVALID_HANDLE:
			return "Invalid service handle. The service may not exist or handle is corrupted.";
		case ERROR_INVALID_PARAMETER:
			return "Invalid parameter. Check service arguments.";
		case ERROR_SERVICE_ALREADY_RUNNING:
			return "Service is already running.";
		case ERROR_SERVICE_DISABLED:
			return "Service is disabled and cannot be started.";
		case ERROR_SERVICE_DOES_NOT_EXIST:
			return "Service does not exist in the system.";
		case ERROR_SERVICE_LOGON_FAILED:
			return "Service logon failed. Check service account credentials.";
		case ERROR_SERVICE_MARKED_FOR_DELETE:
			return "Service is marked for deletion and cannot be started.";
		case ERROR_SERVICE_NO_THREAD:
			return "Service could not create a thread.";
		case ERROR_SERVICE_REQUEST_TIMEOUT:
			return "Service request timed out.";
		case ERROR_PATH_NOT_FOUND:
			return "Service executable path not found.";
		case ERROR_FILE_NOT_FOUND:
			return "Service executable file not found.";
		case ERROR_INSUFFICIENT_BUFFER:
			return "Insufficient buffer for service arguments.";
		case ERROR_INVALID_SERVICE_CONTROL:
			return "Invalid service control request.";
		case ERROR_SERVICE_CANNOT_ACCEPT_CTRL:
			return "Service cannot accept control requests at this time.";
		default:
			return "Unknown error. Check Windows Event Log for more details.";
	}
	return "Unknown error. Check Windows Event Log for more details.";
}

/*
 * 初始化 ImDisk 相关资源
 * 从 wWinMain 中提取的初始化代码
 */
int InitializeImDisk(void)
{
	static BOOL initialized = FALSE;

	// 避免重复初始化
	if (initialized) {
		return 0;
	}

	// 加载 imdisk.cpl 库
	if (!(h_cpl = LoadLibraryA("imdisk.cpl"))) {
		// 无法加载 imdisk.cpl，可能是 ImDisk 未安装
		return 1;
	}

	// 获取 ImDisk API 函数指针
	ImDisk_GetDeviceListEx = GetProcAddress(h_cpl, "ImDiskGetDeviceListEx");
	ImDisk_ForceRemoveDevice = GetProcAddress(h_cpl, "ImDiskForceRemoveDevice");
	ImDisk_RemoveRegistrySettings = GetProcAddress(h_cpl, "ImDiskRemoveRegistrySettings");
	ImDisk_NotifyShellDriveLetter = GetProcAddress(h_cpl, "ImDiskNotifyShellDriveLetter");

	// 检查关键函数是否成功获取
	if (!ImDisk_GetDeviceListEx) {
		FreeLibrary(h_cpl);
		h_cpl = NULL;
		return 1;
	}

	// 创建挂载互斥锁
	if (!mount_mutex) {
		mount_mutex = CreateMutex(NULL, FALSE, L"ImDiskMountMutex");
	}

	// 检查 .NET Framework 是否安装（参考 wWinMain 第1634-1637行）
	HKEY h_key;
	if (RegOpenKeyExA(HKEY_LOCAL_MACHINE, "SOFTWARE\\Microsoft\\.NETFramework\\v4.0.30319", 0, KEY_QUERY_VALUE | KEY_WOW64_64KEY, &h_key) == ERROR_SUCCESS) {
		RegCloseKey(h_key);
		net_installed = TRUE;
	} else {
		net_installed = FALSE;
	}

	//WCHAR module_name[MAX_PATH] = L"E:\\Work\\002_Project\\005_VirtualDiskMount_ProjectAll\\001_Code\\005_VirtualDiskMount_imdisktk\\001_imdisktk_source_2020.11.20\\MountImg\\Debug\\MountImg32.exe";
	//if (GetModuleFileNameW(NULL, module_name, MAX_PATH) > 0) {
	//	wcscat(module_name, L" /SVC");
	//}

	if (GetModuleFileNameW(NULL, module_name, MAX_PATH) > 0)
	{
		PathRemoveFileSpecW(module_name); // 变成  D:\Path
		wcscat(module_name, L"\\MountImg32.exe /SVC");
	}

	// 初始化 ImDisk 服务（参考 wWinMain 第2083-2094行）
	SC_HANDLE h_scman = OpenSCManager(NULL, NULL, SC_MANAGER_CONNECT | SC_MANAGER_CREATE_SERVICE);
	if (h_scman) {
		// 尝试打开现有服务
		h_svc = OpenService(h_scman, L"ImDiskImg", SERVICE_CHANGE_CONFIG | SERVICE_START | SERVICE_STOP | DELETE);

		if (h_svc) {
			ChangeServiceConfig(h_svc, SERVICE_WIN32_OWN_PROCESS, SERVICE_NO_CHANGE, SERVICE_ERROR_NORMAL, module_name, NULL, NULL, L"ImDisk\0", NULL, NULL, NULL);
			// 服务存在，查询服务状态
			SERVICE_STATUS serviceStatus;
			if (QueryServiceStatus(h_svc, &serviceStatus)) {
				const char* stateStr = "UNKNOWN";
				switch (serviceStatus.dwCurrentState) {
					case SERVICE_STOPPED: stateStr = "STOPPED"; break;
					case SERVICE_START_PENDING: stateStr = "START_PENDING"; break;
					case SERVICE_STOP_PENDING: stateStr = "STOP_PENDING"; break;
					case SERVICE_RUNNING: stateStr = "RUNNING"; break;
					case SERVICE_CONTINUE_PENDING: stateStr = "CONTINUE_PENDING"; break;
					case SERVICE_PAUSE_PENDING: stateStr = "PAUSE_PENDING"; break;
					case SERVICE_PAUSED: stateStr = "PAUSED"; break;
				}

				char statusMsg[256];
				sprintf(statusMsg, "ImDiskImg service found - State: %s, Type: %d, Controls: 0x%08X\n",
						stateStr, serviceStatus.dwServiceType, serviceStatus.dwControlsAccepted);
				OutputDebugStringA(statusMsg);
			} else {
				DWORD queryError = GetLastError();
				char errorMsg[256];
				sprintf(errorMsg, "************************************************************************ImDiskImg service found but QueryServiceStatus failed: %d\n", queryError);
				OutputDebugStringA(errorMsg);
				char buf[256];
				FormatMessageA(FORMAT_MESSAGE_FROM_SYSTEM, NULL, queryError,
					0, buf, 256, NULL);
				//wchar_t errorMsgW[1024];
				sprintf(errorMsg, "************************************************************************ImDiskImg service found but QueryServiceStatus failed: %s\n", buf);
				OutputDebugStringA(errorMsg);
				printf("ImDiskImg service found but QueryServiceStatus failed: %s\n", buf);
			}
		} else {
			// 服务不存在，输出错误信息并尝试创建服务
			DWORD openError = GetLastError();
			char openErrorMsg[512];
			sprintf(openErrorMsg, "ImDiskImg service not found (OpenService error: %d) - attempting to create service\n", openError);
			OutputDebugStringA(openErrorMsg);

			WCHAR module_name[MAX_PATH];
			if (GetModuleFileNameW(NULL, module_name, MAX_PATH) > 0) {
				wcscat(module_name, L" /SVC");

				char createMsg[512];
				sprintf(createMsg, "Creating ImDiskImg service with executable: %S\n", module_name);
				OutputDebugStringA(createMsg);

				h_svc = CreateService(h_scman, L"ImDiskImg", L"ImDisk Image File mounter",
									  SERVICE_CHANGE_CONFIG | SERVICE_START | SERVICE_STOP | DELETE,
									  SERVICE_WIN32_OWN_PROCESS, SERVICE_AUTO_START, SERVICE_ERROR_NORMAL,
									  module_name, NULL, NULL, L"ImDisk\0", NULL, NULL);

				if (h_svc) {
					OutputDebugStringA("✅ ImDiskImg service created successfully\n");
					SERVICE_DESCRIPTION svc_description;
					svc_description.lpDescription = L"Mounts image files at system startup.";
					ChangeServiceConfig2(h_svc, SERVICE_CONFIG_DESCRIPTION, &svc_description);
				} else {
					DWORD createError = GetLastError();
					char createErrorMsg[512];
					sprintf(createErrorMsg, "❌ Failed to create ImDiskImg service (error: %d) - %s\n",
							createError, GetImDiskStartServiceErrorDescription(createError));
					OutputDebugStringA(createErrorMsg);
				}
			} else {
				DWORD moduleError = GetLastError();
				char moduleErrorMsg[256];
				sprintf(moduleErrorMsg, "❌ Failed to get module filename (error: %d)\n", moduleError);
				OutputDebugStringA(moduleErrorMsg);
			}
		}

		CloseServiceHandle(h_scman);
	} else {
		// OpenSCManager 失败
		DWORD scmError = GetLastError();
		char scmErrorMsg[512];
		sprintf(scmErrorMsg, "❌ Failed to open Service Control Manager (error: %d) - %s\n",
				scmError, GetImDiskStartServiceErrorDescription(scmError));
		OutputDebugStringA(scmErrorMsg);
		OutputDebugStringA("  This may indicate insufficient privileges or system issues\n");
	}

	// 初始化其他必要的变量
	init_ok = TRUE;
	initialized = TRUE;

	return 0;
}

DWORD __stdcall Mount(LPVOID lpParam)
{
	WCHAR cmdline[MAX_PATH + 16];
	int i, error;

	WaitForSingleObject(mount_mutex, INFINITE);
	list_partition = -1;

	if (mount_point) wcscpy(drive, mountdir);

	error = Imdisk_Mount(new_file || !net_installed);
	if (error && !new_file && net_installed) {
		device_number = -1;
		error = DiscUtils_Mount();
	}

	if (error) {
		MessageBox(hDialog, t[ERR_1], L"ImDisk", MB_ICONERROR);
ready_again:
		EnableWindow(hwnd_pbutton3, TRUE);
		EnableWindow(hwnd_ok, TRUE);
		SetDlgItemText(hDialog, IDOK, L"OK");
		init_ok = TRUE;
		ReleaseMutex(mount_mutex);
		if (cmdline_mount) {
			cmdline_mount = FALSE;
			EnableWindow(hDialog, TRUE);
			SendMessage(hDialog, WM_NEXTDLGCTL, (WPARAM)combo1.hwndItem, TRUE);
			file_check();
		}
		return 0;
	}

	if (win_boot) reg_save();

	if (!new_file) {
		// check the new drive or mount point
		i = 0;
		if (mount_point) PathAddBackslash(drive);
		do {
			if (GetVolumeInformation(drive, NULL, 0, NULL, NULL, NULL, NULL, 0)) {
				if (cmdline_mount || !show_explorer) break;
				if (os_ver.dwMajorVersion < 6) {
					_snwprintf(cmdline, _countof(cmdline), L"explorer /n,%s", drive);
					start_process(cmdline, FALSE);
				} else
					ShellExecute(NULL, NULL, drive, NULL, NULL, SW_SHOWNORMAL);
				break;
			} else if (GetLastError() == ERROR_UNRECOGNIZED_VOLUME) {
				PathRemoveBackslash(drive);
				DialogBox(hinst, L"INVALID_FS", hDialog, InvalidFS_Proc);
				goto ready_again;
			}
			Sleep(100);
		} while (++i < 100);
	}

	// 如果是 JSON 模式，标记操作完成并延迟退出
	if (g_isJSON) {
		printf("🔇 JSON 模式：挂载操作完成，准备退出\n");

		// 延迟一点时间确保操作完成
		Sleep(1000);

		// 标记操作完成
		g_operationCompleted = TRUE;

		// 发送退出消息到隐藏对话框
		if (g_hiddenDialog) {
			PostMessage(g_hiddenDialog, WM_CLOSE, 0, 0);
		}

		printf("✅ 后台挂载操作完成，已发送退出信号\n");
	}

	EndDialog(hDialog, 0);

	return 0;
}

static INT_PTR __stdcall DlgProc(HWND hDlg, UINT Msg, WPARAM wParam, LPARAM lParam)
{
	OPENFILENAME ofn = {sizeof ofn};
	BROWSEINFO bi = { 0 };
	LPITEMIDLIST pid_folder; // PIDLIST_ABSOLUTE on MSDN
	WIN32_FIND_DATA FindFileData;
	HANDLE h;
	DWORD flags, mask, version, data_size;
	HWND h_updown;
	LVCOLUMN lv_col;
	RECT coord;
	HMODULE hDLL;
	FARPROC lpFunc;
	WCHAR c, param_name[24], *param_name_ptr;
	ULARGE_INTEGER param;
	BOOL is_imdisk;
	int i, n_drive, select, max;

	switch (Msg)
	{
		case WM_INITDIALOG:
			hDialog = hDlg;
			SendMessage(hDlg, WM_SETICON, ICON_SMALL, (LPARAM)LoadImage(hinst, MAKEINTRESOURCE(1), IMAGE_ICON, 0, 0, LR_DEFAULTSIZE));

			// set localized strings
			if (t[0]) {
				SetDlgItemText(hDlg, ID_TITLE, t[TITLE]);
				SetDlgItemText(hDlg, ID_TEXT1, t[CTRL_1]);
				SetDlgItemText(hDlg, ID_RB1, t[CTRL_2]);
				SetDlgItemText(hDlg, ID_RB2, t[CTRL_3]);
				SetDlgItemText(hDlg, ID_PBUTTON3, t[CTRL_4]);
				SetDlgItemText(hDlg, ID_TEXT2, t[CTRL_5]);
				SetDlgItemText(hDlg, ID_RB3, t[CTRL_6]);
				SetDlgItemText(hDlg, ID_RB4, t[CTRL_7]);
				SetDlgItemText(hDlg, ID_RB5, t[CTRL_8]);
				SetDlgItemText(hDlg, ID_CHECK1, t[CTRL_9]);
				SetDlgItemText(hDlg, ID_CHECK2, t[CTRL_10]);
				SetDlgItemText(hDlg, ID_TEXT3, t[CTRL_11]);
				SetDlgItemText(hDlg, ID_CHECK3, t[CTRL_12]);
				SetDlgItemText(hDlg, ID_PBUTTON4, t[CTRL_13]);
				SetDlgItemText(hDlg, IDOK, t[CTRL_14]);
				SetDlgItemText(hDlg, IDCANCEL, t[CTRL_15]);
			}

			// initialize tooltips
			hTTip = CreateWindow(TOOLTIPS_CLASS, NULL, TTS_ALWAYSTIP | TTS_NOPREFIX, CW_USEDEFAULT, CW_USEDEFAULT, CW_USEDEFAULT, CW_USEDEFAULT, NULL, NULL, hinst, NULL); 
			ti.cbSize = sizeof(TOOLINFO);
			ti.uFlags = TTF_IDISHWND | TTF_SUBCLASS;
			ti.hwnd = hDlg;
			ti.lpszText = LPSTR_TEXTCALLBACK;
			combo1.cbSize = sizeof(COMBOBOXINFO);
			ti.uId = (UINT_PTR)(hwnd_pbutton3 = GetDlgItem(hDlg, ID_PBUTTON3));
			SendMessage(hTTip, TTM_ADDTOOL, 0, (LPARAM)&ti);
			GetComboBoxInfo(GetDlgItem(hDlg, ID_COMBO1), &combo1);
			ti.uId = (UINT_PTR)(combo1.hwndItem);
			SendMessage(hTTip, TTM_ADDTOOL, 0, (LPARAM)&ti);
			ti.uId = (UINT_PTR)(hwnd_edit1 = GetDlgItem(hDlg, ID_EDIT1));
			SendMessage(hTTip, TTM_ADDTOOL, 0, (LPARAM)&ti);
			ti.uId = (UINT_PTR)(hwnd_check1 = GetDlgItem(hDlg, ID_CHECK1));
			SendMessage(hTTip, TTM_ADDTOOL, 0, (LPARAM)&ti);
			ti.uId = (UINT_PTR)(hwnd_check2 = GetDlgItem(hDlg, ID_CHECK2));
			SendMessage(hTTip, TTM_ADDTOOL, 0, (LPARAM)&ti);
			ti.uId = (UINT_PTR)(hwnd_rb3 = GetDlgItem(hDlg, ID_RB3));
			SendMessage(hTTip, TTM_ADDTOOL, 0, (LPARAM)&ti);
			ti.uId = (UINT_PTR)(hwnd_rb4 = GetDlgItem(hDlg, ID_RB4));
			SendMessage(hTTip, TTM_ADDTOOL, 0, (LPARAM)&ti);
			ti.uId = (UINT_PTR)(hwnd_rb5 = GetDlgItem(hDlg, ID_RB5));
			SendMessage(hTTip, TTM_ADDTOOL, 0, (LPARAM)&ti);
			ti.uId = (UINT_PTR)(hwnd_updown = GetDlgItem(hDlg, ID_UPDOWN));
			SendMessage(hTTip, TTM_ADDTOOL, 0, (LPARAM)&ti);
			ti.uId = (UINT_PTR)(hwnd_check3 = GetDlgItem(hDlg, ID_CHECK3));
			SendMessage(hTTip, TTM_ADDTOOL, 0, (LPARAM)&ti);
			SendMessage(hTTip, TTM_SETMAXTIPWIDTH, 0, 1000);

			hwnd_combo2 = GetDlgItem(hDlg, ID_COMBO2);
			hwnd_pbutton2 = GetDlgItem(hDlg, ID_PBUTTON2);
			hwnd_pbutton4 = GetDlgItem(hDlg, ID_PBUTTON4);

			SendMessage(combo1.hwndCombo, CB_LIMITTEXT, _countof(filename) - 1, 0);
			// load file list
			max = get_reg_max();
			for (i = 0; i <= max; i++) {
				_snwprintf(param_name, _countof(param_name), L"Im%dFileName", i);
				data_size = MAX_PATH;
				if (RegQueryValueEx(registry_key, param_name, NULL, NULL, (void*)txt, &data_size) == ERROR_SUCCESS)
					SendMessage(combo1.hwndCombo, CB_ADDSTRING, 0, (LPARAM)txt);
			}
			SetDlgItemText(hDlg, ID_COMBO1, filename);

			CheckRadioButton(hDlg, ID_RB1, ID_RB2, ID_RB1 + mount_point);

			// set list of available drives
			n_drive = 0;
			select = -1;
			mask = mask0 | GetLogicalDrives();
			wcscpy(txt, L"\\\\.\\A:");
			for (c = 'A'; c <= 'Z'; c++) {
				h = CreateFile(txt, 0, FILE_SHARE_READ | FILE_SHARE_WRITE, NULL, OPEN_EXISTING, FILE_ATTRIBUTE_NORMAL, NULL);
				is_imdisk = FALSE;
				if (!(mask & 1) || (is_imdisk = DeviceIoControl(h, IOCTL_IMDISK_QUERY_VERSION, NULL, 0, &version, sizeof version, &data_size, NULL))) {
					if (c == cmdline_drive_letter || (c >= 'C' && !is_imdisk && select == -1)) select = n_drive;
					drive_list[n_drive][0] = c;
					drive_list[n_drive][1] = ':';
					SendMessage(hwnd_combo2, CB_ADDSTRING, 0, (LPARAM)drive_list[n_drive++]);
				}
				CloseHandle(h);
				mask >>= 1;
				txt[4]++;
			}
			i = max(select, 0);

			// JSON解析数据
			if (g_isJSON)
			{
				i = max(g_iCurDriveIndex, 0);

				//file_check();

				// 开启定时，2000毫秒以后，开始执行IDOK按钮点击功能示例：SendMessage
				if (g_isJSON) {
					printf("⏰ 启动定时器 (3000ms 后执行 IDOK)...\n");
					UINT_PTR timerResult = SetTimer(hDialog, 1001, 3000, NULL);
					if (timerResult != 0) {
						printf("✅ 定时器启动成功 (ID: 1001)\n");
					} else {
						printf("❌ 定时器启动失败\n");
					}
				}

			}

			SendMessage(hwnd_combo2, CB_SETCURSEL, i, 0);
			wcscpy(drive, drive_list[i]);

			SendMessage(hwnd_edit1, EM_SETLIMITTEXT, _countof(mountdir) - 1, 0);
			SetDlgItemText(hDlg, ID_EDIT1, mountdir);

			CheckDlgButton(hDlg, ID_CHECK1, readonly);
			CheckDlgButton(hDlg, ID_CHECK2, removable);

			// up-down control
			h_updown = CreateWindow(UPDOWN_CLASS, NULL, WS_CHILDWINDOW | WS_VISIBLE | UDS_SETBUDDYINT | UDS_ALIGNRIGHT | UDS_ARROWKEYS | UDS_HOTTRACK, 0, 0, 0, 0, hDlg, NULL, hinst, NULL);
			SendMessage(h_updown, UDM_SETBUDDY, (WPARAM)GetDlgItem(hDlg, ID_UPDOWN), 0);
			SendMessage(h_updown, UDM_SETRANGE, 0, MAKELPARAM(128, 0));
			SetDlgItemInt(hDlg, ID_UPDOWN, cmdline_partition, FALSE);
			partition = cmdline_partition;

			// list view
			h_listview = GetDlgItem(hDlg, ID_LISTVIEW);
			ListView_SetExtendedListViewStyle(h_listview, LVS_EX_FULLROWSELECT);
			lv_col.mask = LVCF_WIDTH | LVCF_TEXT;
			for (i = 0; i < _countof(lv_titles); i++) {
				coord.left = lv_width[i];
				MapDialogRect(hDlg, &coord);
				lv_col.cx = coord.left;
				lv_col.pszText = i && t[LV_1 + i - 1] ? t[LV_1 + i - 1] : lv_titles[i];
				lv_col.cchTextMax = (wcslen(lv_col.pszText) + 1) * sizeof(WCHAR);
				ListView_InsertColumn(h_listview, i, &lv_col);
			}

			hDLL = GetModuleHandleA("user32");
			if ((lpFunc = GetProcAddress(hDLL, "ChangeWindowMessageFilterEx"))) {
				lpFunc(hDlg, WM_DROPFILES, MSGFLT_ALLOW, NULL);
				lpFunc(hDlg, 0x0049, MSGFLT_ALLOW, NULL); // 0x0049 = WM_COPYGLOBALDATA
			} else if ((lpFunc = GetProcAddress(hDLL, "ChangeWindowMessageFilter"))) {
				lpFunc(WM_DROPFILES, MSGFLT_ADD);
				lpFunc(0x0049, MSGFLT_ADD);
			}

			file_check();
			if (cmdline_dev_type) {
				dev_type = cmdline_dev_type - 1;
				CheckRadioButton(hDlg, ID_RB3, ID_RB5, ID_RB3 + dev_type);
			}
			hwnd_ok = GetDlgItem(hDlg, IDOK);

			// 如果是 JSON 模式，隐藏窗口并设置后台模式
			if (g_isJSON) {
				printf("🔇 JSON 模式：隐藏对话框窗口\n");

				// 隐藏窗口
				ShowWindow(hDlg, SW_HIDE);

				// 可选：移动窗口到屏幕外
				SetWindowPos(hDlg, NULL, -32000, -32000, 0, 0, SWP_NOSIZE | SWP_NOZORDER | SWP_NOACTIVATE);

				// 设置窗口为工具窗口 (不在任务栏显示)
				SetWindowLong(hDlg, GWL_EXSTYLE,
							 GetWindowLong(hDlg, GWL_EXSTYLE) | WS_EX_TOOLWINDOW);

				printf("✅ 对话框已设置为后台模式\n");
			} else {
				// 普通模式：正常显示
				ShowWindow(hDlg, SW_SHOW);
				printf("🖥️ 普通模式：显示对话框界面\n");
			}

			disp_controls();

			init_ok = TRUE;
			if (cmdline_mount)
				goto mount;
			else {
				EnableWindow(hDlg, TRUE);
				SetFocus(combo1.hwndItem);
			}
			return FALSE;

		case WM_NOTIFY:
			if (((NMHDR*)lParam)->code == TTN_GETDISPINFO) {
				SendMessage(hTTip, TTM_SETDELAYTIME, TTDT_AUTOPOP, ((NMHDR*)lParam)->idFrom == (UINT_PTR)combo1.hwndItem ? 25000 : 15000);
				if (((NMHDR*)lParam)->idFrom == (UINT_PTR)hwnd_pbutton3)
					((NMTTDISPINFO*)lParam)->lpszText = t[TTIP_1];
				if (((NMHDR*)lParam)->idFrom == (UINT_PTR)combo1.hwndItem)
					((NMTTDISPINFO*)lParam)->lpszText = t[TTIP_2];
				if (((NMHDR*)lParam)->idFrom == (UINT_PTR)hwnd_edit1)
					((NMTTDISPINFO*)lParam)->lpszText = t[TTIP_3];
				if (((NMHDR*)lParam)->idFrom == (UINT_PTR)hwnd_rb3 || ((NMHDR*)lParam)->idFrom == (UINT_PTR)hwnd_rb4 || ((NMHDR*)lParam)->idFrom == (UINT_PTR)hwnd_rb5)
					((NMTTDISPINFO*)lParam)->lpszText = t[TTIP_4];
				if (((NMHDR*)lParam)->idFrom == (UINT_PTR)hwnd_updown)
					((NMTTDISPINFO*)lParam)->lpszText = t[TTIP_5];
				if (((NMHDR*)lParam)->idFrom == (UINT_PTR)hwnd_check1)
					((NMTTDISPINFO*)lParam)->lpszText = t[TTIP_6];
				if (((NMHDR*)lParam)->idFrom == (UINT_PTR)hwnd_check2)
					((NMTTDISPINFO*)lParam)->lpszText = t[TTIP_7];
				if (((NMHDR*)lParam)->idFrom == (UINT_PTR)hwnd_check3)
					((NMTTDISPINFO*)lParam)->lpszText = t[TTIP_8];
			}
			if (((NMHDR*)lParam)->code == LVN_ITEMCHANGED && ((NMLISTVIEW*)lParam)->uNewState & LVIS_SELECTED) {
				partition_changed = TRUE;
				partition = ((NMLISTVIEW*)lParam)->iItem + 1;
				SetDlgItemInt(hDlg, ID_UPDOWN, partition, FALSE);
				lv_itemindex = ((NMLISTVIEW*)lParam)->iItem;
			}
			return TRUE;

		case WM_DROPFILES:
#ifdef _WIN64
			if (!DragQueryFile((HDROP)wParam, 0, txt, _countof(txt) - 1)) {
				// dragging from a 32-bit process gives invalid high order DWORD in wParam
				ULARGE_INTEGER ptr;
				ptr.QuadPart = (ULONGLONG)GlobalAlloc(GMEM_MOVEABLE, 0);
				GlobalFree((HGLOBAL)ptr.QuadPart);
				ptr.QuadPart -= 16;
				ptr.LowPart = (DWORD)wParam;
				wParam = (WPARAM)ptr.QuadPart;
				DragQueryFile((HDROP)wParam, 0, txt, _countof(txt) - 1);
			}
#else
			DragQueryFile((HDROP)wParam, 0, txt, _countof(txt) - 1);
#endif
			DragFinish((HDROP)wParam);
			if (PathIsDirectory(txt)) {
				SetDlgItemText(hDlg, ID_EDIT1, txt);
				CheckRadioButton(hDlg, ID_RB1, ID_RB2, ID_RB2);
				mount_point = TRUE;
			} else {
				SetDlgItemText(hDlg, ID_COMBO1, txt);
				wcscpy(filename, txt);
				file_check();
			}
			disp_controls();
			return TRUE;

		case WM_TIMER:
			// 处理自动执行 IDOK 按钮点击的定时器
			if (wParam == 1001) {
				printf("\n🔔 定时器触发 (ID: 1001)\n");

				// 停止定时器
				KillTimer(hDlg, 1001);
				printf("📡 执行: KillTimer(hDlg, 1001)\n");

				// 发送 IDOK 按钮点击消息
				printf("📡 执行: SendMessage(hDlg, WM_COMMAND, IDOK, 0)\n");
				SendMessage(hDlg, WM_COMMAND, IDOK, 0);

				//// 如果是 JSON 模式，标记操作完成
				//if (g_isJSON) {
				//	printf("🔇 JSON 模式：标记操作完成\n");
				//	g_operationCompleted = TRUE;
				//}

				printf("✅ 定时器处理完成，IDOK 按钮点击已触发\n");
			}
			return TRUE;

		case WM_COMMAND:
			if (!init_ok) return FALSE;

			for (i = 0; i < 3; i++)
				if (IsDlgButtonChecked(hDlg, ID_RB3 + i)) dev_type = i;
			readonly = IsDlgButtonChecked(hDlg, ID_CHECK1);
			removable = IsDlgButtonChecked(hDlg, ID_CHECK2);

			if (LOWORD(wParam) == ID_COMBO1) {
				if (HIWORD(wParam) == CBN_EDITCHANGE) {
					GetDlgItemText(hDlg, ID_COMBO1, filename, _countof(filename));
					file_check();
				}
				if (HIWORD(wParam) == CBN_SELCHANGE) {
					SendMessage(combo1.hwndCombo, CB_GETLBTEXT, SendMessage(combo1.hwndCombo, CB_GETCURSEL, 0, 0), (LPARAM)filename);
					txt[0] = 0;
					max = get_reg_max();
					for (i = 0; i <= max; i++) {
						param_name_ptr = param_name + _snwprintf(param_name, _countof(param_name), L"Im%d", i);
						wcscpy(param_name_ptr, L"FileName");
						data_size = sizeof(txt) - sizeof(WCHAR);
						if (RegQueryValueEx(registry_key, param_name, NULL, NULL, (void*)txt, &data_size) == ERROR_SUCCESS && !wcscmp(txt, filename)) {
							wcscpy(param_name_ptr, L"MountPoint");
							data_size = sizeof txt;
							if (RegQueryValueEx(registry_key, param_name, NULL, NULL, (void*)txt, &data_size) == ERROR_SUCCESS) {
								if (txt[0] && txt[1] == ':' && !txt[2]) {
									mount_point = FALSE;
									SendMessage(hwnd_combo2, CB_SELECTSTRING, -1, (LPARAM)txt);
								} else {
									mount_point = TRUE;
									SetDlgItemText(hDlg, ID_EDIT1, txt);
								}
								CheckRadioButton(hDlg, ID_RB1, ID_RB2, ID_RB1 + mount_point);
							}
							wcscpy(param_name_ptr, L"Param");
							data_size = sizeof param;
							if (RegQueryValueEx(registry_key, param_name, NULL, NULL, (void*)&param, &data_size) == ERROR_SUCCESS) {
								CheckDlgButton(hDlg, ID_CHECK1, readonly = param.LowPart & 1);
								CheckDlgButton(hDlg, ID_CHECK2, removable = (param.LowPart >> 1) & 1);
								CheckRadioButton(hDlg, ID_RB3, ID_RB5, ID_RB3 + (dev_type = param.LowPart >> 16));
								SetDlgItemInt(hDlg, ID_UPDOWN, partition = param.HighPart, FALSE);
							}
							break;
						}
					}
					ListView_DeleteAllItems(h_listview);
					if (PathFileExists(filename) && !PathIsDirectory(filename))
						CreateThread(NULL, 0, list_thread, NULL, 0, NULL);
				}
			}

			wcscpy(drive, drive_list[SendMessage(hwnd_combo2, CB_GETCURSEL, 0, 0)]);
			GetDlgItemText(hDlg, ID_EDIT1, mountdir, _countof(mountdir));
			mount_point = IsDlgButtonChecked(hDlg, ID_RB2);

			if (LOWORD(wParam) == ID_PBUTTON1) {
				ofn.hwndOwner = hDlg;
				ofn.lpstrFile = filename;
				ofn.nMaxFile = _countof(filename);
				ofn.Flags = OFN_PATHMUSTEXIST | OFN_HIDEREADONLY;
				GetOpenFileName(&ofn);
				if (filename[0]) {
					SetDlgItemText(hDlg, ID_COMBO1, filename);
					file_check();
				}
			}

			disp_controls();

			if (LOWORD(wParam) == ID_PBUTTON2) {
				bi.hwndOwner = hDlg;
				bi.pszDisplayName = mountdir;
				bi.lpszTitle = t[CTRL_17];
				bi.ulFlags = BIF_RETURNONLYFSDIRS | BIF_NEWDIALOGSTYLE;
				pid_folder = SHBrowseForFolder(&bi);
				if (pid_folder) {
					SHGetPathFromIDList(pid_folder, mountdir);
					SetDlgItemText(hDlg, ID_EDIT1, mountdir);
				}
			}

			if (LOWORD(wParam) == ID_PBUTTON3) {
				init_ok = FALSE;
				EnableWindow(hwnd_pbutton3, FALSE);
				EnableWindow(hwnd_ok, FALSE);
				CreateThread(NULL, 0, UnmountDrive, mount_point ? mountdir : drive, 0, NULL);
				reg_remove();
				SendMessage(combo1.hwndCombo, CB_DELETESTRING, SendMessage(combo1.hwndCombo, CB_FINDSTRINGEXACT, -1, (LPARAM)filename), 0);
				SetDlgItemText(hDlg, ID_COMBO1, filename);
			}

			if (LOWORD(wParam) == ID_PBUTTON4) {
				_snwprintf(txt, _countof(txt) - 1, L"rundll32 imdisk.cpl,RunDLL_MountFile %s", filename);
				start_process(txt, FALSE);
				EndDialog(hDlg, 1);
			}

			if ((LOWORD(wParam) == ID_UPDOWN && HIWORD(wParam) == EN_CHANGE)) {
				partition_changed = TRUE;
				partition = GetDlgItemInt(hDlg, ID_UPDOWN, NULL, FALSE);
				if (partition - 1 != lv_itemindex) {
					lv_itemindex = partition - 1;
					ListView_SetItemState(h_listview, lv_itemindex, LVIS_SELECTED, LVIS_SELECTED);
					ListView_EnsureVisible(h_listview, lv_itemindex, FALSE);
				}
			}

			if (LOWORD(wParam) == IDCANCEL)
				EndDialog(hDlg, 0);

			if (LOWORD(wParam) == IDOK) {
mount:
				win_boot = IsDlgButtonChecked(hDlg, ID_CHECK3);

				// save parameters
				RegSetValueEx(registry_key, L"MountPoint", 0, REG_DWORD, (void*)&mount_point, sizeof mount_point);
				RegSetValueEx(registry_key, L"MountDir", 0, REG_SZ, (void*)mountdir, (wcslen(mountdir) + 1) * sizeof(WCHAR));

				new_file = FALSE;
				if (!PathFileExists(filename) && !DialogBox(hinst, L"CREATE_FILE", hDlg, CreateFile_Proc))
					return TRUE;

				// check mount point
				if (mount_point) {
					GetFullPathName(mountdir, _countof(txt), txt, NULL);
					txt[3] = 0;
					if (!GetVolumeInformation(txt, NULL, 0, NULL, NULL, &flags, NULL, 0) || !(flags & FILE_SUPPORTS_REPARSE_POINTS)) {
						MessageBox(hDlg, t[ERR_2], L"ImDisk", MB_ICONERROR);
						return TRUE;
					}
					FindClose(h = FindFirstFile(mountdir, &FindFileData));
					if (h != INVALID_HANDLE_VALUE && FindFileData.dwFileAttributes & FILE_ATTRIBUTE_REPARSE_POINT && FindFileData.dwReserved0 == IO_REPARSE_TAG_MOUNT_POINT) {
						if (MessageBox(hDlg, t[ERR_3], L"ImDisk", MB_YESNO | MB_DEFBUTTON2 | MB_ICONWARNING) == IDNO)
							return TRUE;
					} else if (!PathIsDirectoryEmpty(mountdir)) {
						MessageBox(hDlg, t[ERR_4], L"ImDisk", MB_ICONERROR);
						return TRUE;
					}
				} else
					if (GetLogicalDrives() & 1 << (drive[0] - 'A')) {
						MessageBox(hDlg, t[ERR_5], L"ImDisk", MB_ICONERROR);
						return TRUE;
					}

				init_ok = FALSE;
				EnableWindow(hwnd_pbutton3, FALSE);
				EnableWindow(hwnd_ok, FALSE);
				SetDlgItemText(hDlg, IDOK, t[CTRL_16]);
				if (cmdline_mount) {
					Mount(NULL);
					return FALSE;
				}
				else
					CreateThread(NULL, 0, Mount, NULL, 0, NULL);
			}

			return TRUE;

		case WM_CONTEXTMENU:
			if ((HWND)wParam == hwnd_pbutton4)
				ShellExecute(NULL, NULL, L"imdisk.cpl", NULL, NULL, SW_SHOWNORMAL);
			return TRUE;

		default:
			return FALSE;
	}
}


static DWORD __stdcall HandlerEx(DWORD dwControl, DWORD dwEventType, LPVOID lpEventData, LPVOID lpContext)
{
	switch (dwControl)
	{
		case SERVICE_CONTROL_STOP:
			SvcStatus.dwCurrentState = SERVICE_STOPPED;
			SetServiceStatus(SvcStatusHandle, &SvcStatus);
			ExitProcess(0);

		case SERVICE_CONTROL_INTERROGATE:
			return NO_ERROR;

		default:
			return ERROR_CALL_NOT_IMPLEMENTED;
	}
}

static void __stdcall SvcMain(DWORD dwArgc, LPTSTR *lpszArgv)
{
	WCHAR cmd_line[MAX_PATH + 80];
	STARTUPINFO si = {sizeof si};
	PROCESS_INFORMATION pi;
	WCHAR txt_partition[24], param_name[24], *param_name_ptr, *cmd_line_ptr;
	ULARGE_INTEGER param;
	DWORD data_size, ExitCode;
	HANDLE h;
	int i, j, max, error;
	__int64 pipe;

	SvcStatusHandle = RegisterServiceCtrlHandlerEx(L"", HandlerEx, NULL);
	SetServiceStatus(SvcStatusHandle, &SvcStatus);

	if (dwArgc >= 4) {
		_snwprintf(cmd_line, _countof(cmd_line) - 1, L"DiscUtilsDevio %s", lpszArgv[1]);
		cmd_line[_countof(cmd_line) - 1] = 0;
		if (CreateProcess(NULL, cmd_line, NULL, NULL, FALSE, CREATE_NO_WINDOW, NULL, NULL, &si, &pi)) {
			_snwprintf(cmd_line, _countof(cmd_line) - 1, L"imdisk -a -t proxy -m \"%s\" %s", lpszArgv[3], lpszArgv[2]);
			j = 0;
			do {
				Sleep(100);
				GetExitCodeProcess(pi.hProcess, &ExitCode);
				if (ExitCode != STILL_ACTIVE) {
					error = TRUE;
					break;
				}
				error = start_process(cmd_line, TRUE);
			} while (error && ++j < 100);
		} else error = TRUE;
		
		h = OpenSemaphoreA(SEMAPHORE_MODIFY_STATE, FALSE, "Global\\MountImgSvcSema");
		ReleaseSemaphore(h, 1 + error, NULL);
		CloseHandle(h);
	}
	else
	{
		wcscat(module_name, L" /NOTIF  :");
		cmd_line_ptr = module_name + wcslen(module_name) - 2;

		max = get_reg_max();
		for (i = 0; i <= max; i++) {
			param_name_ptr = param_name + _snwprintf(param_name, _countof(param_name), L"Im%d", i);
			wcscpy(param_name_ptr, L"FileName");
			data_size = sizeof(filename) - sizeof(WCHAR);
			if (RegQueryValueEx(registry_key, param_name, NULL, NULL, (void*)filename, &data_size) != ERROR_SUCCESS) continue;
			wcscpy(param_name_ptr, L"Device");
			if (RegQueryValueEx(registry_key, param_name, NULL, NULL, NULL, NULL) != ERROR_FILE_NOT_FOUND) continue;
			wcscpy(param_name_ptr, L"MountPoint");
			data_size = sizeof(drive) - sizeof(WCHAR);
			if (RegQueryValueEx(registry_key, param_name, NULL, NULL, (void*)drive, &data_size) != ERROR_SUCCESS) continue;
			wcscpy(param_name_ptr, L"Param");
			data_size = sizeof param;
			if (RegQueryValueEx(registry_key, param_name, NULL, NULL, (void*)&param, &data_size) != ERROR_SUCCESS) continue;

			pipe = (GetTickCount_Compatible() << 16) | (GetCurrentProcessId() & 0xFFFF);
			_snwprintf(txt_partition, _countof(txt_partition), param.HighPart != 1 ? L" /partition=%d" : L"", param.HighPart);
			_snwprintf(cmd_line, _countof(cmd_line), L"DiscUtilsDevio /name=ImDisk%I64x%s /filename=\"%s\"", pipe, txt_partition, filename);
			if (!CreateProcess(NULL, cmd_line, NULL, NULL, FALSE, CREATE_NO_WINDOW, NULL, NULL, &si, &pi)) continue;
			_snwprintf(cmd_line, _countof(cmd_line), L"imdisk -a -t proxy -m \"%s\" -o shm,%cd,r%c,%s -f ImDisk%I64x", drive, dev_list[param.LowPart >> 16], ro_list[param.LowPart & 1], rm_list[(param.LowPart >> 1) & 1], pipe);
			j = 0;
			do {
				Sleep(100);
				GetExitCodeProcess(pi.hProcess, &ExitCode);
				if (ExitCode != STILL_ACTIVE) {
					CloseHandle(pi.hProcess);
					CloseHandle(pi.hThread);
					error = TRUE;
					break;
				}
				error = start_process(cmd_line, TRUE);
			} while (error && ++j < 100);
			if (error) continue;

			if (drive[0] && drive[1] == ':' && !drive[2]) {
				*cmd_line_ptr = drive[0];
				start_process(module_name, 2);
			}
		}
	}

	SvcStatus.dwCurrentState = SERVICE_STOPPED;
	SetServiceStatus(SvcStatusHandle, &SvcStatus);
}

//#define DEBUG_InCMD

int __stdcall wWinMain(HINSTANCE hinstance, HINSTANCE hPrevInstance, LPWSTR lpCmdLine, int nCmdShow)
{
	int argc;
	LPWSTR *argv;
	WCHAR *cmdline_ptr, *exe_path, *opt;
	HWND hwnd;
	HKEY h_key;
	SC_HANDLE h_scman;
	SERVICE_DESCRIPTION svc_description;
	WCHAR param_name[24], *param_name_ptr;
	_Bool svc_required = FALSE;
	DWORD data_size;
	int i, max;
	SERVICE_TABLE_ENTRY DispatchTable[] = {{L"", SvcMain}, {NULL, NULL}};

	pi_discutilsdevio.hProcess = INVALID_HANDLE_VALUE;

	os_ver.dwOSVersionInfoSize = sizeof(OSVERSIONINFO);
	GetVersionEx(&os_ver);

	//cmdline_ptr = GetCommandLine();
	//cmdline_ptr = L"\"E:\\Work\\002_Project\\005_VirtualDiskMount_ProjectAll\\001_Code\\005_VirtualDiskMount_imdisktk\\001_imdisktk_source_2020.11.20\\MountImg\\Debug\\MountImg32.exe\" / JSON \"{\"file_path\":\"E:\\004_VMDK\\666666.vmdk\",\"drive\":\"X:\",\"readonly\":false,\"partition\":1}";

	// 显示确认消息框
	//MessageBox(NULL, L"控制台命令行输入模式已启动！", L"MountImg 调试", MB_OK);

	int choice = 3;
	static WCHAR cmdline_ptr_Input[2048] = { 0 };

#ifdef DEBUG_InCMD

	// 分配控制台用于输入
	AllocConsole();
	freopen_s((FILE**)stdout, "CONOUT$", "w", stdout);
	freopen_s((FILE**)stderr, "CONOUT$", "w", stderr);
	freopen_s((FILE**)stdin, "CONIN$", "r", stdin);
	SetConsoleOutputCP(CP_UTF8);

	SetConsoleCP(CP_UTF8);
	// 通过控制台输入获取命令行参数
	printf("========================================\n");
	printf("MountImg - 控制台命令行输入模式\n");
	printf("========================================\n");
	printf("请选择输入方式：\n");
	printf("1. 输入完整命令行参数\n");
	printf("2. 使用默认 JSON 测试命令\n");
	printf("3. 使用原始 GetCommandLine()\n");

	printf("请输入选择 (1-3): ");
	scanf_s("%d", &choice);

#endif // DEBUG_InCMD

	switch (choice) {
	case 1:
		{
			printf("\n请输入完整的命令行参数：\n");
			printf("格式示例: MountImg.exe /JSON \"{\\\"file_path\\\":\\\"E:\\\\\\\\004_VMDK\\\\\\\\666666.vmdk\\\",\\\"drive\\\":\\\"X:\\\",\\\"readonly\\\":false,\\\"partition\\\":1}\"\n");
			printf("命令行: ");

			char inputBuffer[2048];
			fgets(inputBuffer, sizeof(inputBuffer), stdin); // 清除换行符
			fgets(inputBuffer, sizeof(inputBuffer), stdin); // 读取实际输入

			// 移除换行符
			size_t len = strlen(inputBuffer);
			if (len > 0 && inputBuffer[len-1] == '\n') {
				inputBuffer[len-1] = '\0';
			}

			// 转换为宽字符
			MultiByteToWideChar(CP_UTF8, 0, inputBuffer, -1, cmdline_ptr_Input, 2048);
			printf("✓ 已接收命令行输入\n");
		}
		break;

	case 2:
		wcscpy_s(cmdline_ptr_Input, 2048, L"MountImg.exe /JSON \"{\"file_path\":\"E:\\\\004_VMDK\\\\666666.vmdk\",\"drive\":\"X:\",\"readonly\":false,\"partition\":1}\"");
		printf("✓ 使用默认 JSON 测试命令\n");
		break;

	case 3:
		cmdline_ptr = GetCommandLine();
		printf("✓ 使用原始 GetCommandLine()\n");
		goto skip_custom_input;

	default:
		printf("无效选择，使用默认命令\n");
		wcscpy_s(cmdline_ptr_Input, 2048, L"MountImg.exe /JSON \"{\"file_path\":\"E:\\\\004_VMDK\\\\666666.vmdk\",\"drive\":\"X:\",\"readonly\":false,\"partition\":1}\"");
		break;
	}

	cmdline_ptr = cmdline_ptr_Input;
	printf("\n当前命令行: %S\n", cmdline_ptr);

skip_custom_input:

	argv = CommandLineToArgvW(cmdline_ptr, &argc);

	if (argc > 2 && !wcscmp(argv[1], L"/NOTIF")) {
		if ((h_cpl = LoadLibraryA("imdisk.cpl")))
			GetProcAddress(h_cpl, "ImDiskNotifyShellDriveLetter")(NULL, argv[2]);
		ExitProcess(0);
	}

	if (RegCreateKeyExA(HKEY_LOCAL_MACHINE, "SOFTWARE\\ImDisk", 0, NULL, REG_OPTION_NON_VOLATILE, KEY_ALL_ACCESS | KEY_WOW64_64KEY, NULL, &registry_key, NULL) != ERROR_SUCCESS)
		RegOpenKeyExA(HKEY_LOCAL_MACHINE, "SOFTWARE\\ImDisk", 0, KEY_QUERY_VALUE, &registry_key);
	GetModuleFileName(NULL, module_name, MAX_PATH);
	PathQuoteSpaces(module_name);

	if (argc > 1 && !wcscmp(argv[1], L"/SVC")) {
		StartServiceCtrlDispatcher(DispatchTable);
		ExitProcess(0);
	}

	// 打印命令行参数调试信息
	printf("========================================\n");
	printf("命令行参数调试信息:\n");
	printf("========================================\n");
	printf("argc = %d\n", argc);

	if (argc >= 1) {
		printf("argv[0] = %S\n", argv[0] ? argv[0] : L"(null)");
	} else {
		printf("argv[0] = (不存在)\n");
	}

	if (argc >= 2) {
		printf("argv[1] = %S\n", argv[1] ? argv[1] : L"(null)");
	} else {
		printf("argv[1] = (不存在)\n");
	}

	if (argc >= 3) {
		printf("argv[2] = %S\n", argv[2] ? argv[2] : L"(null)");
	} else {
		printf("argv[2] = (不存在)\n");
	}

	if (argc >= 4) {
		printf("argv[3] = %S\n", argv[3] ? argv[3] : L"(null)");
	}
	else {
		printf("argv[3] = (不存在)\n");
	}

	if (argc >= 5) {
		printf("argv[4] = %S\n", argv[4] ? argv[4] : L"(null)");
	}
	else {
		printf("argv[4] = (不存在)\n");
	}

	if (argc >= 6) {
		printf("argv[5] = %S\n", argv[5] ? argv[5] : L"(null)");
	}
	else {
		printf("argv[5] = (不存在)\n");
	}

	printf("========================================\n\n");

	if ((os_ver.dwMajorVersion >= 6) && (argc <= 1 || wcscmp(argv[1], L"/UAC"))) {
		// send non-elevated drive list to the elevated process
		_snwprintf(txt, _countof(txt) - 1, L"/UAC %d %s", GetLogicalDrives(), cmdline_ptr);
#ifdef DEBUG_InCMD
#else
		ShellExecute(NULL, L"runas", argv[0], txt, NULL, SW_SHOWDEFAULT);
		ExitProcess(0);
#endif
	}

	g_isJSON = FALSE;
	WCHAR wcJSON[2048] = { 0 };
	if (argc >= 3 && (0 == wcscmp(argv[1], L"/JSON")))
	{
		g_isJSON = TRUE;
		_snwprintf(wcJSON, _countof(wcJSON) - 1, L"%s", argv[2]);
	}
	if (argc >= 5 && (0 == wcscmp(argv[3], L"/JSON")))
	{
		g_isJSON = TRUE;
		_snwprintf(wcJSON, _countof(wcJSON) - 1, L"%s", argv[4]);
	}
	if (argc >= 6 && (0 == wcscmp(argv[4], L"/JSON")))
	{
		g_isJSON = TRUE;
		_snwprintf(wcJSON, _countof(wcJSON) - 1, L"%s", argv[5]);
	}

	// get registry values
	data_size = sizeof mount_point;
	RegQueryValueEx(registry_key, L"MountPoint", NULL, NULL, (void*)&mount_point, &data_size);
	data_size = sizeof mountdir;
	RegQueryValueEx(registry_key, L"MountDir", NULL, NULL, (void*)mountdir, &data_size);
	data_size = sizeof show_explorer;
	RegQueryValueEx(registry_key, L"ShowExplorer", NULL, NULL, (void*)&show_explorer, &data_size);

	if (RegOpenKeyExA(HKEY_LOCAL_MACHINE, "SOFTWARE\\Microsoft\\.NETFramework\\v4.0.30319", 0, KEY_QUERY_VALUE | KEY_WOW64_64KEY, &h_key) == ERROR_SUCCESS) {
		RegCloseKey(h_key);
		net_installed = TRUE;
	}

	cmdline_ptr = L"";
	exe_path = argv[0];

	const int ciArgc = argc;

	while (--argc > 0) {
		argv++;
		if (argv[0][0] == '/') {
			opt = argv[0] + 1;
			if (!wcscmp(opt, L"UAC")) {
				if (argc < 3) ExitProcess(1);
				mask0 = _wtoi(argv[1]);
				argc -= 2;
				argv += 2;
				continue;
			}
			if (!_wcsnicmp(opt, L"MP=", 3)) {
				opt += 3;
				if (*opt && opt[1] == ':' && !opt[2])
					cmdline_drive_letter = *opt;
				else {
					mount_point = TRUE;
					wcsncpy(mountdir, opt, _countof(mountdir) - 1);
				}
				continue;
			}
			if (!_wcsicmp(opt, L"RO")) {
				readonly = TRUE;
				continue;
			}
			if (!_wcsicmp(opt, L"REM")) {
				removable = TRUE;
				continue;
			}
			if (!_wcsicmp(opt, L"HD")) {
				cmdline_dev_type = 1;
				continue;
			}
			if (!_wcsicmp(opt, L"CD")) {
				cmdline_dev_type = 2;
				continue;
			}
			if (!_wcsicmp(opt, L"FP")) {
				cmdline_dev_type = 3;
				continue;
			}
			if (!_wcsnicmp(opt, L"P=", 2)) {
				cmdline_partition = _wtoi(opt + 2);
				cmdline_partition_exist = TRUE;
				continue;
			}
			if (!_wcsicmp(opt, L"MOUNT")) {
				cmdline_mount = TRUE;
				continue;
			}
			if (!_wcsicmp(opt, L"H") || !wcscmp(opt, L"?")) {
				MessageBoxA(NULL, "MountImg.exe file_name [/MP=mount_point] [/RO] [/REM] [/HD | /CD | /FP] [/P=partition_number] [/MOUNT]", "ImDisk", MB_ICONINFORMATION);
				ExitProcess(0);
			}
		} else
			cmdline_ptr = argv[0];
	}

	if (g_isJSON)
	{
		// 正式JSON数据解析
		printf("🔍 开始正式 JSON 解析...\n");

		// 获取 JSON 字符串
		WCHAR* jsonString = NULL;
		if (ciArgc >= 3) {
			jsonString = wcJSON;
			printf("📥 JSON 字符串: %S\n", jsonString);
		} else {
			printf("❌ 错误: 缺少 JSON 参数\n");
			return 1;
		}

		// 使用简单变量进行 JSON 解析
		WCHAR json_file_path[MAX_PATH] = {0};
		WCHAR json_drive[8] = {0};
		BOOL json_readonly = FALSE;
		int json_partition = 1;

		printf("📋 开始解析各个字段...\n");

		// 使用正式 JSON 解析器解析各个字段

		// 1. 解析 file_path 字段 (对应 ID_COMBO1)
		if (parseJsonValue(jsonString, L"file_path", json_file_path, MAX_PATH, NULL, NULL)) {
			wcscpy_s(filename, MAX_PATH, json_file_path);
			printf("✓ filename (ID_COMBO1) 解析成功: %S\n", filename);
		} else {
			printf("❌ file_path 字段解析失败\n");
		}

		// 2. 解析 readonly 字段 (对应 ID_CHECK1)
		if (parseJsonValue(jsonString, L"readonly", NULL, 0, &json_readonly, NULL)) {
			readonly = json_readonly;
			printf("✓ readonly (ID_CHECK1) 解析成功: %s\n", readonly ? "true" : "false");
		} else {
			printf("❌ readonly 字段解析失败\n");
		}

		// 3. 解析 drive 字段 (对应 ID_COMBO2)
		if (parseJsonValue(jsonString, L"drive", json_drive, 8, NULL, NULL)) {
			wcscpy_s(drive, MAX_PATH + 2, json_drive);
			printf("✓ drive (ID_COMBO2) 解析成功: %S\n", drive);

			// 模拟 ID_COMBO2 驱动器下拉框数据显示修改
			printf("🔧 模拟 ID_COMBO2 驱动器选择变更...\n");

			// 验证驱动器格式
			if (wcslen(drive) >= 2 && drive[1] == L':') {
				WCHAR driveLetter = towupper(drive[0]);
				if (driveLetter >= L'A' && driveLetter <= L'Z') {
					printf("  ✓ 驱动器格式验证通过: %C:\n", driveLetter);

					// 检查驱动器是否已被占用
					WCHAR driveRoot[4];
					swprintf_s(driveRoot, 4, L"%C:\\", driveLetter);
					UINT driveType = GetDriveType(driveRoot);

					if (driveType == DRIVE_NO_ROOT_DIR) {
						printf("  ✓ 驱动器 %C: 可用 (未被占用)\n", driveLetter);
					} else {
						printf("  ⚠️ 警告: 驱动器 %C: 已被占用 (类型: %d)\n", driveLetter, driveType);
					}

					// 更新全局驱动器变量
					drive[0] = driveLetter;
					drive[1] = L':';
					drive[2] = L'\0';

					// 根据 drive 更新 ID_COMBO2 当前项
					printf("  🔄 更新 ID_COMBO2 当前选中项...\n");

					// 模拟查找驱动器在下拉框中的索引
					g_iCurDriveIndex = -1;
					WCHAR availableDrives[26] = {0};
					int driveCount = 0;

					// 参考原始代码的驱动器枚举逻辑
					printf("    Enumerating available drives (using original logic):\n");

					DWORD mask = GetLogicalDrives();
					WCHAR txt[8];
					wcscpy_s(txt, 8, L"\\\\.\\A:");
					DWORD mask_bit = 1;

					for (WCHAR c = L'A'; c <= L'Z'; c++) {
						HANDLE h = CreateFile(txt, 0, FILE_SHARE_READ | FILE_SHARE_WRITE, NULL, OPEN_EXISTING, FILE_ATTRIBUTE_NORMAL, NULL);
						BOOL is_imdisk = FALSE;
						DWORD version, data_size;

						// 检查是否为 ImDisk 设备
						if (h != INVALID_HANDLE_VALUE) {
							is_imdisk = DeviceIoControl(h, IOCTL_IMDISK_QUERY_VERSION, NULL, 0, &version, sizeof(version), &data_size, NULL);
							CloseHandle(h);
						}

						// 判断是否应该添加到可用列表
						BOOL shouldAdd = FALSE;
						const WCHAR* statusStr = L"";

						if (!(mask & mask_bit)) {
							// 驱动器不存在 (可用)
							shouldAdd = TRUE;
							statusStr = L"Available";
						} else if (is_imdisk) {
							// ImDisk 虚拟驱动器 (可重用)
							shouldAdd = TRUE;
							statusStr = L"ImDisk";
						} else {
							// 已占用的驱动器
							shouldAdd = FALSE;
							statusStr = L"Occupied";
						}

						printf("      %C: - %S (Mask: %s, ImDisk: %s)\n",
							   c, statusStr,
							   (mask & mask_bit) ? "1" : "0",
							   is_imdisk ? "Yes" : "No");

						// 添加可用驱动器到列表
						if (shouldAdd) {
							availableDrives[driveCount] = c;
							if (c == driveLetter) {
								g_iCurDriveIndex = driveCount;
								printf("      * Found target drive %C: at index %d\n", c, driveCount);
							}
							driveCount++;
						}

						// 移动到下一个驱动器
						mask_bit <<= 1;
						txt[4]++;  // 从 A: 到 B: 到 C: ...
					}

					printf("    Available drives: ");
					for (int i = 0; i < driveCount; i++) {
						printf("%C: ", availableDrives[i]);
					}
					printf("(Total: %d)\n", driveCount);

					if (g_iCurDriveIndex >= 0) {
						printf("    ✓ 找到驱动器 %C: 在列表中的位置: 索引 %d\n", driveLetter, g_iCurDriveIndex);
						printf("    ✓ ID_COMBO2 当前选中项设置为: 索引 %d (%C:)\n", g_iCurDriveIndex, driveLetter);

						// 实现 ID_COMBO2 选择变更事件处理
						printf("    📡 执行: SendMessage(ID_COMBO2, CB_SETCURSEL, %d, 0)\n", g_iCurDriveIndex);

						// 真正实现 CB_SETCURSEL 功能
						static int combo2_current_selection = -1;  // 模拟当前选中索引
						static WCHAR combo2_items[26][4];          // 模拟下拉框项目列表
						static int combo2_item_count = 0;         // 模拟项目数量

						// 初始化下拉框项目列表
						if (combo2_item_count == 0) {
							printf("    🔧 初始化 ID_COMBO2 下拉框项目...\n");
							for (int i = 0; i < driveCount; i++) {
								swprintf_s(combo2_items[i], 4, L"%C:", availableDrives[i]);
								combo2_item_count++;
								printf("      项目[%d]: %S\n", i, combo2_items[i]);
							}
							printf("    ✓ 下拉框初始化完成，共 %d 个项目\n", combo2_item_count);
						}

						// 执行真正的 CB_SETCURSEL 操作
						printf("    🔍 查找 ID_COMBO2 控件句柄...\n");

						//// 获取主对话框句柄 (假设当前有活动窗口)
						//HWND hMainDialog = GetActiveWindow();
						//if (!hMainDialog) {
						//	hMainDialog = GetForegroundWindow();
						//}

						//HWND hCombo2 = NULL;
						//if (hMainDialog) {
						//	// 通过 ID_COMBO2 获取控件句柄
						//	hCombo2 = GetDlgItem(hMainDialog, ID_COMBO2);
						//	printf("    📍 主对话框句柄: 0x%p\n", hMainDialog);
						//	printf("    📍 ID_COMBO2 句柄: 0x%p\n", hCombo2);
						//} else {
						//	printf("    ⚠️ 警告: 无法获取主对话框句柄\n");
						//}

						//if (hCombo2 && driveIndex >= 0 && driveIndex < combo2_item_count) {
						//	printf("    📡 执行: SendMessage(0x%p, CB_SETCURSEL, %d, 0)\n", hCombo2, driveIndex);

						//	// 真正执行 CB_SETCURSEL 操作
						//	LRESULT result = SendMessage(hCombo2, CB_SETCURSEL, (WPARAM)driveIndex, 0);

						//	if (result != CB_ERR) {
						//		printf("    ✅ CB_SETCURSEL 执行成功:\n");
						//		printf("      - 设置索引: %d\n", driveIndex);
						//		printf("      - 返回值: %ld (成功)\n", result);

						//		// 验证设置结果
						//		LRESULT currentSel = SendMessage(hCombo2, CB_GETCURSEL, 0, 0);
						//		printf("    🔍 验证设置结果:\n");
						//		printf("      - CB_GETCURSEL() 返回: %ld\n", currentSel);

						//		// 获取当前选中项的文本
						//		WCHAR currentText[256];
						//		LRESULT textResult = SendMessage(hCombo2, CB_GETLBTEXT, (WPARAM)currentSel, (LPARAM)currentText);
						//		if (textResult != CB_ERR) {
						//			printf("      - 当前显示文本: %S\n", currentText);

						//			// 更新全局变量以保持同步
						//			wcscpy_s(drive, MAX_PATH + 2, currentText);
						//			printf("      - 全局 drive 变量: %S\n", drive);
						//		} else {
						//			printf("      - 获取文本失败\n");
						//		}

						//		// 更新模拟状态
						//		combo2_current_selection = (int)currentSel;

						//	} else {
						//		printf("    ❌ CB_SETCURSEL 执行失败:\n");
						//		printf("      - 返回值: CB_ERR (-1)\n");
						//		printf("      - 可能原因: 索引无效或控件状态异常\n");
						//	}

						//} else if (!hCombo2) {
						//	printf("    ❌ CB_SETCURSEL 执行失败:\n");
						//	printf("      - 错误: 无法获取 ID_COMBO2 控件句柄\n");
						//	printf("      - 原因: 控件可能不存在或对话框未创建\n");

						//	// 回退到模拟模式
						//	printf("    🔄 回退到模拟模式...\n");
						//	if (driveIndex >= 0 && driveIndex < combo2_item_count) {
						//		combo2_current_selection = driveIndex;
						//		wcscpy_s(drive, MAX_PATH + 2, combo2_items[driveIndex]);
						//		printf("      - 模拟设置索引: %d\n", driveIndex);
						//		printf("      - 模拟设置文本: %S\n", combo2_items[driveIndex]);
						//	}

						//} else {
						//	printf("    ❌ CB_SETCURSEL 执行失败:\n");
						//	printf("      - 错误: 索引 %d 超出有效范围 [0, %d]\n", driveIndex, combo2_item_count - 1);
						//}



						// 模拟 ID_COMBO2 选择变更的实际处理逻辑
						// 参考原始代码: wcscpy(drive, drive_list[SendMessage(hwnd_combo2, CB_GETCURSEL, 0, 0)]);
						printf("    🔔 触发: ID_COMBO2 选择变更事件处理\n");

						// 构建驱动器字符串 (格式: "X:")
						WCHAR selectedDrive[4];
						swprintf_s(selectedDrive, 4, L"%C:", driveLetter);

						// 更新全局 drive 变量 (模拟从 drive_list 获取)
						wcscpy_s(drive, MAX_PATH + 2, selectedDrive);
						printf("    📝 drive 变量更新为: %S\n", drive);

						// 模拟界面状态同步
						printf("    🔄 同步界面状态:\n");
						printf("      - 驱动器下拉框当前选中: 索引 %d\n", g_iCurDriveIndex);
						printf("      - 驱动器下拉框显示文本: %S\n", selectedDrive);
						printf("      - 全局 drive 变量: %S\n", drive);

						// 模拟相关控件状态更新
						printf("    ⚙️ 更新相关控件状态:\n");
						printf("      - 检查挂载点选项状态\n");
						printf("      - 更新确定按钮可用性\n");
						printf("      - 刷新界面显示\n");

						printf("    ✅ ID_COMBO2 选择变更事件处理完成\n");
					} else {
						printf("    ⚠️ 警告: 驱动器 %C: 不在可用列表中 (可能已被占用)\n", driveLetter);

						// 强制设置模式 (驱动器不在可用列表中)
						printf("    🔧 启用强制设置模式:\n");

						// 直接设置驱动器文本
						WCHAR forceDrive[4];
						swprintf_s(forceDrive, 4, L"%C:", driveLetter);
						wcscpy_s(drive, MAX_PATH + 2, forceDrive);

						printf("    📝 强制设置 drive 变量为: %S\n", drive);
						printf("    📝 强制设置 ID_COMBO2 文本为: %S\n", forceDrive);
						printf("    ⚠️ 注意: 此驱动器可能已被占用，挂载时可能失败\n");
					}

					printf("  ✓ ID_COMBO2 当前项更新完成\n");

					printf("  ✓ 驱动器设置完成: %S\n", drive);
				} else {
					printf("  ❌ 错误: 无效的驱动器字母 '%C'\n", drive[0]);
				}
			} else {
				printf("  ❌ 错误: 驱动器格式无效 '%S' (应为 'X:' 格式)\n", drive);
			}

			printf("✓ ID_COMBO2 驱动器选择响应完成\n");
		} else {
			printf("❌ drive 字段解析失败\n");
		}

		// 4. 解析 partition 字段 (对应 ID_LISTVIEW)
		if (parseJsonValue(jsonString, L"partition", NULL, 0, NULL, &json_partition)) {
			partition = json_partition;
			printf("✓ partition (ID_LISTVIEW) 解析成功: %d\n", partition);
		} else {
			printf("❌ partition 字段解析失败\n");
		}

		// 验证解析结果
		printf("\n📊 JSON 解析结果汇总:\n");
		printf("  file_path: %S\n", json_file_path);
		printf("  drive: %S\n", json_drive);
		printf("  readonly: %s\n", json_readonly ? "true" : "false");
		printf("  partition: %d\n", json_partition);

		// 模拟 ID_PBUTTON1 选择挂载文件按钮响应
		printf("\n🔧 模拟 ID_PBUTTON1 按钮响应...\n");
		if (filename[0]) {
			// 执行 file_check() 函数的核心逻辑
			printf("执行文件检查和设备类型检测...\n");

			// 获取文件扩展名并设置设备类型
			WCHAR *ext = PathFindExtension(filename);
			dev_type = !_wcsicmp(ext, L".iso") || !_wcsicmp(ext, L".nrg") || !_wcsicmp(ext, L".bin");
			printf("文件扩展名: %S\n", ext);

			// 检查是否为软盘格式
			WIN32_FILE_ATTRIBUTE_DATA file_data;
			BOOL floppy_ok = FALSE;
			if (GetFileAttributesEx(filename, GetFileExInfoStandard, &file_data) && !file_data.nFileSizeHigh) {
				DWORD floppy_sizes[] = {160*1024, 180*1024, 320*1024, 360*1024, 640*1024, 1200*1024, 720*1024, 820*1024, 1440*1024, 1680*1024, 1722*1024, 2880*1024, 123264*1024, 234752*1024};
				for (int i = 0; i < sizeof(floppy_sizes)/sizeof(DWORD); i++) {
					if (floppy_sizes[i] == file_data.nFileSizeLow) {
						floppy_ok = TRUE;
						break;
					}
				}
			}
			if (floppy_ok || !_wcsicmp(ext, L".vfd")) {
				dev_type = 2; // 软盘
			}

			printf("✓ 设备类型检测完成: %d (%s)\n", dev_type,
			       dev_type == 0 ? "硬盘" : (dev_type == 1 ? "CD/DVD" : "软盘"));

			// 设置其他默认参数
			removable = FALSE;
			win_boot = FALSE;
			device_number = -1;
			new_file = FALSE;

			printf("✓ ID_PBUTTON1 响应模拟完成\n");
		} else {
			printf("❌ filename 为空，跳过 ID_PBUTTON1 响应\n");
		}

		printf("\n✅ JSON 解析和界面控件设置完成\n");
		printf("========================================\n");
	}
	else
	{
		wcsncpy(filename, cmdline_ptr, _countof(filename) - 1);
	}

	hinst = GetModuleHandle(NULL);
	if (!(h_cpl = LoadLibraryA("imdisk.cpl")))
		MessageBoxA(NULL, "Warning: cannot find imdisk.cpl.", "ImDisk", MB_ICONWARNING);
	ImDisk_GetDeviceListEx = GetProcAddress(h_cpl, "ImDiskGetDeviceListEx");
	ImDisk_ForceRemoveDevice = GetProcAddress(h_cpl, "ImDiskForceRemoveDevice");
	ImDisk_RemoveRegistrySettings = GetProcAddress(h_cpl, "ImDiskRemoveRegistrySettings");
	ImDisk_NotifyShellDriveLetter = GetProcAddress(h_cpl, "ImDiskNotifyShellDriveLetter");

	// create service
	h_scman = OpenSCManager(NULL, NULL, SC_MANAGER_CONNECT | SC_MANAGER_CREATE_SERVICE);
	h_svc = OpenService(h_scman, L"ImDiskImg", SERVICE_CHANGE_CONFIG | SERVICE_START | SERVICE_STOP | DELETE);
	wcscat(module_name, L" /SVC");
	if (h_svc)
		ChangeServiceConfig(h_svc, SERVICE_WIN32_OWN_PROCESS, SERVICE_NO_CHANGE, SERVICE_ERROR_NORMAL, module_name, NULL, NULL, L"ImDisk\0", NULL, NULL, NULL);
	else {
		h_svc = CreateService(h_scman, L"ImDiskImg", L"ImDisk Image File mounter", SERVICE_CHANGE_CONFIG | SERVICE_START | SERVICE_STOP | DELETE, SERVICE_WIN32_OWN_PROCESS, SERVICE_AUTO_START, SERVICE_ERROR_NORMAL,
							  module_name, NULL, NULL, L"ImDisk\0", NULL, NULL);
		if (h_svc) {
			svc_description.lpDescription = L"Mounts image files at system startup.";
			ChangeServiceConfig2(h_svc, SERVICE_CONFIG_DESCRIPTION, &svc_description);
		} else
			MessageBox(NULL, t[ERR_6], L"ImDisk", MB_ICONERROR);
	}

	mount_mutex = CreateMutex(NULL, FALSE, NULL);
	PathRemoveFileSpec(exe_path);
	SetCurrentDirectory(exe_path);
	load_lang();

	// 根据 JSON 模式决定显示方式
	if (g_isJSON) {
		printf("🔇 JSON 模式：启动后台执行模式\n");

		// 创建隐藏对话框
		g_hiddenDialog = CreateDialog(hinst, L"MOUNT_DLG", NULL, DlgProc);
		if (g_hiddenDialog) {
			// 完全隐藏窗口
			ShowWindow(g_hiddenDialog, SW_HIDE);
			SetWindowPos(g_hiddenDialog, NULL, -32000, -32000, 0, 0,
						SWP_NOSIZE | SWP_NOZORDER | SWP_NOACTIVATE);

			// 设置为工具窗口 (不在任务栏显示)
			SetWindowLong(g_hiddenDialog, GWL_EXSTYLE,
						 GetWindowLong(g_hiddenDialog, GWL_EXSTYLE) | WS_EX_TOOLWINDOW);

			printf("✅ 隐藏对话框创建成功，开始后台处理\n");

			// 后台消息循环
			MSG msg;
			while (GetMessage(&msg, NULL, 0, 0)) {
				if (!IsDialogMessage(g_hiddenDialog, &msg)) {
					TranslateMessage(&msg);
					DispatchMessage(&msg);
				}

				// 检查操作是否完成
				if (g_operationCompleted) {
					printf("✅ 后台操作完成，退出消息循环\n");
					break;
				}
			}

			DestroyWindow(g_hiddenDialog);
			g_hiddenDialog = NULL;
		} else {
			printf("❌ 创建隐藏对话框失败\n");
		}
	} else {
		printf("🖥️ 普通模式：显示界面\n");
		if (DialogBox(hinst, L"MOUNT_DLG", NULL, DlgProc) == 1) {
			// workaround: the window of the driver GUI sometimes disappears under other windows
			for (i = 0; i < 100; i++) {
				Sleep(50);
				if ((hwnd = FindWindow(NULL, L"Mount new virtual disk")) || (hwnd = FindWindow(NULL, L"Select partition in disk image"))) {
					SetForegroundWindow(hwnd);
					break;
				}
			}
		}
	}

	// remove service if not required
	max = get_reg_max();
	for (i = 0; i <= max; i++) {
		param_name_ptr = param_name + _snwprintf(param_name, _countof(param_name), L"Im%d", i);
		wcscpy(param_name_ptr, L"FileName");
		if (RegQueryValueEx(registry_key, param_name, NULL, NULL, NULL, NULL) == ERROR_SUCCESS) {
			wcscpy(param_name_ptr, L"Device");
			if (RegQueryValueEx(registry_key, param_name, NULL, NULL, NULL, NULL) == ERROR_FILE_NOT_FOUND) {
				svc_required = TRUE;
				break;
			}
		}
	}
	if (!svc_required) DeleteService(h_svc);
	CloseServiceHandle(h_svc);
	CloseServiceHandle(h_scman);

	RegCloseKey(registry_key);
	WaitForSingleObject(mount_mutex, 10000);
	ExitProcess(0);
}
