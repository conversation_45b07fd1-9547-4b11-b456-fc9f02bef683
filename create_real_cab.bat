@echo off
echo Creating real Microsoft CAB file with complete structure...

rem Clean up
if exist files.cab del files.cab
if exist files.ddf del files.ddf

echo Step 1: Creating DDF file with proper syntax...

rem Create DDF header
echo .OPTION EXPLICIT > files.ddf
echo .Set CabinetNameTemplate=files.cab >> files.ddf
echo .Set DiskDirectoryTemplate=. >> files.ddf
echo .Set CompressionType=MSZIP >> files.ddf
echo .Set UniqueFiles=OFF >> files.ddf
echo .Set Cabinet=ON >> files.ddf
echo .Set Compress=ON >> files.ddf
echo .Set SourceDir=files >> files.ddf
echo. >> files.ddf

echo Step 2: Adding main files to DDF...
rem Add main files (they will be in root of CAB)
for %%f in (files\*.exe files\*.dll files\*.lnk files\*.txt files\*.inf files\*.rpt) do (
    echo "%%~nxf" >> files.ddf
)

echo Step 3: Adding driver directory files to DDF...
rem Add driver directory files
for /r files\driver %%f in (*.*) do (
    set "filepath=%%f"
    setlocal enabledelayedexpansion
    set "relpath=!filepath:*files\=!"
    echo "!relpath!" >> files.ddf
    endlocal
)

echo Step 4: Adding lang directory files to DDF...
rem Add lang directory files
for /r files\lang %%f in (*.*) do (
    set "filepath=%%f"
    setlocal enabledelayedexpansion
    set "relpath=!filepath:*files\=!"
    echo "!relpath!" >> files.ddf
    endlocal
)

echo Step 5: Showing DDF content (first 20 lines)...
powershell -Command "Get-Content files.ddf | Select-Object -First 20"

echo Step 6: Creating CAB file with makecab...
makecab /F files.ddf

if %ERRORLEVEL% EQU 0 (
    echo SUCCESS: CAB file created!
    
    echo Step 7: Testing CAB file...
    mkdir test_cab
    extrac32.exe /e /l test_cab files.cab
    
    if exist test_cab\config.exe (
        echo SUCCESS: config.exe found in root!
    ) else (
        echo ERROR: config.exe not found in root
    )
    
    if exist test_cab\driver (
        echo SUCCESS: driver directory found!
    ) else (
        echo ERROR: driver directory missing
    )
    
    if exist test_cab\lang (
        echo SUCCESS: lang directory found!
    ) else (
        echo ERROR: lang directory missing
    )
    
    echo File count in CAB:
    dir test_cab /s /b | find /c /v ""
    
    echo CAB file size:
    dir files.cab
    
    echo Testing with Windows expand...
    expand -D files.cab > expand_test.txt 2>&1
    if %ERRORLEVEL% EQU 0 (
        echo SUCCESS: Windows expand can read CAB!
        echo First 10 files in CAB:
        powershell -Command "Get-Content expand_test.txt | Select-Object -First 10"
    ) else (
        echo WARNING: Windows expand has issues
        type expand_test.txt
    )
    
    rmdir /s /q test_cab 2>nul
    del expand_test.txt 2>nul
    
) else (
    echo ERROR: makecab failed
    echo Checking DDF file for errors...
    echo Last 10 lines of DDF:
    powershell -Command "Get-Content files.ddf | Select-Object -Last 10"
)

echo Step 8: Cleaning up...
del files.ddf 2>nul

echo Done!
pause
