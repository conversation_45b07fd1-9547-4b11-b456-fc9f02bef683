@echo off
rem 使用Windows原生COM对象解压ZIP文件
rem 这是Windows XP及以上系统都支持的原生方法

if not "%1"=="7" start /min cmd /c ""%~0" 7 %*" & exit /b
set F=%TEMP%\DiskUp%TIME::=%

rem 使用Windows原生的Shell.Application COM对象解压
rem 这是Windows内置的功能，不需要外部工具
mkdir "%F%"
mshta "javascript:var s=new ActiveXObject('Shell.Application');s.NameSpace('%F%').CopyHere(s.NameSpace('%~dp0files.zip').Items(),20);close();"

rem 等待解压完成
ping 127.0.0.1 -n 3 >nul

"%F%\config.exe" %2 %3 %4
rd /s /q "%F%"
