@echo off
chcp 65001 >nul
echo ========================================
echo Testing Detailed Error Information
echo ========================================

echo.
echo 这个测试验证 StartService 详细错误信息的输出
echo.
echo 添加的错误信息包括:
echo 1. 错误代码 (十进制和十六进制)
echo 2. 系统错误消息 (FormatMessage)
echo 3. 错误描述 (常见错误的详细说明)
echo 4. 服务句柄状态
echo 5. 命令行参数详情
echo 6. 服务状态查询结果
echo.

echo 错误信息输出格式:
echo ✅ StartService 成功时:
echo   - 正常执行挂载流程
echo   - 输出服务状态信息
echo.
echo ❌ StartService 失败时:
echo   - Error Code: [code] (0x[hex])
echo   - System Message: [FormatMessage result]
echo   - Error Description: [detailed explanation]
echo   - Service Handle: 0x[handle]
echo   - Arguments Count: 3
echo   - Command Line 1/2/Drive: [actual parameters]
echo.

echo 常见错误码说明:
echo ERROR_ACCESS_DENIED (5): 需要管理员权限
echo ERROR_INVALID_HANDLE (6): 服务句柄无效
echo ERROR_SERVICE_ALREADY_RUNNING (1056): 服务已在运行
echo ERROR_SERVICE_DISABLED (1058): 服务被禁用
echo ERROR_SERVICE_DOES_NOT_EXIST (1060): 服务不存在
echo ERROR_PATH_NOT_FOUND (3): 服务可执行文件路径未找到
echo.

echo 启动 VirtualDiskTool32.exe 测试详细错误信息...
echo ----------------------------------------

VirtualDiskTool32.exe --test-mount

echo.
echo ----------------------------------------
echo 程序执行完毕，退出码: %ERRORLEVEL%
echo.

if %ERRORLEVEL% EQU 0 (
    echo ✅ SUCCESS: 程序正常运行，检查调试输出中的详细信息
) else (
    echo ❌ FAILED: 程序运行出错，退出码: %ERRORLEVEL%
    echo 检查调试输出中的详细错误信息
)

echo.
echo 检查目标 X: 驱动器...
if exist X:\ (
    echo ✅ SUCCESS: X: 驱动器已挂载
    echo 目录列表:
    dir X: /w
) else (
    echo ❌ FAILED: X: 驱动器未挂载
    echo 这可能是正常的，如果 ImDisk 挂载成功就不会尝试 DiscUtils
)

echo.
echo ========================================
echo 详细错误信息实现技术说明:
echo ========================================
echo.
echo ✅ GetStartServiceErrorDescription() 函数:
echo   提供常见 StartService 错误码的详细说明
echo   包括权限、句柄、服务状态等各种错误情况
echo   返回用户友好的错误描述
echo.
echo ✅ FormatMessage() 系统错误消息:
echo   使用 FORMAT_MESSAGE_FROM_SYSTEM 获取系统错误消息
echo   支持多语言环境 (LANG_NEUTRAL, SUBLANG_DEFAULT)
echo   转换为 UTF-8 编码用于调试输出
echo.
echo ✅ 服务状态查询 (QueryServiceStatus):
echo   SERVICE_STOPPED: 服务已停止
echo   SERVICE_START_PENDING: 服务正在启动
echo   SERVICE_RUNNING: 服务正在运行
echo   SERVICE_STOP_PENDING: 服务正在停止
echo   SERVICE_PAUSED: 服务已暂停
echo.
echo ✅ 详细的调试输出:
echo   错误代码: 十进制和十六进制格式
echo   系统消息: Windows 提供的标准错误描述
echo   错误描述: 针对 StartService 的具体说明
echo   服务句柄: 显示句柄地址，便于验证有效性
echo   命令行参数: 显示传递给服务的具体参数
echo.
echo ✅ 服务初始化过程的详细日志:
echo   OpenSCManager 结果
echo   OpenService 结果和服务状态
echo   CreateService 过程 (如果需要)
echo   服务配置设置结果
echo.
echo ✅ 错误处理层次:
echo   1. OpenSCManager 失败 → 权限或系统问题
echo   2. OpenService 失败 → 服务不存在，尝试创建
echo   3. CreateService 失败 → 创建权限或配置问题
echo   4. StartService 失败 → 服务启动问题 (详细分析)
echo   5. QueryServiceStatus 失败 → 服务状态查询问题
echo.
echo ✅ 调试信息的价值:
echo   - 快速定位问题根因
echo   - 提供解决方案建议
echo   - 便于技术支持和维护
echo   - 减少问题诊断时间
echo.

pause
