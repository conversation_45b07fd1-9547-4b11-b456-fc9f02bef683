========================================
磁盘挂载工具自动解压运行脚本使用说明
========================================

本目录包含三个脚本文件，用于自动解压files.zip并运行config.exe：

1. ExtractAndRun.bat    - 完整功能版本，支持多种解压方法
2. RunDiskTool.bat      - 简化版本，专门针对XP系统优化
3. RunDiskTool.ps1      - PowerShell版本，适用于Windows 7及以上

========================================
系统兼容性
========================================

Windows XP/2003:
- 推荐使用: RunDiskTool.bat
- 备选方案: ExtractAndRun.bat

Windows Vista/7/8/10/11:
- 推荐使用: RunDiskTool.ps1
- 备选方案: ExtractAndRun.bat 或 RunDiskTool.bat

========================================
使用方法
========================================

方法1: 直接双击运行
- 双击任意脚本文件即可自动解压并运行config.exe

方法2: 命令行运行
- 打开命令提示符
- 切换到脚本所在目录
- 执行: RunDiskTool.bat [参数]
- 或执行: powershell -ExecutionPolicy Bypass -File RunDiskTool.ps1 [参数]

方法3: 带参数运行
- RunDiskTool.bat install
- RunDiskTool.bat uninstall
- 等等...

========================================
工作流程
========================================

1. 检查files.zip文件是否存在
2. 清理旧的临时目录 %TEMP%\DiskUp
3. 创建新的临时目录
4. 解压files.zip到临时目录
5. 查找并验证config.exe
6. 运行config.exe（传递所有命令行参数）
7. 询问是否清理临时文件
8. 退出并返回config.exe的退出代码

========================================
解压方法说明
========================================

ExtractAndRun.bat 支持多种解压方法：
1. PowerShell解压 (Windows 7+)
2. VBScript解压 (XP兼容)
3. expand命令解压 (备用方案)

RunDiskTool.bat 使用：
- VBScript解压 (XP兼容，最稳定)

RunDiskTool.ps1 使用：
- .NET Framework ZIP解压 (最快速)

========================================
故障排除
========================================

如果脚本无法运行：

1. 检查files.zip文件是否存在且完整
2. 确保有足够的磁盘空间
3. 检查临时目录权限
4. 尝试以管理员身份运行

常见错误：
- "找不到files.zip文件" - 确保ZIP文件在脚本同目录
- "解压失败" - 检查ZIP文件是否损坏
- "找不到config.exe" - 检查ZIP文件内容是否正确

========================================
技术细节
========================================

临时目录: %TEMP%\DiskUp
- Windows XP: C:\Documents and Settings\用户名\Local Settings\Temp\DiskUp
- Windows 7+: C:\Users\<USER>\AppData\Local\Temp\DiskUp

支持的ZIP格式:
- 标准ZIP格式
- 无密码保护
- 支持子目录结构

参数传递:
- 所有命令行参数都会传递给config.exe
- 支持带空格的参数（需要用引号包围）

========================================
安全说明
========================================

1. 脚本会在临时目录解压文件，使用完毕后可选择清理
2. 不会修改系统文件或注册表（除非config.exe本身这样做）
3. 建议在运行前检查files.zip的来源和完整性
4. 如有安全软件报警，请确认文件来源后添加信任

========================================
版本信息
========================================

创建日期: 2025年7月22日
兼容系统: Windows XP SP3 及以上
测试环境: Windows XP, 7, 10, 11
脚本编码: UTF-8 (支持中文路径和文件名)

========================================
