﻿/*
 * privilege_manager.h
 * VirtualDiskTool 权限管理模块
 * 
 * 功能：检测和提升程序权限
 * 参考：MountImg.c 中的权限提升实现
 */

#ifndef PRIVILEGE_MANAGER_H
#define PRIVILEGE_MANAGER_H

#include <windows.h>

#ifdef __cplusplus
extern "C" {
#endif

/*
 * 检查当前进程是否以管理员权限运行
 * 
 * 返回值：
 *   TRUE: 当前以管理员权限运行
 *   FALSE: 当前未以管理员权限运行
 */
BOOL IsRunningAsAdministrator(void);

/*
 * 检查当前操作系统版本是否需要UAC
 * 
 * 返回值：
 *   TRUE: 需要UAC (Vista及以上版本)
 *   FALSE: 不需要UAC (XP及以下版本)
 */
BOOL IsUACRequired(void);

/*
 * 请求管理员权限并重新启动程序
 * 完全参照 MountImg.c 中的实现
 *
 * 参数：
 *   argc: 原始命令行参数数量
 *   argv: 原始命令行参数数组
 *   cmdline_ptr: 命令行参数字符串 (参照 MountImg.c)
 *
 * 返回值：
 *   0: 成功启动提升权限的进程
 *   非0: 失败
 *
 * 注意：成功时此函数不会返回，程序会退出
 */
int RequestAdministratorPrivileges(int argc, wchar_t* argv[], const wchar_t* cmdline_ptr);

/*
 * 检查命令行参数中是否包含UAC标志
 * 
 * 参数：
 *   argc: 命令行参数数量
 *   argv: 命令行参数数组
 * 
 * 返回值：
 *   TRUE: 包含UAC标志，表示已经是提升权限后的进程
 *   FALSE: 不包含UAC标志
 */
BOOL IsElevatedProcess(int argc, wchar_t* argv[]);

/*
 * 从UAC参数中提取原始命令行参数
 * 
 * 参数：
 *   argc: 命令行参数数量
 *   argv: 命令行参数数组
 *   originalCmdLine: 输出缓冲区，存储原始命令行
 *   bufferSize: 缓冲区大小
 * 
 * 返回值：
 *   0: 成功
 *   非0: 失败
 */
int ExtractOriginalCommandLine(int argc, wchar_t* argv[], wchar_t* originalCmdLine, int bufferSize);

/*
 * 获取操作系统版本信息
 * 
 * 参数：
 *   osVersion: 输出的版本信息结构
 * 
 * 返回值：
 *   TRUE: 成功
 *   FALSE: 失败
 */
BOOL GetOSVersion(OSVERSIONINFO* osVersion);

#ifdef __cplusplus
}
#endif

#endif // PRIVILEGE_MANAGER_H
