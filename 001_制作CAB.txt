

cd /d E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20-New_ReBuild_Src\001_Creator_CAB\files
dir /b /s /a-d *.* | powershell -command "$input | % { $_.Substring('E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20-New_ReBuild_Src\001_Creator_CAB\files'.Length+1) }" > E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20-New_ReBuild_Src\001_Creator_CAB\filelist.txt




cd /d E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20-New_ReBuild_Src\001_Creator_CAB
makecab /f package.ddf


cd /d E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20-New_ReBuild_Src\001_Creator_CAB\files
makecab /f 111111.ddf

"C:\Program Files\WinRAR\WinRAR.exe" a -afcab test.cab files\config.exe
"C:\Program Files\WinRAR\WinRAR.exe" a -afcab 666666.cab files


extrac32.exe /e /l "C:\Documents and Settings\Administrator\桌面\001_Work\setup" "C:\Documents and Settings\Administrator\桌面\001_Work\setup\files.cab"

