@echo off
chcp 65001 >nul
echo ========================================
echo 测试 VirtualDiskTool 进程循环问题修复
echo ========================================

echo.
echo 🔍 问题描述：
echo VirtualDiskTool执行后，进程里面开启了好多VirtualDiskTool32.exe进程
echo 一直在增加VirtualDiskTool32.exe进程
echo.

echo 🔧 修复内容：
echo 1. 修复权限提升参数传递问题
echo 2. 修复UAC参数解析逻辑
echo 3. 添加进程ID跟踪调试信息
echo 4. 确保正确的进程退出机制
echo.

echo 📋 修复前的问题：
echo - RequestAdministratorPrivileges() 只传递 /UAC 参数
echo - 缺少 logical_drives 和 cmdline_ptr 参数
echo - 提升后进程无法正确识别自己
echo - 导致无限递归权限提升循环
echo.

echo ✅ 修复后的改进：
echo - 正确传递 /UAC <logical_drives> <cmdline_ptr> 参数
echo - 修复参数解析逻辑 (argc >= 4 检查)
echo - 添加详细的调试信息输出
echo - 确保进程正确退出
echo.

echo 🚀 开始测试修复效果...
echo ----------------------------------------

echo 检查当前进程数量...
tasklist /fi "imagename eq VirtualDiskTool32.exe" | find /c "VirtualDiskTool32.exe" > temp_count_before.txt
set /p COUNT_BEFORE=<temp_count_before.txt
del temp_count_before.txt
echo 测试前 VirtualDiskTool32.exe 进程数量: %COUNT_BEFORE%

echo.
echo 启动 VirtualDiskTool32.exe 进行测试...
echo 注意观察是否会产生多个进程
echo.

echo 执行命令: VirtualDiskTool32.exe --test-mount
echo ----------------------------------------
VirtualDiskTool32.exe --test-mount

echo.
echo ----------------------------------------
echo 程序执行完毕，退出码: %ERRORLEVEL%

echo.
echo 等待3秒后检查进程数量...
timeout /t 3 /nobreak >nul

tasklist /fi "imagename eq VirtualDiskTool32.exe" | find /c "VirtualDiskTool32.exe" > temp_count_after.txt
set /p COUNT_AFTER=<temp_count_after.txt
del temp_count_after.txt
echo 测试后 VirtualDiskTool32.exe 进程数量: %COUNT_AFTER%

echo.
echo 📊 测试结果分析：
echo ----------------------------------------

if %COUNT_AFTER% GTR %COUNT_BEFORE% (
    set /a DIFF=%COUNT_AFTER%-%COUNT_BEFORE%
    echo ❌ FAILED: 检测到新增 !DIFF! 个进程
    echo.
    echo 当前运行的 VirtualDiskTool32.exe 进程：
    tasklist /fi "imagename eq VirtualDiskTool32.exe"
    echo.
    echo 🔍 可能的问题：
    echo 1. 权限提升逻辑仍有问题
    echo 2. 参数解析不正确
    echo 3. 进程退出机制失效
    echo 4. UAC对话框被用户取消
    echo.
    echo 🛠️ 建议检查：
    echo 1. 查看程序输出的调试信息
    echo 2. 检查参数传递是否正确
    echo 3. 确认UAC权限提升是否成功
    echo 4. 验证进程退出逻辑
) else (
    echo ✅ SUCCESS: 没有检测到进程数量增加
    echo 修复成功！进程循环问题已解决
)

echo.
echo 🔍 详细进程信息：
echo ----------------------------------------
echo 当前所有 VirtualDiskTool32.exe 进程：
tasklist /fi "imagename eq VirtualDiskTool32.exe" /fo table

echo.
echo 📋 技术修复总结：
echo ========================================
echo.
echo ✅ 修复1: 参数传递修复
echo   修复前: _snwprintf(txt, _countof(txt) - 1, L"/UAC");
echo   修复后: _snwprintf(txt, _countof(txt) - 1, L"/UAC %%d %%S", logicalDrives, cmdline_ptr);
echo.
echo ✅ 修复2: 参数解析修复
echo   修复前: if (argc >= 3) // 错误的参数数量检查
echo   修复后: if (argc >= 4) // 正确检查 /UAC <drives> <cmdline>
echo.
echo ✅ 修复3: 调试信息增强
echo   - 添加进程ID跟踪
echo   - 添加参数详细输出
echo   - 添加权限检查状态显示
echo.
echo ✅ 修复4: 退出机制确保
echo   - 确保 ExitProcess(0) 正确调用
echo   - 避免进程继续执行导致循环
echo.

echo 🎯 预期行为：
echo 1. Windows XP: 直接执行，不触发UAC
echo 2. Windows Vista+: 弹出UAC对话框，用户确认后执行
echo 3. 无论哪种情况，都不应该产生多个进程
echo.

echo ========================================
echo 进程循环问题修复测试完成
echo ========================================
