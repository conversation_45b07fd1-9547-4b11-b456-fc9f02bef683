@echo off
rem 使用Windows原生extrac32.exe的最终解决方案
rem 完全模仿原始脚本结构

if not "%1"=="7" start /min cmd /c ""%~0" 7 %*" & exit /b
set F=%TEMP%\DiskUp%TIME::=%

rem 将ZIP文件临时重命名为CAB格式，extrac32.exe可以处理ZIP格式的CAB文件
set TEMP_CAB=%TEMP%\files_%TIME::=%.cab
copy "%~dp0files.zip" "%TEMP_CAB%" >nul

rem 使用extrac32.exe解压（与原始脚本完全相同的命令）
extrac32.exe /e /l "%F%" "%TEMP_CAB%"

rem 清理临时CAB文件
del "%TEMP_CAB%" 2>nul

rem 运行config.exe（与原始脚本相同的逻辑）
if exist "%F%\config.exe" (
    "%F%\config.exe" %2 %3 %4
) else (
    for /r "%F%" %%i in (config.exe) do if exist "%%i" "%%i" %2 %3 %4 & goto :cleanup
)

:cleanup
rd /s /q "%F%"
