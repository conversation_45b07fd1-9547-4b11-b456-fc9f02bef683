#include <windows.h>
#include <afxres.h>
#include "resource.h"

1 ICON "..\\VD.ico"

TAB1 DIALOGEX 0, 0, 200, 220
	 FONT 9, "MS Shell Dlg"
	 STYLE DS_SHELLFONT
BEGIN
	GROUPBOX " RamDisk Configuration Tool ", ID_TITLE, 7, 8, 185, 193
	LTEXT "", ID_TEXT1, 22, 206, 180, 9
	LTEXT "Size:", ID_TEXT3, 27, 29, 45, 9
	EDITTEXT ID_EDIT1, 77, 27, 48, 12, ES_NUMBER
	AUTORADIOBUTTON "KB", ID_RB1, 130, 22, 24, 8
	AUTORADIOBUTTON "MB", ID_RB2, 130, 30, 24, 8
	AUTORADIOBUTTON "GB", ID_RB3, 130, 38, 24, 8
	AUTOCHECKBOX "Allocate Memory Dynamically", ID_CHECK1, 27, 49, 150, 15
	LTEXT "Drive Letter:", ID_TEXT4, 27, 81, 70, 9
	COMBOBOX ID_COMBO1, 97, 79, 28, 24, CBS_DROPDOWNLIST | WS_TABSTOP
	PUSHBUTTON "Unmount", ID_PBUTTON3, 132, 79, 48, 13
	LTEXT "File System:", ID_TEXT5, 27, 108, 70, 9
	COMBOBOX ID_COMBO2, 97, 106, 36, 24, CBS_DROPDOWNLIST | WS_TABSTOP
	AUTOCHECKBOX "Launch at Windows Startup", ID_CHECK2, 27, 133, 150, 15
	AUTOCHECKBOX "Create TEMP Folder", ID_CHECK3, 27, 154, 150, 15
	PUSHBUTTON "Set TEMP Variables...", ID_PBUTTON4, 27, 171, 108, 13
END

TAB2 DIALOGEX 0, 0, 200, 220
	 FONT 9, "MS Shell Dlg"
	 STYLE DS_SHELLFONT
BEGIN
	GROUPBOX " RamDisk Configuration Tool ", ID_TITLE, 7, 8, 185, 193
	CONTROL "", IDC_STATIC, "Static", SS_GRAYFRAME, 9, 114, 181, 1
	CONTROL "", IDC_STATIC, "Static", SS_GRAYFRAME, 9, 161, 181, 1
	LTEXT "", ID_TEXT1, 22, 206, 200, 9
	LTEXT "Cluster Size:", ID_TEXT6, 18, 23, 85, 9
	COMBOBOX ID_COMBO3, 18, 33, 55, 24, CBS_DROPDOWNLIST | WS_TABSTOP
	LTEXT "Drive Label:", ID_TEXT7, 105, 23, 85, 9
	EDITTEXT ID_EDIT2, 105, 33, 55, 12, ES_AUTOHSCROLL
	AUTOCHECKBOX "Quick Format", ID_CHECK4, 18, 53, 172, 10
	AUTOCHECKBOX "Enable NTFS Compression", ID_CHECK5, 18, 65, 172, 10
	AUTOCHECKBOX "Use AWE physical memory", ID_CHECK6, 18, 77, 172, 10
	PUSHBUTTON "Dynamic RamDisk Parameters...", ID_PBUTTON9, 18, 93, 132, 13
	AUTOCHECKBOX "Use Mount Point:", ID_CHECK7, 18, 123, 117, 10
	PUSHBUTTON "Unmount", ID_PBUTTON7, 136, 122, 48, 13
	COMBOBOX ID_COMBO4, 16, 138, 150, 24, CBS_DROPDOWN | CBS_AUTOHSCROLL | CBS_SORT | WS_TABSTOP
	PUSHBUTTON "...", ID_PBUTTON8, 168, 138, 16, 13
	LTEXT "Additional Parameters:", ID_TEXT9, 18, 168, 172, 9
	EDITTEXT ID_EDIT4, 16, 178, 150, 12, ES_AUTOHSCROLL
	PUSHBUTTON "...", ID_PBUTTON2, 168, 177, 16, 13
END

TAB3 DIALOGEX 0, 0, 200, 220
	 FONT 9, "MS Shell Dlg"
	 STYLE DS_SHELLFONT
BEGIN
	GROUPBOX " RamDisk Configuration Tool ", ID_TITLE, 7, 8, 185, 193
	LTEXT "", ID_TEXT1, 22, 206, 200, 9
	LTEXT "Load Content from Image File or Folder:", ID_TEXT2, 18, 25, 172, 9
	EDITTEXT ID_EDIT3, 16, 35, 150, 12, ES_AUTOHSCROLL
	PUSHBUTTON "...", ID_PBUTTON1, 168, 34, 16, 13
	AUTOCHECKBOX "Synchronize at System Shutdown", ID_CHECK8, 18, 58, 172, 10
	AUTOCHECKBOX "Copy only files with Archive attribute", ID_CHECK9, 28, 75, 162, 10
	AUTOCHECKBOX "Delete data removed from the RamDisk", ID_CHECK10, 28, 90, 162, 10
	LTEXT "Excluded Folders:", ID_TEXT8, 28, 107, 162, 9
	EDITTEXT ID_EDIT5, 28, 117, 145, 49, ES_MULTILINE | ES_WANTRETURN | WS_HSCROLL | WS_VSCROLL
	PUSHBUTTON "Synchronize now", ID_PBUTTON2, 28, 176, 100, 13
END

VAR_DLG DIALOGEX 0, 0, 264, 177
		STYLE WS_SYSMENU | DS_SHELLFONT
		FONT 9, "MS Shell Dlg"
		CAPTION "ImDisk"
BEGIN
	GROUPBOX " TEMP Environment Variables ", ID_TITLE, 6, 6, 252, 146
	GROUPBOX " User ", ID_TEXT1, 12, 20, 241, 40
	LTEXT "TEMP:", IDC_STATIC, 21, 32, 24, 9
	EDITTEXT ID_EDIT5, 45, 31, 203, 12, ES_AUTOHSCROLL
	LTEXT "TMP:", IDC_STATIC, 21, 46, 24, 9
	EDITTEXT ID_EDIT6, 45, 45, 203, 12, ES_AUTOHSCROLL
	GROUPBOX " System ", ID_TEXT2, 12, 64, 241, 40
	LTEXT "TEMP:", IDC_STATIC, 21, 76, 24, 9
	EDITTEXT ID_EDIT7, 45, 75, 203, 12, ES_AUTOHSCROLL
	LTEXT "TMP:", IDC_STATIC, 21, 90, 24, 9
	EDITTEXT ID_EDIT8, 45, 89, 203, 12, ES_AUTOHSCROLL
	PUSHBUTTON "Set All To:", ID_PBUTTON5, 13, 113, 70, 14
	EDITTEXT ID_EDIT9, 85, 114, 167, 12, ES_AUTOHSCROLL
	PUSHBUTTON "Set Defaults", ID_PBUTTON6, 13, 132, 80, 14
	DEFPUSHBUTTON "OK", IDOK, 170, 158, 43, 13
	PUSHBUTTON "Cancel", IDCANCEL, 217, 158, 43, 13
END

DYN_DLG DIALOGEX 0, 0, 220, 222
		STYLE WS_SYSMENU | DS_SHELLFONT
		FONT 9, "MS Shell Dlg"
		CAPTION "ImDisk"
BEGIN
	GROUPBOX "", IDC_STATIC, 6, 2, 208, 159
	GROUPBOX " Free Space Wiping Parameters ", ID_TITLE, 8, 43, 204, 116
	GROUPBOX "", IDC_STATIC, 6, 163, 208, 34
	LTEXT "Memory Release Method:", ID_TEXT6, 30, 11, 180, 9
	COMBOBOX ID_COMBO5, 30, 21, 120, 24, CBS_DROPDOWNLIST | WS_TABSTOP
	LTEXT "", ID_TEXT7, 16, 55, 188, 18
	LTEXT "Minimal Amount of Data to Clean:", ID_TEXT1, 30, 77, 180, 9
	EDITTEXT ID_EDIT12, 30, 87, 21, 12, ES_NUMBER
	LTEXT "/ 1000", IDC_STATIC, 54, 88, 30, 9
	LTEXT "Minimal Time between 2 Cleanups:", ID_TEXT2, 30, 104, 180, 9
	EDITTEXT ID_EDIT13, 30, 114, 33, 12, ES_NUMBER
	LTEXT "Seconds", ID_TEXT4, 66, 116, 100, 9
	LTEXT "Maximal Activity before Cleanup:", ID_TEXT3, 30, 131, 180, 9
	EDITTEXT ID_EDIT14, 30, 141, 29, 12, ES_NUMBER
	LTEXT "MB/s", ID_TEXT5, 62, 142, 100, 9
	LTEXT "Size of Allocated Memory Blocks:", ID_TEXT8, 30, 170, 180, 9
	LTEXT "2^", IDC_STATIC, 30, 182, 14, 9
	EDITTEXT ID_EDIT11, 44, 180, 21, 12, ES_NUMBER
	DEFPUSHBUTTON "OK", IDOK, 126, 203, 43, 13
	PUSHBUTTON "Cancel", IDCANCEL, 173, 203, 43, 13
END

WARN_DLG DIALOGEX 0, 0, 250, 150
		   STYLE WS_SYSMENU | DS_SHELLFONT
		   FONT 9, "MS Shell Dlg"
		   CAPTION "ImDisk"
BEGIN
	LTEXT "", ID_TEXT1, 44, 13, 200, 87
	PUSHBUTTON "Shutdown settings", ID_PBUTTON1, 59, 101, 130, 13
	AUTOCHECKBOX "Do not show this warning again", ID_CHECK1, 8, 132, 162, 10
	DEFPUSHBUTTON "Close", ID_PBUTTON2, 203, 131, 43, 13
	GROUPBOX "", IDC_STATIC, -5, 120, 260, 40
END


1 VERSIONINFO
FILEVERSION 7,6,0,0
BEGIN
BLOCK "StringFileInfo"
	BEGIN
	BLOCK "040904b0"
		BEGIN
			VALUE "FileDescription", "RamDisk Configuration Tool"
			VALUE "ProductName", "imdisk"
			VALUE "ProductVersion", "*******"
		END
	END
BLOCK "VarFileInfo"
	BEGIN
		VALUE "Translation", 0x0409, 0x04B0
	END
END
