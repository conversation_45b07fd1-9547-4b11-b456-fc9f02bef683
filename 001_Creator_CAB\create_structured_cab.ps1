# PowerShell script to create CAB file with complete folder structure
Write-Host "Creating CAB file with complete folder structure..." -ForegroundColor Green

# Clean up existing files
if (Test-Path "files_structured.cab") { Remove-Item "files_structured.cab" -Force }
if (Test-Path "structured.ddf") { Remove-Item "structured.ddf" -Force }

# Create DDF file content
Write-Host "Creating DDF directive file with folder structure..." -ForegroundColor Yellow

$ddfContent = @"
.OPTION EXPLICIT
.Set CabinetNameTemplate=files_structured.cab
.Set DiskDirectoryTemplate=.
.Set CompressionType=MSZIP
.Set UniqueFiles=OFF
.Set Cabinet=ON
.Set Compress=ON

"@

# Get all files from files directory recursively
$allFiles = Get-ChildItem "files" -Recurse -File
Write-Host "Found $($allFiles.Count) files to include" -ForegroundColor Green

# Add each file to DDF with proper path mapping to preserve folder structure
foreach ($file in $allFiles) {
    $sourcePath = $file.FullName
    $relativePath = $file.FullName.Substring((Get-Item "files").FullName.Length + 1)
    
    # Add file with preserved directory structure
    $ddfContent += "`"$sourcePath`" `"$relativePath`"`n"
    Write-Host "  Adding: $relativePath"
}

# Write DDF file
$ddfContent | Out-File "structured.ddf" -Encoding ASCII

Write-Host "Running makecab with folder structure..." -ForegroundColor Yellow
$result = Start-Process -FilePath "makecab" -ArgumentList "/F", "structured.ddf" -Wait -PassThru -NoNewWindow

if ($result.ExitCode -eq 0) {
    Write-Host "SUCCESS: CAB file with folder structure created!" -ForegroundColor Green
    
    if (Test-Path "files_structured.cab") {
        $cabFile = Get-Item "files_structured.cab"
        Write-Host "CAB file size: $($cabFile.Length) bytes" -ForegroundColor Green
        
        # Test the CAB file
        Write-Host "Testing CAB file structure..." -ForegroundColor Yellow
        if (Test-Path "test_structured") { Remove-Item "test_structured" -Recurse -Force }
        New-Item -ItemType Directory -Path "test_structured" -Force | Out-Null
        
        $extractResult = Start-Process -FilePath "extrac32.exe" -ArgumentList "/e", "/l", "test_structured", "files_structured.cab" -Wait -PassThru -NoNewWindow
        
        if ($extractResult.ExitCode -eq 0) {
            $extractedFiles = Get-ChildItem "test_structured" -Recurse -File
            Write-Host "Extracted $($extractedFiles.Count) files" -ForegroundColor Green
            
            # Check main files
            if (Test-Path "test_structured\config.exe") {
                Write-Host "SUCCESS: config.exe found in root directory!" -ForegroundColor Green
            } else {
                Write-Host "WARNING: config.exe not found in root directory" -ForegroundColor Yellow
            }
            
            # Check folder structure
            if (Test-Path "test_structured\driver") {
                Write-Host "SUCCESS: driver directory found!" -ForegroundColor Green
                $driverFiles = Get-ChildItem "test_structured\driver" -Recurse -File
                Write-Host "  Driver directory contains $($driverFiles.Count) files" -ForegroundColor Green
            } else {
                Write-Host "ERROR: driver directory missing!" -ForegroundColor Red
            }
            
            if (Test-Path "test_structured\lang") {
                Write-Host "SUCCESS: lang directory found!" -ForegroundColor Green
                $langFiles = Get-ChildItem "test_structured\lang" -File
                Write-Host "  Lang directory contains $($langFiles.Count) files" -ForegroundColor Green
            } else {
                Write-Host "ERROR: lang directory missing!" -ForegroundColor Red
            }
            
            # Show directory structure
            Write-Host "`nDirectory structure:" -ForegroundColor Cyan
            Get-ChildItem "test_structured" -Recurse | Where-Object { $_.PSIsContainer } | ForEach-Object {
                $depth = ($_.FullName.Substring((Get-Item "test_structured").FullName.Length) -split '\\').Count - 1
                $indent = "  " * $depth
                Write-Host "$indent$($_.Name)/" -ForegroundColor Cyan
            }
            
        } else {
            Write-Host "ERROR: Failed to extract CAB file" -ForegroundColor Red
        }
        
        # Keep test directory for inspection
        Write-Host "`nTest directory preserved at: test_structured" -ForegroundColor Yellow
        
    } else {
        Write-Host "ERROR: CAB file was not created" -ForegroundColor Red
    }
} else {
    Write-Host "ERROR: makecab failed with exit code $($result.ExitCode)" -ForegroundColor Red
}

# Clean up DDF file
Remove-Item "structured.ddf" -Force -ErrorAction SilentlyContinue

Write-Host "Done!" -ForegroundColor Green
