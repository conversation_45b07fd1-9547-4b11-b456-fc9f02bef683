@echo off
chcp 65001 >nul
echo ========================================
echo 测试 Unicode 字符修复 - 解决进程循环问题
echo ========================================

echo.
echo 🔍 发现的根本问题：
echo 1. MountImg.c 使用 wWinMain(LPWSTR argv) - 宽字符
echo 2. VirtualDiskTool 使用 main(char* argv) - 窄字符
echo 3. 导致字符串比较和参数传递完全错误
echo.

echo 🛠️ 修复内容：
echo ✅ 修复1: 函数签名
echo    修复前: int main(int argc, char* argv[])
echo    修复后: int wmain(int argc, wchar_t* argv[])
echo.
echo ✅ 修复2: 字符串类型
echo    修复前: char cmdline_ptr[MAX_PATH]
echo    修复后: wchar_t cmdline_ptr[MAX_PATH]
echo.
echo ✅ 修复3: 字符串比较
echo    修复前: strcmp(argv[1], "/UAC")
echo    修复后: wcscmp(argv[1], L"/UAC")
echo.
echo ✅ 修复4: 参数格式化
echo    修复前: _snwprintf(txt, L"/UAC %%d %%S", logicalDrives, cmdline_ptr)
echo    修复后: _snwprintf(txt, L"/UAC %%d %%s", logicalDrives, cmdline_ptr)
echo.

echo 📋 预期修复效果：
echo - 权限检查逻辑正确工作
echo - UAC参数正确传递和解析
echo - 提升后进程正确识别自己
echo - 不再产生无限循环
echo.

echo 🚀 开始测试修复效果...
echo ----------------------------------------

echo 测试前进程数量:
tasklist /fi "imagename eq VirtualDiskTool32.exe" | find /c "VirtualDiskTool32.exe"

echo.
echo 执行 VirtualDiskTool32.exe --test-mount
echo 观察是否还会产生多个进程...
echo.

VirtualDiskTool32.exe --test-mount

echo.
echo 程序退出码: %ERRORLEVEL%

echo.
echo 等待3秒后检查进程状态...
timeout /t 3 /nobreak >nul

echo 测试后进程数量:
tasklist /fi "imagename eq VirtualDiskTool32.exe" | find /c "VirtualDiskTool32.exe"

echo.
echo 当前所有 VirtualDiskTool32.exe 进程:
tasklist /fi "imagename eq VirtualDiskTool32.exe" /fo table

echo.
echo 📊 技术分析：
echo ========================================
echo.
echo 🔍 MountImg.c 的正确实现：
echo   int __stdcall wWinMain(HINSTANCE, HINSTANCE, LPWSTR lpCmdLine, int)
echo   LPWSTR *argv;  // 宽字符参数数组
echo   wcscmp(argv[1], L"/UAC")  // 宽字符比较
echo   _snwprintf(txt, L"/UAC %%d %%s", GetLogicalDrives(), cmdline_ptr);
echo.
echo 🔧 VirtualDiskTool 修复后：
echo   int wmain(int argc, wchar_t* argv[])
echo   wchar_t cmdline_ptr[MAX_PATH];  // 宽字符缓冲区
echo   wcscmp(argv[1], L"/UAC")  // 宽字符比较
echo   _snwprintf(txt, L"/UAC %%d %%s", logicalDrives, cmdline_ptr);
echo.
echo ✅ 关键修复点：
echo 1. 字符编码统一：全部使用宽字符 (wchar_t)
echo 2. 函数调用统一：wcscmp, wcslen, wcscpy 等
echo 3. 格式化字符串：%%S 改为 %%s (宽字符到宽字符)
echo 4. 调试输出：使用 wprintf 输出宽字符
echo.

echo 🎯 预期行为：
echo - Windows XP: 直接执行，不触发UAC
echo - Windows Vista+: 正确弹出UAC，用户确认后只有1个进程
echo - 无论哪种情况都不会产生进程循环
echo.

echo ========================================
echo Unicode 字符修复测试完成
echo ========================================

pause
