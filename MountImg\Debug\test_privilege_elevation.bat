@echo off
chcp 65001 >nul
echo ========================================
echo Testing Privilege Elevation
echo ========================================

echo.
echo 这个测试验证 VirtualDiskTool 的权限提升功能
echo.
echo 权限提升实现说明:
echo 参考 MountImg.c 中的权限提升逻辑
echo 检测当前权限状态，必要时请求管理员权限
echo 支持 Windows Vista+ 的 UAC 机制
echo.

echo 实现特点:
echo 1. 自动检测操作系统版本 (Vista+ 需要 UAC)
echo 2. 检查当前进程是否以管理员权限运行
echo 3. 如果权限不足，使用 ShellExecute "runas" 请求提升
echo 4. 传递原始命令行参数给提升后的进程
echo 5. 支持 /UAC 参数标识提升后的进程
echo.

echo 检查当前系统信息...
echo ----------------------------------------

ver
echo.

echo 检查当前用户权限...
net session >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo ✅ 当前以管理员权限运行
    echo   VirtualDiskTool 应该直接执行，不需要权限提升
) else (
    echo ❌ 当前未以管理员权限运行
    echo   VirtualDiskTool 应该请求权限提升
)

echo.
echo 启动 VirtualDiskTool32.exe 测试权限提升...
echo ----------------------------------------

echo 注意: 如果出现 UAC 提示，请点击"是"以允许权限提升
echo.

VirtualDiskTool32.exe

echo.
echo ----------------------------------------
echo 程序执行完毕，退出码: %ERRORLEVEL%
echo.

if %ERRORLEVEL% EQU 0 (
    echo ✅ SUCCESS: 权限提升功能正常工作
) else (
    echo ❌ FAILED: 程序运行出错，退出码: %ERRORLEVEL%
    echo.
    echo 可能的问题:
    echo 1. 用户拒绝了 UAC 权限提升请求
    echo 2. 系统不支持 UAC (Windows XP)
    echo 3. 程序编译或链接错误
    echo 4. 缺少必要的系统权限
)

echo.
echo ========================================
echo 权限提升实现技术说明:
echo ========================================
echo.
echo ✅ 权限检测函数 (IsRunningAsAdministrator):
echo   使用 CheckTokenMembership 检查当前用户是否属于管理员组
echo   创建管理员组 SID: SECURITY_BUILTIN_DOMAIN_RID + DOMAIN_ALIAS_RID_ADMINS
echo   返回 TRUE/FALSE 表示当前权限状态
echo.
echo ✅ UAC 需求检测 (IsUACRequired):
echo   使用 GetVersionEx 获取操作系统版本
echo   Windows Vista (6.0) 及以上版本需要 UAC
echo   Windows XP 及以下版本不需要 UAC
echo.
echo ✅ 权限提升请求 (RequestAdministratorPrivileges):
echo   参考 MountImg.c 实现: ShellExecute(NULL, L"runas", exePath, parameters, NULL, SW_SHOWDEFAULT)
echo   构建参数格式: /UAC <logical_drives> <original_params>
echo   成功启动提升权限进程后调用 ExitProcess(0)
echo.
echo ✅ 提升进程检测 (IsElevatedProcess):
echo   检查命令行第一个参数是否为 "/UAC"
echo   提升后的进程会收到特殊的 UAC 参数
echo.
echo ✅ 参数提取 (ExtractOriginalCommandLine):
echo   从 UAC 参数中提取原始命令行参数
echo   格式: argv[0]=程序名, argv[1]="/UAC", argv[2]=logical_drives, argv[3]=original_params
echo.
echo ✅ 主函数流程:
echo   1. 检查是否为提升权限后的进程 (IsElevatedProcess)
echo   2. 如果不是，检查是否需要 UAC (IsUACRequired)
echo   3. 检查当前权限 (IsRunningAsAdministrator)
echo   4. 如果权限不足，请求提升 (RequestAdministratorPrivileges)
echo   5. 继续执行正常的程序逻辑
echo.
echo ✅ 错误处理:
echo   - UAC 请求失败: 显示错误码和说明
echo   - 用户拒绝提升: 程序正常退出
echo   - 系统不支持: 在 XP 上直接执行
echo   - 参数解析失败: 提供默认行为
echo.
echo ✅ 兼容性:
echo   - Windows XP: 不需要 UAC，直接执行
echo   - Windows Vista+: 自动检测和请求 UAC
echo   - 32位/64位: 通用实现
echo   - 不同语言环境: 使用 Unicode API
echo.

pause
