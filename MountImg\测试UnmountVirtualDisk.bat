@echo off
chcp 65001 >nul
echo.
echo ═══════════════════════════════════════════════════════════════
echo                    测试 UnmountVirtualDisk 功能
echo ═══════════════════════════════════════════════════════════════
echo.

cd /d "%~dp0"

echo 📋 测试步骤：
echo    1. 使用VirtualDiskTool32.exe测试卸载功能
echo    2. 检查详细的调试输出
echo    3. 验证卸载结果
echo.

echo 🔧 开始测试...
echo.

REM 测试卸载Z:驱动器
echo ═══ 测试1: 卸载Z:驱动器 ═══
Debug\VirtualDiskTool32.exe --unmount Z:
echo.

REM 测试卸载Y:驱动器  
echo ═══ 测试2: 卸载Y:驱动器 ═══
Debug\VirtualDiskTool32.exe --unmount Y:
echo.

REM 测试卸载X:驱动器
echo ═══ 测试3: 卸载X:驱动器 ═══
Debug\VirtualDiskTool32.exe --unmount X:
echo.

REM 测试无效的驱动器号
echo ═══ 测试4: 测试无效驱动器号 ═══
Debug\VirtualDiskTool32.exe --unmount ZZ
echo.

echo ✅ 测试完成！
echo.
echo 💡 提示：
echo    - 如果看到详细的调试输出，说明增强的UnmountVirtualDisk_Core正在工作
echo    - 检查退出码和错误信息来判断卸载是否成功
echo    - 可以使用DebugView工具查看更详细的调试信息
echo.

pause
