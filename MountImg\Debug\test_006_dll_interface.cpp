/*
 * test_006_dll_interface.cpp
 * 测试VirtualDiskLib的006_Dll标准接口
 *
 * 编译命令：
 * cl test_006_dll_interface.cpp /I..\VirtualDiskLib VirtualDiskLib32.lib /Fe:test_006_dll.exe
 */

#include <iostream>
#include <string>
#include <windows.h>

// 函数指针类型定义
typedef void (*ProgressCallback)(const std::string& taskId, int progress, const std::string& matchResult);
typedef bool (*QueryTaskControlCallback)(const std::string& taskId, int controlType);

// DLL函数指针类型
typedef std::string (*GetLibraryInfoFunc)(
    const std::string& params,
    ProgressCallback progressCallback,
    const std::string& taskId,
    QueryTaskControlCallback queryTaskControlCb
);

typedef std::string (*InitializeVirtualDiskLibFunc)(
    const std::string& params,
    ProgressCallback progressCallback,
    const std::string& taskId,
    QueryTaskControlCallback queryTaskControlCb
);

typedef std::string (*CleanupVirtualDiskLibFunc)(
    const std::string& params,
    ProgressCallback progressCallback,
    const std::string& taskId,
    QueryTaskControlCallback queryTaskControlCb
);

typedef std::string (*MountVirtualDiskFunc)(
    const std::string& params,
    ProgressCallback progressCallback,
    const std::string& taskId,
    QueryTaskControlCallback queryTaskControlCb
);

typedef std::string (*UnmountVirtualDiskFunc)(
    const std::string& params,
    ProgressCallback progressCallback,
    const std::string& taskId,
    QueryTaskControlCallback queryTaskControlCb
);

typedef std::string (*GetMountStatusFunc)(
    const std::string& params,
    ProgressCallback progressCallback,
    const std::string& taskId,
    QueryTaskControlCallback queryTaskControlCb
);

// 进度回调函数
void TestProgressCallback(const std::string& taskId, int progress, const std::string& matchResult) {
    std::cout << "[PROGRESS] Task: " << taskId << ", Progress: " << progress << "%" << std::endl;
    if (progress == -1) {
        std::cout << "[MATCH] Result: " << matchResult << std::endl;
    }
}

// 任务控制回调函数
bool TestQueryTaskControlCallback(const std::string& taskId, int controlType) {
    // controlType: 1=cancel, 2=pause
    std::cout << "[CONTROL] Task: " << taskId << ", Control Type: " << controlType << std::endl;
    return false; // 不取消或暂停
}

int main() {
    std::cout << "=== VirtualDiskLib 006_Dll Interface Test ===" << std::endl;
    
    // 加载DLL
    HMODULE hDll = LoadLibraryA("VirtualDiskLib32.dll");
    if (!hDll) {
        std::cout << "ERROR: Failed to load VirtualDiskLib32.dll" << std::endl;
        return 1;
    }
    
    std::cout << "✓ DLL loaded successfully" << std::endl;
    
    // 获取函数指针
    GetLibraryInfoFunc GetLibraryInfo = (GetLibraryInfoFunc)GetProcAddress(hDll, "GetLibraryInfo");
    InitializeVirtualDiskLibFunc InitializeVirtualDiskLib = (InitializeVirtualDiskLibFunc)GetProcAddress(hDll, "InitializeVirtualDiskLib");
    CleanupVirtualDiskLibFunc CleanupVirtualDiskLib = (CleanupVirtualDiskLibFunc)GetProcAddress(hDll, "CleanupVirtualDiskLib");
    MountVirtualDiskFunc MountVirtualDisk = (MountVirtualDiskFunc)GetProcAddress(hDll, "MountVirtualDisk");
    UnmountVirtualDiskFunc UnmountVirtualDisk = (UnmountVirtualDiskFunc)GetProcAddress(hDll, "UnmountVirtualDisk");
    GetMountStatusFunc GetMountStatus = (GetMountStatusFunc)GetProcAddress(hDll, "GetMountStatus");
    
    if (!GetLibraryInfo || !InitializeVirtualDiskLib || !CleanupVirtualDiskLib ||
        !MountVirtualDisk || !UnmountVirtualDisk || !GetMountStatus) {
        std::cout << "ERROR: Failed to get function pointers" << std::endl;
        FreeLibrary(hDll);
        return 1;
    }
    
    std::cout << "✓ Function pointers obtained" << std::endl;
    
    try {
        // 测试1: 获取库信息
        std::cout << "\n--- Test 1: GetLibraryInfo ---" << std::endl;
        std::string result = GetLibraryInfo("{}", TestProgressCallback, "test-info", TestQueryTaskControlCallback);
        std::cout << "Result: " << result << std::endl;
        
        // 测试2: 初始化库
        std::cout << "\n--- Test 2: InitializeVirtualDiskLib ---" << std::endl;
        result = InitializeVirtualDiskLib("{\"check_dependencies\":true,\"enable_debug\":false}", 
                                         TestProgressCallback, "test-init", TestQueryTaskControlCallback);
        std::cout << "Result: " << result << std::endl;
        
        // 测试3: 获取挂载状态
        std::cout << "\n--- Test 3: GetMountStatus ---" << std::endl;
        result = GetMountStatus("{\"drive\":\"Z:\"}", 
                               TestProgressCallback, "test-status", TestQueryTaskControlCallback);
        std::cout << "Result: " << result << std::endl;
        
        // 测试4: 清理库
        std::cout << "\n--- Test 4: CleanupVirtualDiskLib ---" << std::endl;
        result = CleanupVirtualDiskLib("{\"force_cleanup\":false}", 
                                      TestProgressCallback, "test-cleanup", TestQueryTaskControlCallback);
        std::cout << "Result: " << result << std::endl;
        
        std::cout << "\n=== All tests completed successfully ===" << std::endl;
        
    } catch (const std::exception& e) {
        std::cout << "ERROR: Exception occurred: " << e.what() << std::endl;
    } catch (...) {
        std::cout << "ERROR: Unknown exception occurred" << std::endl;
    }
    
    // 释放DLL
    FreeLibrary(hDll);
    std::cout << "✓ DLL unloaded" << std::endl;
    
    return 0;
}
