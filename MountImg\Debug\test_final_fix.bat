@echo off
chcp 65001 >nul
echo ========================================
echo 最终修复测试 - VirtualDiskTool 进程循环问题
echo ========================================

echo.
echo 🔧 本次修复的关键问题：
echo 1. 参数传递错误：之前传递的是 exePath 而不是 cmdline_ptr
echo 2. ShellExecute 失败处理：失败时仍然调用 ExitProcess(0)
echo 3. 参数解析逻辑：需要正确解析 /UAC <drives> <cmdline> 格式
echo.

echo 📋 修复内容：
echo ✅ 修复1: 正确传递原始命令行参数
echo    修复前: _snwprintf(txt, L"/UAC %%d \"%%s\"", logicalDrives, exePath);
echo    修复后: _snwprintf(txt, L"/UAC %%d %%S", logicalDrives, cmdline_ptr);
echo.
echo ✅ 修复2: 正确处理 ShellExecute 失败
echo    修复前: //return 1; (被注释)
echo    修复后: return 1; (失败时返回错误，不退出进程)
echo.
echo ✅ 修复3: 添加详细调试信息
echo    - 显示所有命令行参数
echo    - 显示参数解析过程
echo    - 显示权限检查逻辑
echo.

echo 🚀 开始测试...
echo ----------------------------------------

echo 测试前进程数量:
tasklist /fi "imagename eq VirtualDiskTool32.exe" | find /c "VirtualDiskTool32.exe"

echo.
echo 启动 VirtualDiskTool32.exe --test-mount
echo 观察调试输出和进程行为...
echo.

VirtualDiskTool32.exe --test-mount

echo.
echo ----------------------------------------
echo 程序执行完毕，退出码: %ERRORLEVEL%

echo.
echo 等待2秒后检查进程数量...
timeout /t 2 /nobreak >nul

echo 测试后进程数量:
tasklist /fi "imagename eq VirtualDiskTool32.exe" | find /c "VirtualDiskTool32.exe"

echo.
echo 当前所有 VirtualDiskTool32.exe 进程:
tasklist /fi "imagename eq VirtualDiskTool32.exe" /fo table

echo.
echo 📊 预期行为分析：
echo ========================================
echo.
echo 🎯 Windows XP (版本 < 6):
echo   - 应该显示 "UAC not required"
echo   - 直接执行测试功能
echo   - 只有1个进程，执行完毕后退出
echo.
echo 🎯 Windows Vista+ (版本 >= 6, 非管理员):
echo   - 应该显示 "Requesting administrator privileges"
echo   - 弹出UAC对话框
echo   - 用户确认后启动新的提升权限进程
echo   - 原进程调用 ExitProcess(0) 退出
echo   - 最终只有1个提升权限的进程在运行
echo.
echo 🎯 Windows Vista+ (版本 >= 6, 已是管理员):
echo   - 应该显示 "UAC not required" 或直接执行
echo   - 不触发权限提升逻辑
echo   - 只有1个进程运行
echo.

echo ❌ 如果仍有问题，可能原因：
echo 1. 代码未重新编译
echo 2. UAC对话框被用户拒绝
echo 3. 系统权限设置问题
echo 4. 其他未发现的逻辑错误
echo.

echo ========================================
echo 最终修复测试完成
echo ========================================

pause
