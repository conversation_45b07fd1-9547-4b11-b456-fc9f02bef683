@echo off
rem 磁盘挂载工具启动脚本 - 增强版
rem 完全模仿原始脚本结构，支持XP及以上系统

if not "%1"=="7" start /min cmd /c ""%~0" 7 %*" & exit /b

set F=%TEMP%\DiskUp%TIME::=%

rem 检测系统并选择解压方法
ver | find "5.1" >nul && goto :use_vbs
ver | find "5.2" >nul && goto :use_vbs
goto :use_ps

:use_ps
rem Windows Vista及以上使用PowerShell
powershell -Command "try { Expand-Archive -Path '%~dp0files.zip' -DestinationPath '%F%' -Force } catch { exit 1 }"
if %ERRORLEVEL% NEQ 0 goto :use_vbs
goto :find_config

:use_vbs
rem Windows XP/2003使用VBScript
mkdir "%F%"
echo Set objShell = CreateObject("Shell.Application") > "%TEMP%\uz.vbs"
echo Set objFolder = objShell.NameSpace("%F%") >> "%TEMP%\uz.vbs"
echo Set objZip = objShell.NameSpace("%~dp0files.zip") >> "%TEMP%\uz.vbs"
echo If Not objZip Is Nothing Then objFolder.CopyHere objZip.Items, 20 >> "%TEMP%\uz.vbs"
cscript //nologo "%TEMP%\uz.vbs"
del "%TEMP%\uz.vbs"
timeout /t 2 /nobreak >nul 2>&1

:find_config
rem 查找config.exe并运行
if exist "%F%\config.exe" (
    "%F%\config.exe" %2 %3 %4
) else (
    rem 在子目录中查找
    for /r "%F%" %%i in (config.exe) do (
        if exist "%%i" (
            "%%i" %2 %3 %4
            goto :cleanup
        )
    )
)

:cleanup
rd /s /q "%F%"
