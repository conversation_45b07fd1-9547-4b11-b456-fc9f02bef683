@echo off
chcp 65001 >nul
echo ========================================
echo Testing ID_COMBO2 Drive Selection Response
echo ========================================

echo.
echo 这个测试验证 ID_COMBO2 驱动器下拉框的数据显示修改响应
echo.
echo ID_COMBO2 响应功能:
echo 1. 驱动器格式验证 (X: 格式)
echo 2. 驱动器字母有效性检查 (A-Z)
echo 3. 驱动器占用状态检测
echo 4. 驱动器字母标准化 (转大写)
echo 5. 全局变量更新
echo.

echo 启动 MountImg.exe 测试 ID_COMBO2 响应...
echo ----------------------------------------

echo 2 | MountImg.exe

echo.
echo ----------------------------------------
echo 程序执行完毕，退出码: %ERRORLEVEL%
echo.

echo 检查各种驱动器状态...
echo.
echo 当前系统驱动器状态:
for %%d in (C D E F G H I J K L M N O P Q R S T U V W X Y Z) do (
    if exist %%d:\ (
        echo   %%d: - 已占用
    ) else (
        echo   %%d: - 可用
    )
)

echo.
echo 检查目标 X: 驱动器...
if exist X:\ (
    echo ✅ SUCCESS: X: 驱动器已挂载
    echo 目录列表:
    dir X: /w
) else (
    echo ❌ FAILED: X: 驱动器未挂载
)

echo.
echo ========================================
echo ID_COMBO2 响应功能技术说明:
echo ========================================
echo.
echo 1. 驱动器格式验证:
echo    - 检查长度 >= 2
echo    - 检查第二个字符是否为 ':'
echo    - 格式: "X:" 或 "x:"
echo.
echo 2. 驱动器字母验证:
echo    - 范围检查: A-Z (不区分大小写)
echo    - 自动转换为大写
echo    - 拒绝无效字符
echo.
echo 3. 驱动器占用检测:
echo    - 使用 GetDriveType() API
echo    - DRIVE_NO_ROOT_DIR = 可用
echo    - 其他值 = 已占用
echo.
echo 4. 全局变量更新:
echo    - drive[0] = 大写驱动器字母
echo    - drive[1] = ':'
echo    - drive[2] = '\0'
echo.
echo 5. 错误处理:
echo    - 格式错误提示
echo    - 占用警告
echo    - 无效字符拒绝
echo.
echo 支持的输入格式:
echo   - "X:" (推荐)
echo   - "x:" (自动转大写)
echo   - "X" (自动添加冒号)
echo.

pause
