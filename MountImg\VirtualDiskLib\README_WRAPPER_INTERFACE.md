# VirtualDiskLib 统一封装接口使用说明

## 📋 **概述**

VirtualDiskLib现在提供了统一的C++封装接口，将所有虚拟磁盘操作函数封装成简化的C++接口，提供更好的易用性和类型安全。

## 🔧 **核心特性**

- **统一接口设计**：所有函数使用相同的参数模式
- **简化回调函数**：提供更简单的进度和任务控制回调
- **自动任务ID生成**：可选的任务ID参数，支持自动生成
- **异常安全**：完整的异常处理和错误报告
- **线程安全**：使用thread_local存储确保多线程安全

## 📚 **接口函数列表**

### 1. **核心封装接口**

```cpp
// 挂载虚拟磁盘
std::string MountVirtualDiskWrapper(
    const std::string& params,
    SimpleProgressCallback progressCallback = nullptr,
    const std::string& taskId = "",
    SimpleQueryTaskControlCallback queryTaskControlCb = nullptr
);

// 卸载虚拟磁盘
std::string UnmountVirtualDiskWrapper(
    const std::string& params,
    SimpleProgressCallback progressCallback = nullptr,
    const std::string& taskId = "",
    SimpleQueryTaskControlCallback queryTaskControlCb = nullptr
);

// 获取挂载状态
std::string GetMountStatusWrapper(
    const std::string& params = R"({"drive": ""})",
    SimpleProgressCallback progressCallback = nullptr,
    const std::string& taskId = "",
    SimpleQueryTaskControlCallback queryTaskControlCb = nullptr
);

// 获取库信息
std::string GetLibraryInfoWrapper(
    const std::string& params = "{}",
    SimpleProgressCallback progressCallback = nullptr,
    const std::string& taskId = "",
    SimpleQueryTaskControlCallback queryTaskControlCb = nullptr
);

// 初始化库
std::string InitializeVirtualDiskLibWrapper(
    const std::string& params = R"({"check_dependencies": true, "enable_debug": false})",
    SimpleProgressCallback progressCallback = nullptr,
    const std::string& taskId = "",
    SimpleQueryTaskControlCallback queryTaskControlCb = nullptr
);

// 清理库
std::string CleanupVirtualDiskLibWrapper(
    const std::string& params = R"({"force_cleanup": false})",
    SimpleProgressCallback progressCallback = nullptr,
    const std::string& taskId = "",
    SimpleQueryTaskControlCallback queryTaskControlCb = nullptr
);
```

### 2. **便捷接口函数**

```cpp
// 简化的挂载接口
std::string MountDiskSimple(
    const std::string& imagePath,
    const std::string& driveLetter,
    bool readonly = false,
    int partition = 1,
    SimpleProgressCallback progressCallback = nullptr
);

// 简化的卸载接口
std::string UnmountDiskSimple(
    const std::string& driveLetter,
    bool force = false,
    SimpleProgressCallback progressCallback = nullptr
);
```

## 🎯 **回调函数类型**

### 1. **简化进度回调**
```cpp
typedef void (*SimpleProgressCallback)(const std::string& taskId, int progress);
```
- `taskId`: 任务唯一标识符
- `progress`: 进度值 (0-100)

### 2. **简化任务控制回调**
```cpp
typedef bool (*SimpleQueryTaskControlCallback)(const std::string& taskId, int controlType);
```
- `taskId`: 任务唯一标识符
- `controlType`: 控制类型 (1=取消, 2=暂停)
- 返回值: `true`=执行控制操作, `false`=继续执行

## 💡 **使用示例**

### 1. **基本使用模式**

```cpp
#include "VirtualDiskLib.h"

// 定义回调函数
void MyProgressCallback(const std::string& taskId, int progress) {
    std::cout << "Task " << taskId << ": " << progress << "%" << std::endl;
}

bool MyControlCallback(const std::string& taskId, int controlType) {
    // 根据需要决定是否取消任务
    return false; // 不取消
}

int main() {
    // 1. 初始化库
    std::string result = InitializeVirtualDiskLibWrapper(
        R"({"check_dependencies": true})",
        MyProgressCallback,
        "init_task",
        MyControlCallback
    );
    
    // 2. 挂载磁盘
    std::string mountParams = R"({
        "image_path": "C:\\test\\disk.vmdk",
        "drive_letter": "Z:",
        "readonly": false,
        "partition": 1
    })";
    
    result = MountVirtualDiskWrapper(
        mountParams,
        MyProgressCallback,
        "mount_task",
        MyControlCallback
    );
    
    // 3. 检查结果
    if (result.find("\"status\":\"success\"") != std::string::npos) {
        std::cout << "挂载成功!" << std::endl;
    }
    
    return 0;
}
```

### 2. **使用便捷接口**

```cpp
// 简化的挂载操作
std::string result = MountDiskSimple(
    "C:\\test\\disk.vmdk",  // 镜像路径
    "Z:",                   // 驱动器号
    false,                  // 不是只读
    1,                      // 分区1
    MyProgressCallback      // 进度回调
);

// 简化的卸载操作
result = UnmountDiskSimple(
    "Z:",                   // 驱动器号
    false,                  // 不强制卸载
    MyProgressCallback      // 进度回调
);
```

### 3. **无回调的简单使用**

```cpp
// 最简单的使用方式
std::string result = MountDiskSimple("C:\\test\\disk.vmdk", "Z:");

if (result.find("\"status\":\"success\"") != std::string::npos) {
    std::cout << "挂载成功!" << std::endl;
}
```

## 🔍 **JSON参数格式**

### 挂载参数
```json
{
    "image_path": "C:\\path\\to\\disk.vmdk",
    "drive_letter": "Z:",
    "readonly": false,
    "partition": 1
}
```

### 卸载参数
```json
{
    "drive": "Z:",
    "force": false
}
```

### 状态查询参数
```json
{
    "drive": "Z:"  // 空字符串表示查询所有驱动器
}
```

## ⚠️ **注意事项**

1. **初始化顺序**：使用其他功能前必须先调用 `InitializeVirtualDiskLibWrapper`
2. **清理资源**：程序结束前建议调用 `CleanupVirtualDiskLibWrapper`
3. **任务ID**：如果不提供taskId，系统会自动生成唯一ID
4. **线程安全**：封装接口是线程安全的，可以在多线程环境中使用
5. **异常处理**：所有函数都包含完整的异常处理，不会抛出未捕获的异常

## 🚀 **编译和链接**

```bash
# 编译测试程序
cl test_wrapper_interface.cpp /I. VirtualDiskLib32.lib /Fe:test_wrapper.exe

# 在你的项目中使用
#include "VirtualDiskLib.h"
// 链接 VirtualDiskLib32.lib 或 VirtualDiskLib64.lib
```

## 📝 **完整示例**

参考 `test_wrapper_interface.cpp` 文件获取完整的使用示例和测试代码。
