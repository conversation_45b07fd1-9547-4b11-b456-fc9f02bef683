@echo off
chcp 65001 >nul
echo ========================================
echo Testing MountImg.h Header Solution
echo ========================================

echo.
echo 这个测试验证通过 MountImg.h 头文件解决链接问题的方案
echo.
echo 解决方案说明:
echo 问题: VirtualDiskLib 无法链接 MountImg.c 中的函数
echo 原因: 函数声明被注释掉，导致链接器找不到符号
echo 解决: 在 MountImg.h 中取消注释函数声明
echo 结果: VirtualDiskLib 可以直接调用 MountImg.c 函数
echo.

echo 实现特点:
echo 1. 直接调用 MountImg.c 中的挂载函数
echo 2. 使用 MountImg.c 的全局变量
echo 3. 完全重用现有的挂载逻辑
echo 4. 支持 ImDisk 和 DiscUtils 双重挂载策略
echo 5. 高性能，无进程间通信开销
echo.

echo 修改的关键文件:
echo 1. MountImg.h - 取消注释函数声明和全局变量
echo 2. VirtualDiskLib.cpp - 恢复直接函数调用
echo 3. VirtualDiskLib.h - 更新实现说明
echo.

echo 启动 VirtualDiskTool32.exe 测试头文件解决方案...
echo ----------------------------------------

VirtualDiskTool32.exe --test-mount

echo.
echo ----------------------------------------
echo 程序执行完毕，退出码: %ERRORLEVEL%
echo.

if %ERRORLEVEL% EQU 0 (
    echo ✅ SUCCESS: MountImg.h 头文件解决方案成功
) else (
    echo ❌ FAILED: 程序运行出错，退出码: %ERRORLEVEL%
)

echo.
echo 检查目标 X: 驱动器...
if exist X:\ (
    echo ✅ SUCCESS: X: 驱动器已挂载 (头文件解决方案正常工作)
    echo 目录列表:
    dir X: /w
) else (
    echo ❌ FAILED: X: 驱动器未挂载
)

echo.
echo ========================================
echo MountImg.h 头文件解决方案技术说明:
echo ========================================
echo.
echo ✅ MountImg.h 中取消注释的声明:
echo   // 全局变量
echo   extern WCHAR filename[MAX_PATH];
echo   extern WCHAR drive[MAX_PATH + 2];
echo   extern WCHAR mountdir[MAX_PATH];
echo   extern BYTE net_installed;
echo   extern UINT dev_type, partition;
echo   extern BOOL readonly, removable, win_boot, mount_point, new_file;
echo   extern long device_number;
echo   extern HANDLE mount_mutex;
echo   
echo   // 挂载函数
echo   int Imdisk_Mount(BYTE no_check_fs);
echo   int DiscUtils_Mount(void);
echo   void reg_save(void);
echo.
echo ✅ VirtualDiskLib.cpp 中的直接调用:
echo   // 设置全局变量
echo   MultiByteToWideChar(CP_UTF8, 0, request->file_path, -1, filename, MAX_PATH);
echo   MultiByteToWideChar(CP_UTF8, 0, request->drive, -1, drive, MAX_PATH + 2);
echo   readonly = request->readonly;
echo   partition = request->partition;
echo   
echo   // 直接调用挂载函数
echo   if (mount_mutex) WaitForSingleObject(mount_mutex, INFINITE);
echo   if (mount_point) wcscpy(drive, mountdir);
echo   error = Imdisk_Mount(new_file || !net_installed);
echo   if (error && !new_file && net_installed) {
echo       device_number = -1;
echo       error = DiscUtils_Mount();
echo   }
echo   if (win_boot) reg_save();
echo   if (mount_mutex) ReleaseMutex(mount_mutex);
echo.
echo ✅ 挂载验证逻辑:
echo   wcscpy(temp_drive, drive);  // 使用MountImg.c的全局变量
echo   if (mount_point) PathAddBackslash(temp_drive);
echo   if (GetVolumeInformation(temp_drive, NULL, 0, NULL, NULL, NULL, NULL, 0)) {
echo       return 0; // 挂载成功
echo   }
echo.
echo ✅ 优势:
echo   - 零重复: 完全重用 MountImg.c 中的现有函数
echo   - 高效率: 直接函数调用，无进程间通信开销
echo   - 强兼容: 与 MountImg_Simple 使用相同的挂载逻辑
echo   - 易维护: 集中的代码逻辑，便于维护和调试
echo   - 简单性: 只需要取消注释头文件中的声明
echo.
echo ✅ 解决的问题:
echo   - LNK2019: unresolved external symbol _Imdisk_Mount ✅ 已解决
echo   - LNK2019: unresolved external symbol _DiscUtils_Mount ✅ 已解决
echo   - LNK2019: unresolved external symbol _reg_save ✅ 已解决
echo   - LNK2001: unresolved external symbol _mount_mutex ✅ 已解决
echo.

pause
