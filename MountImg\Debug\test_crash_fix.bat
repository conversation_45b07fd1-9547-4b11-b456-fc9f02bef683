@echo off
chcp 65001 >nul
echo ========================================
echo Testing ImDisk Crash Fix
echo ========================================

echo.
echo 这个测试验证 ImDisk_GetDeviceListEx 崩溃问题的修复
echo.
echo 崩溃问题分析:
echo 问题: if (!ImDisk_GetDeviceListEx(_countof(list_device), list_device)) return -1;
echo 原因: ImDisk_GetDeviceListEx 函数指针为 NULL，调用时崩溃
echo 根因: imdisk.cpl 未加载或 GetProcAddress 失败
echo.

echo 修复方案:
echo 1. 添加 InitializeImDisk() 函数，确保 ImDisk 正确初始化
echo 2. 在 get_imdisk_unit() 中添加安全检查
echo 3. 在 ExecuteMountOperation() 开始时调用初始化
echo 4. 避免调用空函数指针
echo.

echo 修复的关键代码:
echo ✅ InitializeImDisk() 函数:
echo   - 加载 imdisk.cpl 库
echo   - 获取 ImDisk API 函数指针
echo   - 创建挂载互斥锁
echo   - 设置初始化标志
echo.
echo ✅ get_imdisk_unit() 安全检查:
echo   - 检查 ImDisk_GetDeviceListEx 函数指针
echo   - 检查 list_device 数组
echo   - 安全调用 ImDisk API
echo.
echo ✅ ExecuteMountOperation() 初始化调用:
echo   - Step 0: 调用 InitializeImDisk()
echo   - 检查初始化结果
echo   - 继续后续挂载步骤
echo.

echo 启动 VirtualDiskTool32.exe 测试崩溃修复...
echo ----------------------------------------

VirtualDiskTool32.exe --test-mount

echo.
echo ----------------------------------------
echo 程序执行完毕，退出码: %ERRORLEVEL%
echo.

if %ERRORLEVEL% EQU 0 (
    echo ✅ SUCCESS: 崩溃问题已修复，程序正常运行
) else (
    echo ❌ FAILED: 程序运行出错，退出码: %ERRORLEVEL%
    echo.
    echo 可能的问题:
    echo 1. ImDisk 未安装或服务未启动
    echo 2. imdisk.cpl 文件缺失
    echo 3. 权限不足
    echo 4. 其他系统问题
)

echo.
echo 检查目标 X: 驱动器...
if exist X:\ (
    echo ✅ SUCCESS: X: 驱动器已挂载 (崩溃修复成功)
    echo 目录列表:
    dir X: /w
) else (
    echo ❌ FAILED: X: 驱动器未挂载
)

echo.
echo ========================================
echo 崩溃修复技术说明:
echo ========================================
echo.
echo ✅ 问题根因分析:
echo   ImDisk_GetDeviceListEx 是通过 GetProcAddress 动态获取的函数指针
echo   如果 LoadLibraryA("imdisk.cpl") 失败，函数指针为 NULL
echo   直接调用 NULL 指针会导致访问违例崩溃 (0xC0000005)
echo.
echo ✅ InitializeImDisk() 实现:
echo   static BOOL initialized = FALSE;
echo   if (initialized) return 0; // 避免重复初始化
echo   
echo   if (!(h_cpl = LoadLibraryA("imdisk.cpl"))) return 1;
echo   
echo   ImDisk_GetDeviceListEx = GetProcAddress(h_cpl, "ImDiskGetDeviceListEx");
echo   ImDisk_ForceRemoveDevice = GetProcAddress(h_cpl, "ImDiskForceRemoveDevice");
echo   ImDisk_RemoveRegistrySettings = GetProcAddress(h_cpl, "ImDiskRemoveRegistrySettings");
echo   ImDisk_NotifyShellDriveLetter = GetProcAddress(h_cpl, "ImDiskNotifyShellDriveLetter");
echo   
echo   if (!ImDisk_GetDeviceListEx) {
echo       FreeLibrary(h_cpl);
echo       return 1;
echo   }
echo   
echo   if (!mount_mutex) {
echo       mount_mutex = CreateMutex(NULL, FALSE, L"ImDiskMountMutex");
echo   }
echo.
echo ✅ get_imdisk_unit() 安全检查:
echo   if (!ImDisk_GetDeviceListEx) return -1; // 函数指针检查
echo   if (!list_device) return -1;            // 数组检查
echo   if (!ImDisk_GetDeviceListEx(_countof(list_device), list_device)) return -1;
echo.
echo ✅ 错误处理策略:
echo   - 初始化失败: 返回错误码，不继续挂载
echo   - 函数指针为空: 返回 -1，避免崩溃
echo   - API 调用失败: 返回 -1，进行错误处理
echo   - 资源清理: 失败时释放已分配的资源
echo.
echo ✅ 防护措施:
echo   - 静态初始化标志，避免重复初始化
echo   - 函数指针有效性检查
echo   - 资源泄漏防护
echo   - 错误码传播机制
echo.

pause
