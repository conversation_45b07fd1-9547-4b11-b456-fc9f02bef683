.OPTION EXPLICIT
.Set CabinetNameTemplate=files_new.cab
.Set DiskDirectoryTemplate=.
.Set CompressionType=MSZIP
.Set UniqueFiles=OFF
.Set Cabinet=ON
.Set Compress=ON

; Main files - will be in root of CAB
files\config.exe config.exe
files\config32.exe config32.exe
files\config32_uninstall.exe config32_uninstall.exe
files\devio.exe devio.exe
files\DiscUtilsDevio.exe DiscUtilsDevio.exe
files\ImDisk-Dlg.exe ImDisk-Dlg.exe
files\ImDiskTk-svc.exe ImDiskTk-svc.exe
files\ImDiskTk-svc32.exe ImDiskTk-svc32.exe
files\ImDiskTk-svc64.exe ImDiskTk-svc64.exe
files\MountImg.exe MountImg.exe
files\MountImg32.exe MountImg32.exe
files\RamDiskUI.exe RamDiskUI.exe
files\RamDyn.exe RamDyn.exe
files\RamDyn32.exe RamDyn32.exe
files\RamDyn64.exe RamDyn64.exe
files\DevioNet.dll DevioNet.dll
files\DiscUtils.Core.dll DiscUtils.Core.dll
files\DiscUtils.Dmg.dll DiscUtils.Dmg.dll
files\DiscUtils.Streams.dll DiscUtils.Streams.dll
files\DiscUtils.Vdi.dll DiscUtils.Vdi.dll
files\DiscUtils.Vhd.dll DiscUtils.Vhd.dll
files\DiscUtils.Vhdx.dll DiscUtils.Vhdx.dll
files\DiscUtils.Vmdk.dll DiscUtils.Vmdk.dll
files\DiscUtils.Xva.dll DiscUtils.Xva.dll
files\ImDiskNet.dll ImDiskNet.dll
files\cp-admin.lnk cp-admin.lnk
files\cp.lnk cp.lnk
files\lang.txt lang.txt
files\setup.inf setup.inf
files\setup.rpt setup.rpt

; Driver files - maintain directory structure
files\driver\gpl.txt driver\gpl.txt
files\driver\imdisk.inf driver\imdisk.inf
files\driver\install.cmd driver\install.cmd
files\driver\msgboxw.exe driver\msgboxw.exe
files\driver\readme.txt driver\readme.txt
files\driver\runwaitw.exe driver\runwaitw.exe
files\driver\uninstall_imdisk.cmd driver\uninstall_imdisk.cmd
files\driver\awealloc\amd64\awealloc.cer driver\awealloc\amd64\awealloc.cer
files\driver\awealloc\amd64\awealloc.sys driver\awealloc\amd64\awealloc.sys
files\driver\awealloc\i386\awealloc.cer driver\awealloc\i386\awealloc.cer
files\driver\awealloc\i386\awealloc.sys driver\awealloc\i386\awealloc.sys
files\driver\cli\amd64\imdisk.exe driver\cli\amd64\imdisk.exe
files\driver\cli\i386\imdisk.exe driver\cli\i386\imdisk.exe
files\driver\cpl\amd64\imdisk.cpl driver\cpl\amd64\imdisk.cpl
files\driver\cpl\amd64\imdisk.lib driver\cpl\amd64\imdisk.lib
files\driver\cpl\i386\imdisk.cpl driver\cpl\i386\imdisk.cpl
files\driver\cpl\i386\imdisk.lib driver\cpl\i386\imdisk.lib
files\driver\svc\amd64\imdsksvc.exe driver\svc\amd64\imdsksvc.exe
files\driver\svc\i386\imdsksvc.exe driver\svc\i386\imdsksvc.exe
files\driver\sys\amd64\imdisk.cer driver\sys\amd64\imdisk.cer
files\driver\sys\amd64\imdisk.sys driver\sys\amd64\imdisk.sys
files\driver\sys\i386\imdisk.cer driver\sys\i386\imdisk.cer
files\driver\sys\i386\imdisk.sys driver\sys\i386\imdisk.sys

; Language files - maintain directory structure
files\lang\brazilian-portuguese.txt lang\brazilian-portuguese.txt
files\lang\english.txt lang\english.txt
files\lang\french.txt lang\french.txt
files\lang\german.txt lang\german.txt
files\lang\italian.txt lang\italian.txt
files\lang\russian.txt lang\russian.txt
files\lang\schinese.txt lang\schinese.txt
files\lang\spanish.txt lang\spanish.txt
files\lang\swedish.txt lang\swedish.txt
