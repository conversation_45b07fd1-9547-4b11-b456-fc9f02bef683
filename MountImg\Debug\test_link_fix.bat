@echo off
chcp 65001 >nul
echo ========================================
echo Testing Link Error Fix for ImDisk Functions
echo ========================================

echo.
echo 这个测试验证修复 ImDisk 函数链接错误的结果
echo.
echo 链接错误修复说明:
echo 错误: LNK2019: unresolved external symbol __imp__ImDiskOpenDeviceByMountPoint@8
echo 原因: ImDisk 函数不是静态链接的，需要动态加载
echo 修复: 使用 LoadLibrary + GetProcAddress 动态加载
echo.

echo 修复前的问题:
echo   直接调用: h = (HANDLE)ImDiskOpenDeviceByMountPoint(mount_point, access);
echo   结果: 链接器找不到函数符号
echo.

echo 修复后的解决方案:
echo   1. 加载库: h_cpl = LoadLibraryA("imdisk.cpl");
echo   2. 获取函数: ImDiskOpenDeviceByMountPoint = GetProcAddress(h_cpl, "ImDiskOpenDeviceByMountPoint");
echo   3. 调用函数: h = (HANDLE)ImDiskOpenDeviceByMountPoint(mount_point, access);
echo   4. 释放库: FreeLibrary(h_cpl);
echo.

echo 启动 VirtualDiskTool32.exe 测试修复结果...
echo ----------------------------------------

VirtualDiskTool32.exe --test-unmount

echo.
echo ----------------------------------------
echo 程序执行完毕，退出码: %ERRORLEVEL%
echo.

if %ERRORLEVEL% EQU 0 (
    echo ✅ SUCCESS: 链接错误已修复，程序正常运行
) else (
    echo ❌ FAILED: 程序运行出错，退出码: %ERRORLEVEL%
)

echo.
echo 检查目标 X: 驱动器...
if exist X:\ (
    echo ❌ FAILED: X: 驱动器仍然存在 (卸载失败)
    echo 目录列表:
    dir X: /w
) else (
    echo ✅ SUCCESS: X: 驱动器已卸载
)

echo.
echo ========================================
echo 动态加载 ImDisk 函数技术说明:
echo ========================================
echo.
echo ✅ 参考代码来源 (ImDisk-Dlg.c 第425-429行):
echo   if (!(h_cpl = LoadLibraryA("imdisk.cpl"))) {
echo       error(NULL, L"Error: cannot find imdisk.cpl. Please reinstall the driver.");
echo       ExitProcess(1);
echo   }
echo   ImDiskOpenDeviceByMountPoint = GetProcAddress(h_cpl, "ImDiskOpenDeviceByMountPoint");
echo.
echo ✅ 修复后的实现:
echo   // Step 0: 加载 ImDisk 库
echo   h_cpl = LoadLibraryA("imdisk.cpl");
echo   if (!h_cpl) {
echo       strcpy_s(response.error_message, "Cannot find imdisk.cpl");
echo       goto cleanup;
echo   }
echo   
echo   // 获取函数指针
echo   typedef HANDLE (WINAPI *ImDiskOpenDeviceByMountPointProc)(LPCWSTR, DWORD);
echo   ImDiskOpenDeviceByMountPointProc ImDiskOpenDeviceByMountPoint = 
echo       (ImDiskOpenDeviceByMountPointProc)GetProcAddress(h_cpl, "ImDiskOpenDeviceByMountPoint");
echo   
echo   if (!ImDiskOpenDeviceByMountPoint) {
echo       strcpy_s(response.error_message, "Cannot find ImDiskOpenDeviceByMountPoint function");
echo       goto cleanup;
echo   }
echo.
echo ✅ 动态加载的 ImDisk 函数:
echo   1. ImDiskOpenDeviceByMountPoint - 通过挂载点打开设备
echo   2. ImDiskForceRemoveDevice - 强制移除设备
echo   3. ImDiskRemoveMountPoint - 移除挂载点
echo.
echo ✅ 资源管理:
echo   - 库加载: LoadLibraryA("imdisk.cpl")
echo   - 函数获取: GetProcAddress(h_cpl, "函数名")
echo   - 库释放: FreeLibrary(h_cpl) (在 cleanup 标签处)
echo.
echo ✅ 错误处理:
echo   - 库加载失败: "Cannot find imdisk.cpl"
echo   - 函数获取失败: "Cannot find 函数名 function"
echo   - 自动资源清理: goto cleanup
echo.
echo ✅ 优势:
echo   - 无需静态链接 ImDisk 库
echo   - 运行时动态检查 ImDisk 可用性
echo   - 与 ImDisk-Dlg.c 完全一致的实现方式
echo   - 自动资源管理和错误处理
echo.

pause
