@echo off
chcp 65001 >nul
echo ========================================
echo Testing Real Windows API Implementation
echo ========================================

echo.
echo 这个测试验证真正的 Windows API CB_SETCURSEL 实现
echo.
echo 真正的 API 实现功能:
echo 1. 通过 GetActiveWindow() 获取主对话框句柄
echo 2. 通过 GetDlgItem(hDialog, ID_COMBO2) 获取控件句柄
echo 3. 使用 SendMessage(hCombo2, CB_SETCURSEL, index, 0) 设置选中项
echo 4. 使用 SendMessage(hCombo2, CB_GETCURSEL, 0, 0) 验证结果
echo 5. 使用 SendMessage(hCombo2, CB_GETLBTEXT, index, text) 获取文本
echo 6. 回退到模拟模式 (如果控件不存在)
echo.

echo 启动 MountImg.exe 测试真正的 Windows API...
echo ----------------------------------------

echo 2 | MountImg.exe

echo.
echo ----------------------------------------
echo 程序执行完毕，退出码: %ERRORLEVEL%
echo.

echo 检查目标 X: 驱动器...
if exist X:\ (
    echo ✅ SUCCESS: X: 驱动器已挂载
    echo 目录列表:
    dir X: /w
) else (
    echo ❌ FAILED: X: 驱动器未挂载
)

echo.
echo ========================================
echo 真正的 Windows API 实现技术说明:
echo ========================================
echo.
echo 1. 窗口句柄获取:
echo    HWND hMainDialog = GetActiveWindow();
echo    if (!hMainDialog) hMainDialog = GetForegroundWindow();
echo.
echo 2. 控件句柄获取:
echo    HWND hCombo2 = GetDlgItem(hMainDialog, ID_COMBO2);
echo.
echo 3. CB_SETCURSEL 执行:
echo    LRESULT result = SendMessage(hCombo2, CB_SETCURSEL, driveIndex, 0);
echo.
echo 4. 结果验证:
echo    LRESULT currentSel = SendMessage(hCombo2, CB_GETCURSEL, 0, 0);
echo.
echo 5. 文本获取:
echo    SendMessage(hCombo2, CB_GETLBTEXT, currentSel, currentText);
echo.
echo 6. 错误处理:
echo    - 句柄获取失败 → 回退到模拟模式
echo    - API 调用失败 → 返回 CB_ERR
echo    - 索引超出范围 → 错误提示
echo.
echo 预期输出:
echo   🔍 查找 ID_COMBO2 控件句柄...
echo   📍 主对话框句柄: 0x12345678
echo   📍 ID_COMBO2 句柄: 0x87654321
echo   📡 执行: SendMessage(0x87654321, CB_SETCURSEL, 3, 0)
echo   ✅ CB_SETCURSEL 执行成功:
echo     - 设置索引: 3
echo     - 返回值: 3 (成功)
echo   🔍 验证设置结果:
echo     - CB_GETCURSEL() 返回: 3
echo     - 当前显示文本: X:
echo     - 全局 drive 变量: X:
echo.
echo 如果控件不存在:
echo   ❌ CB_SETCURSEL 执行失败:
echo     - 错误: 无法获取 ID_COMBO2 控件句柄
echo     - 原因: 控件可能不存在或对话框未创建
echo   🔄 回退到模拟模式...
echo     - 模拟设置索引: 3
echo     - 模拟设置文本: X:
echo.

pause
