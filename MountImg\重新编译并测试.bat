@echo off
echo Rebuild and Test Script
echo ======================

echo.
echo 1. Checking Visual Studio environment...
where msbuild >nul 2>&1
if %errorlevel% neq 0 (
    echo Setting up Visual Studio environment...
    call "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\Common7\Tools\VsDevCmd.bat"
    if %errorlevel% neq 0 (
        echo ERROR: Cannot setup Visual Studio environment
        pause
        exit /b 1
    )
)

echo.
echo 2. Cleaning previous build...
if exist "Debug" (
    echo Cleaning Debug directory...
    del /q "Debug\*.*" 2>nul
)

echo.
echo 3. Rebuilding solution...
echo This may take a few minutes...
msbuild VirtualDiskLib.sln /p:Configuration=Debug /p:Platform=Win32 /m /v:minimal
if %errorlevel% neq 0 (
    echo.
    echo ERROR: Build failed!
    echo Please check the build output above for errors.
    pause
    exit /b 1
)

echo.
echo 4. Build successful! Checking output files...
if exist "Debug\VirtualDiskLib32.dll" (
    echo ✓ VirtualDiskLib32.dll created
    dir "Debug\VirtualDiskLib32.dll"
) else (
    echo ✗ VirtualDiskLib32.dll not found
)

if exist "Debug\VirtualDiskTool32.exe" (
    echo ✓ VirtualDiskTool32.exe created
    dir "Debug\VirtualDiskTool32.exe"
) else (
    echo ✗ VirtualDiskTool32.exe not found
)

echo.
echo 5. Starting debug output capture...
echo.
echo IMPORTANT: 
echo - Debug information will be output to both console and debug stream
echo - If you have DebugView running, you'll see detailed trace there too
echo - Look for detailed mount operation steps in the output below
echo.

echo 6. Running test with enhanced debug output...
echo ================================================
cd Debug
VirtualDiskTool32.exe

echo.
echo ================================================
echo Test completed.
echo.
echo If you still see error 1004 without detailed debug info:
echo 1. Check if the DLL was actually rebuilt (check timestamp above)
echo 2. Make sure no other process is using the old DLL
echo 3. Try running from Visual Studio debugger for more details
echo.
pause
