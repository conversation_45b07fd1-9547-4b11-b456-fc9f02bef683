# 命令行调试功能说明

## 📋 **功能概述**

为了帮助调试和分析命令行参数处理问题，我在config.c中添加了命令行信息打印功能。

### 添加的调试代码
```c
// 调试信息：打印命令行参数
#ifdef _DEBUG
{
    WCHAR debug_msg[1024];
    _snwprintf_s(debug_msg, _countof(debug_msg), _TRUNCATE, 
        L"Debug Info:\nFull Command Line: %s\nArgument Count: %d\n", 
        cmdline_ptr ? cmdline_ptr : L"(null)", argc);
    OutputDebugStringW(debug_msg);
    
    for (int j = 0; j < argc; j++) {
        _snwprintf_s(debug_msg, _countof(debug_msg), _TRUNCATE, 
            L"argv[%d]: %s\n", j, argv[j] ? argv[j] : L"(null)");
        OutputDebugStringW(debug_msg);
    }
}
#else
// Release版本：显示命令行信息（可选，用于调试）
if (argc > 0 && argv[0] && wcsstr(argv[0], L"debug")) {
    // 显示消息框...
}
#endif
```

## 🔍 **调试方法**

### 1. **Debug版本调试**

#### 使用Visual Studio调试器
1. **设置断点**: 在命令行解析代码处设置断点
2. **启动调试**: 按F5启动调试
3. **查看输出**: 在VS的"输出"窗口中查看调试信息

#### 使用DebugView工具
1. **下载DebugView**: 从Microsoft Sysinternals下载
2. **运行DebugView**: 启动DebugView.exe
3. **运行程序**: 运行Debug版本的config.exe
4. **查看输出**: 在DebugView中查看OutputDebugString的输出

#### 输出格式示例
```
Debug Info:
Full Command Line: "C:\Program Files\ImDisk\config.exe" /install "C:\Program Files\ImDisk"
Argument Count: 3
argv[0]: C:\Program Files\ImDisk\config.exe
argv[1]: /install
argv[2]: C:\Program Files\ImDisk
```

### 2. **Release版本调试**

#### 特殊调试模式
如果程序名包含"debug"，Release版本也会显示命令行信息：

1. **重命名程序**: 将config.exe重命名为config_debug.exe
2. **运行程序**: 运行重命名后的程序
3. **查看消息框**: 程序会显示包含命令行信息的消息框

#### 消息框内容示例
```
Command Line Debug Info:

Full Command: "C:\Program Files\ImDisk\config_debug.exe" /install

Argument Count: 2

Arguments:
[0]: C:\Program Files\ImDisk\config_debug.exe
[1]: /install
```

## 🛠 **使用场景**

### 1. **崩溃调试**
当程序在命令行处理时崩溃，可以：
- 查看完整的命令行内容
- 检查参数数量和内容
- 验证参数解析是否正确

### 2. **UAC权限提升调试**
在UAC权限提升过程中：
- 查看原始命令行
- 验证参数重构是否正确
- 检查"/UAC"参数的添加

### 3. **参数传递验证**
验证参数在不同阶段的传递：
- 程序启动时的原始参数
- UAC提升后的参数
- 子进程接收到的参数

## 📊 **调试信息详解**

### 1. **显示的信息**

#### 完整命令行
```
Full Command Line: "C:\Program Files\ImDisk\config.exe" /install "C:\Program Files\ImDisk"
```
- **包含**: 程序完整路径和所有参数
- **用途**: 查看系统传递给程序的原始命令行

#### 参数计数
```
Argument Count: 3
```
- **含义**: CommandLineToArgvW解析出的参数数量
- **包含**: 程序名(argv[0])也算作一个参数

#### 单个参数
```
argv[0]: C:\Program Files\ImDisk\config.exe
argv[1]: /install
argv[2]: C:\Program Files\ImDisk
```
- **argv[0]**: 总是程序的完整路径
- **argv[1]及以后**: 实际的命令行参数

### 2. **常见问题诊断**

#### 问题1: 参数丢失
```
// 期望
Argument Count: 3
argv[1]: /install
argv[2]: C:\Program Files\ImDisk

// 实际
Argument Count: 2
argv[1]: /install
```
**原因**: 路径中的空格导致参数解析错误
**解决**: 确保路径参数用引号包围

#### 问题2: 参数合并
```
// 期望
argv[1]: /install
argv[2]: C:\Program Files\ImDisk

// 实际
argv[1]: /installC:\Program Files\ImDisk
```
**原因**: 参数之间缺少空格分隔
**解决**: 检查参数构建逻辑

#### 问题3: 特殊字符问题
```
argv[1]: /install "C:\Program Files\ImDisk"
```
**问题**: 参数中包含了引号字符
**解决**: 检查引号的转义处理

## 🔧 **高级调试技巧**

### 1. **条件断点**
在Visual Studio中设置条件断点：
```c
// 只在特定参数时中断
argc > 2 && wcscmp(argv[1], L"/install") == 0
```

### 2. **内存窗口查看**
在调试器中查看字符串内存：
- 查看cmdline_ptr指向的内存
- 检查argv数组的内容
- 验证字符串的null终止

### 3. **调用堆栈分析**
当崩溃发生时：
- 查看调用堆栈
- 检查函数参数
- 分析崩溃前的执行路径

## 📝 **调试日志示例**

### 正常启动
```
Debug Info:
Full Command Line: "C:\ImDisk\config.exe"
Argument Count: 1
argv[0]: C:\ImDisk\config.exe
```

### 安装模式
```
Debug Info:
Full Command Line: "C:\ImDisk\config.exe" /install "C:\Program Files\ImDisk"
Argument Count: 3
argv[0]: C:\ImDisk\config.exe
argv[1]: /install
argv[2]: C:\Program Files\ImDisk
```

### UAC权限提升
```
Debug Info:
Full Command Line: "C:\ImDisk\config.exe" /UAC /install "C:\Program Files\ImDisk"
Argument Count: 4
argv[0]: C:\ImDisk\config.exe
argv[1]: /UAC
argv[2]: /install
argv[3]: C:\Program Files\ImDisk
```

## 🎯 **使用建议**

### 1. **开发阶段**
- 始终使用Debug版本进行开发
- 在DebugView中监控输出
- 设置相关断点进行详细调试

### 2. **测试阶段**
- 使用Release版本的debug模式
- 测试各种命令行参数组合
- 验证UAC权限提升功能

### 3. **部署前**
- 移除或禁用调试输出
- 确保Release版本不显示调试信息
- 进行最终的功能验证

## 🚀 **扩展功能**

### 1. **日志文件输出**
可以扩展为输出到日志文件：
```c
FILE* log_file = _wfopen(L"config_debug.log", L"a");
if (log_file) {
    fwprintf(log_file, L"Command Line: %s\n", cmdline_ptr);
    fclose(log_file);
}
```

### 2. **环境变量控制**
通过环境变量控制调试输出：
```c
if (GetEnvironmentVariableW(L"IMDISK_DEBUG", NULL, 0) > 0) {
    // 显示调试信息
}
```

### 3. **网络调试**
发送调试信息到网络端点：
```c
// 发送到本地调试服务器
// 适用于远程调试场景
```

这个调试功能将帮助您快速定位和解决命令行参数相关的问题！

---
**功能添加时间**: 2025年7月16日  
**调试方式**: OutputDebugString + MessageBox  
**适用版本**: Debug版本 + Release版本(条件触发)  
**状态**: 已实现 ✅  
**用途**: 命令行参数调试和问题诊断 🔍
