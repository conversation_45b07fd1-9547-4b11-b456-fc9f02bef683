@echo off
chcp 65001 >nul
echo ========================================
echo Testing Original Drive Enumeration Logic
echo ========================================

echo.
echo 这个测试验证基于原始代码的驱动器枚举逻辑
echo.
echo 原始逻辑特点:
echo 1. 使用 GetLogicalDrives() 获取驱动器掩码
echo 2. 使用 CreateFile() 检查驱动器访问性
echo 3. 使用 DeviceIoControl() 检测 ImDisk 设备
echo 4. 智能判断驱动器可用性
echo.

echo 驱动器可用性判断规则:
echo   - 掩码位为 0: 驱动器不存在 (可用)
echo   - 掩码位为 1 且是 ImDisk: ImDisk 虚拟驱动器 (可重用)
echo   - 掩码位为 1 且非 ImDisk: 已占用驱动器 (不可用)
echo.

echo 启动 MountImg.exe 测试原始枚举逻辑...
echo ----------------------------------------

echo 2 | MountImg.exe

echo.
echo ----------------------------------------
echo 程序执行完毕，退出码: %ERRORLEVEL%
echo.

echo 检查目标 X: 驱动器...
if exist X:\ (
    echo ✅ SUCCESS: X: 驱动器已挂载
    echo 目录列表:
    dir X: /w
) else (
    echo ❌ FAILED: X: 驱动器未挂载
)

echo.
echo ========================================
echo 原始驱动器枚举逻辑技术说明:
echo ========================================
echo.
echo 1. 逻辑驱动器掩码获取:
echo    DWORD mask = GetLogicalDrives();
echo    // 每个位代表一个驱动器 (A=bit0, B=bit1, ...)
echo.
echo 2. 驱动器设备文件访问:
echo    wcscpy_s(txt, 8, L"\\\\.\\A:");
echo    HANDLE h = CreateFile(txt, 0, FILE_SHARE_READ | FILE_SHARE_WRITE, 
echo                         NULL, OPEN_EXISTING, FILE_ATTRIBUTE_NORMAL, NULL);
echo.
echo 3. ImDisk 设备检测:
echo    BOOL is_imdisk = DeviceIoControl(h, IOCTL_IMDISK_QUERY_VERSION, 
echo                                     NULL, 0, &version, sizeof(version), 
echo                                     &data_size, NULL);
echo.
echo 4. 可用性判断逻辑:
echo    if (!(mask & mask_bit)) {
echo        // 驱动器不存在，可用
echo        shouldAdd = TRUE;
echo    } else if (is_imdisk) {
echo        // ImDisk 虚拟驱动器，可重用
echo        shouldAdd = TRUE;
echo    } else {
echo        // 已占用驱动器，不可用
echo        shouldAdd = FALSE;
echo    }
echo.
echo 5. 掩码位移动:
echo    mask_bit <<= 1;  // 移动到下一个驱动器位
echo    txt[4]++;        // 从 A: 到 B: 到 C: ...
echo.
echo 预期输出示例:
echo   Enumerating available drives (using original logic):
echo     A: - Available (Mask: 0, ImDisk: No)
echo     B: - Available (Mask: 0, ImDisk: No)
echo     C: - Occupied (Mask: 1, ImDisk: No)
echo     D: - Occupied (Mask: 1, ImDisk: No)
echo     E: - Occupied (Mask: 1, ImDisk: No)
echo     F: - Occupied (Mask: 1, ImDisk: No)  ← F盘被正确识别但标记为占用
echo     ...
echo     X: - Available (Mask: 0, ImDisk: No)
echo     * Found target drive X: at index 15
echo.
echo ✅ 优势:
echo   - 更精确的驱动器状态检测
echo   - 支持 ImDisk 虚拟驱动器重用
echo   - 避免占用已使用的驱动器
echo   - 与原始界面逻辑完全一致
echo.

pause
