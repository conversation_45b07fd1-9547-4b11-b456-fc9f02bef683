@echo off
chcp 65001 >nul
echo ========================================
echo Testing Direct Mount Implementation
echo ========================================

echo.
echo 这个测试验证不使用 MountImg32.exe 的直接挂载实现
echo.
echo 实现特点:
echo 1. 不调用外部 MountImg32.exe 进程
echo 2. 直接使用 ImDisk API 进行挂载
echo 3. 备选使用 ImDisk 命令行方式
echo 4. 完全独立的挂载实现
echo 5. 避免进程间通信开销
echo.

echo 实现的双重策略:
echo Strategy 1: 直接 ImDisk API 调用 (DirectImDiskMount)
echo   - 动态加载 imdisk.cpl
echo   - 获取 ImDiskCreateDevice 函数指针
echo   - 直接调用 API 创建虚拟设备
echo   - 设置设备参数和标志
echo.
echo Strategy 2: ImDisk 命令行调用 (备选方案)
echo   - 构建 imdisk 命令行
echo   - 启动 imdisk.exe 进程
echo   - 等待进程完成
echo   - 验证挂载结果
echo.

echo 启动 VirtualDiskTool32.exe 测试直接实现...
echo ----------------------------------------

VirtualDiskTool32.exe --test-mount

echo.
echo ----------------------------------------
echo 程序执行完毕，退出码: %ERRORLEVEL%
echo.

if %ERRORLEVEL% EQU 0 (
    echo ✅ SUCCESS: 直接挂载实现执行成功
) else (
    echo ❌ FAILED: 程序运行出错，退出码: %ERRORLEVEL%
)

echo.
echo 检查目标 X: 驱动器...
if exist X:\ (
    echo ✅ SUCCESS: X: 驱动器已挂载 (直接实现正常工作)
    echo 目录列表:
    dir X: /w
) else (
    echo ❌ FAILED: X: 驱动器未挂载
)

echo.
echo ========================================
echo 直接挂载实现技术说明:
echo ========================================
echo.
echo ✅ 直接 ImDisk API 实现 (DirectImDiskMount):
echo   HMODULE h_cpl = LoadLibraryA("imdisk.cpl");
echo   ImDiskCreateDeviceProc ImDiskCreateDevice = 
echo       (ImDiskCreateDeviceProc)GetProcAddress(h_cpl, "ImDiskCreateDevice");
echo   
echo   IMDISK_CREATE_DATA create_data = {0};
echo   create_data.DeviceNumber = IMDISK_AUTO_DEVICE_NUMBER;
echo   create_data.Flags = IMDISK_TYPE_FILE | IMDISK_DEVICE_TYPE_HD;
echo   if (g_readonly) create_data.Flags |= IMDISK_OPTION_RO;
echo   wcscpy_s(create_data.FileName, g_filename);
echo   
echo   BOOL result = ImDiskCreateDevice(NULL, &create_data, FALSE);
echo.
echo ✅ ImDisk 命令行实现 (备选方案):
echo   _snwprintf(imdisk_cmd, _countof(imdisk_cmd),
echo              L"imdisk -a -f \"%s\" -m %s%s%s",
echo              g_filename, g_drive,
echo              g_readonly ? L" -o ro" : L"",
echo              g_partition > 0 ? L" -p" : L"");
echo   
echo   CreateProcessW(NULL, imdisk_cmd, NULL, NULL, FALSE, 0, NULL, NULL, &si, &pi);
echo   WaitForSingleObject(pi.hProcess, 30000);
echo.
echo ✅ 双重策略流程:
echo   1. 首先尝试直接 ImDisk API 调用
echo   2. 如果 API 调用失败，使用命令行方式
echo   3. 两种方式都失败才报告错误
echo   4. 提供最大的兼容性和可靠性
echo.
echo ✅ 优势:
echo   - 高性能: 直接 API 调用，无进程开销
echo   - 高可靠: 双重策略，提供备选方案
echo   - 独立性: 不依赖外部 MountImg32.exe
echo   - 兼容性: 支持所有 ImDisk 功能
echo   - 可控性: 完全控制挂载过程
echo.
echo ✅ 错误处理:
echo   - API 加载失败: "Cannot load imdisk.cpl"
echo   - 函数获取失败: "Cannot find ImDiskCreateDevice function"
echo   - 设备创建失败: "ImDiskCreateDevice failed"
echo   - 命令行失败: "ImDisk command timeout"
echo.

pause
