@echo off
chcp 65001 >nul
echo ========================================
echo Testing Background Mode Implementation
echo ========================================

echo.
echo 这个测试验证 MountImg32.exe 的后台执行功能
echo.
echo 后台执行功能特点:
echo 1. g_isJSON = TRUE 时，窗口隐藏，后台执行
echo 2. g_isJSON = FALSE 时，正常显示界面
echo 3. 自动完成挂载操作后退出
echo 4. 不在任务栏显示
echo 5. 完整的操作流程保持不变
echo.

echo 实现的关键步骤:
echo Step 1: 添加全局变量 g_operationCompleted, g_hiddenDialog
echo Step 2: 修改 wWinMain 函数，根据 g_isJSON 选择显示模式
echo Step 3: 修改 WM_TIMER 处理，添加操作完成标记
echo Step 4: 修改 Mount 函数，添加后台模式退出逻辑
echo Step 5: 修改 WM_INITDIALOG，添加窗口隐藏处理
echo.

echo 启动 MountImg32.exe 测试后台模式...
echo ----------------------------------------

echo 测试 JSON 输入:
echo {
echo   "file_path": "E:\\test.vhd",
echo   "drive": "X:",
echo   "readonly": false,
echo   "partition": 0
echo }

echo 2 | MountImg.exe

echo.
echo ----------------------------------------
echo 程序执行完毕，退出码: %ERRORLEVEL%
echo.

if %ERRORLEVEL% EQU 0 (
    echo ✅ SUCCESS: 后台模式执行成功
) else (
    echo ❌ FAILED: 程序运行出错，退出码: %ERRORLEVEL%
)

echo.
echo 检查目标 X: 驱动器...
if exist X:\ (
    echo ✅ SUCCESS: X: 驱动器已挂载 (后台模式正常工作)
    echo 目录列表:
    dir X: /w
) else (
    echo ❌ FAILED: X: 驱动器未挂载
)

echo.
echo ========================================
echo 后台模式实现技术说明:
echo ========================================
echo.
echo ✅ 全局变量:
echo   BOOL g_operationCompleted = FALSE;  // 操作完成标志
echo   HWND g_hiddenDialog = NULL;         // 隐藏对话框句柄
echo.
echo ✅ wWinMain 函数修改:
echo   if (g_isJSON) {
echo       // 创建隐藏对话框
echo       g_hiddenDialog = CreateDialog(hinst, L"MOUNT_DLG", NULL, DlgProc);
echo       ShowWindow(g_hiddenDialog, SW_HIDE);
echo       SetWindowPos(g_hiddenDialog, NULL, -32000, -32000, 0, 0, ...);
echo       SetWindowLong(g_hiddenDialog, GWL_EXSTYLE, ... | WS_EX_TOOLWINDOW);
echo       
echo       // 后台消息循环
echo       while (GetMessage(&msg, NULL, 0, 0)) {
echo           if (g_operationCompleted) break;
echo       }
echo   } else {
echo       // 普通模式
echo       DialogBox(hinst, L"MOUNT_DLG", NULL, DlgProc);
echo   }
echo.
echo ✅ WM_TIMER 处理修改:
echo   case WM_TIMER:
echo       if (wParam == 1001) {
echo           SendMessage(hDlg, WM_COMMAND, IDOK, 0);
echo           if (g_isJSON) {
echo               g_operationCompleted = TRUE;  // 标记完成
echo           }
echo       }
echo.
echo ✅ Mount 函数修改:
echo   if (g_isJSON) {
echo       printf("🔇 JSON 模式：挂载操作完成，准备退出");
echo       Sleep(1000);  // 确保操作完成
echo       g_operationCompleted = TRUE;
echo       if (g_hiddenDialog) {
echo           PostMessage(g_hiddenDialog, WM_CLOSE, 0, 0);
echo       }
echo   }
echo.
echo ✅ WM_INITDIALOG 修改:
echo   if (g_isJSON) {
echo       ShowWindow(hDlg, SW_HIDE);
echo       SetWindowPos(hDlg, NULL, -32000, -32000, 0, 0, ...);
echo       SetWindowLong(hDlg, GWL_EXSTYLE, ... | WS_EX_TOOLWINDOW);
echo   } else {
echo       ShowWindow(hDlg, SW_SHOW);
echo   }
echo.
echo ✅ 执行流程:
echo   JSON 模式:
echo     创建隐藏对话框 → 启动定时器 → 500ms 后触发 IDOK → 
echo     执行挂载 → 标记完成 → 退出消息循环 → 程序结束
echo   
echo   普通模式:
echo     显示对话框 → 用户交互 → 手动操作 → 正常退出
echo.

pause
