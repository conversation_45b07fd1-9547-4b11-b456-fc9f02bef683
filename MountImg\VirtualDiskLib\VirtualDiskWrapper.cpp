/*
 * VirtualDiskWrapper.cpp
 * 虚拟磁盘库统一封装接口实现
 * 
 * 功能：将VirtualDiskLib的所有函数封装成统一的C++接口
 * 支持：进度回调、任务控制、异常处理
 * 兼容性：Windows XP及以上版本，C++14标准
 */

#define _CRT_SECURE_NO_WARNINGS
#include "VirtualDiskWrapper.h"
#include <sstream>
#include <chrono>
#include <iomanip>
#include <windows.h>

// ========================================
// 静态成员变量定义
// ========================================

thread_local SimpleProgressCallback VirtualDiskWrapper::s_current_progress_callback = nullptr;
thread_local SimpleQueryTaskControlCallback VirtualDiskWrapper::s_current_control_callback = nullptr;

// ========================================
// 构造函数和析构函数
// ========================================

VirtualDiskWrapper::VirtualDiskWrapper() {
    OutputDebugStringA("🔧 VirtualDiskWrapper: Constructor called\n");
}

VirtualDiskWrapper::~VirtualDiskWrapper() {
    OutputDebugStringA("🧹 VirtualDiskWrapper: Destructor called\n");
    
    // 如果已初始化，执行清理
    if (m_initialized.load()) {
        try {
            CleanupVirtualDiskLib();
        } catch (...) {
            OutputDebugStringA("⚠️  VirtualDiskWrapper: Exception during cleanup in destructor\n");
        }
    }
}

// ========================================
// 回调函数适配器实现
// ========================================

void VirtualDiskWrapper::ProgressCallbackAdapter(const std::string& taskId, int progress, const std::string& matchResult) {
    // 将原始回调适配为简化回调
    if (s_current_progress_callback) {
        s_current_progress_callback(taskId, progress);
    }
}

bool VirtualDiskWrapper::QueryTaskControlCallbackAdapter(const std::string& taskId, int controlType) {
    // 将原始回调适配为简化回调
    if (s_current_control_callback) {
        return s_current_control_callback(taskId, controlType);
    }
    return false; // 默认不取消或暂停
}

// ========================================
// 辅助函数实现
// ========================================

std::string VirtualDiskWrapper::GenerateTaskId(const std::string& operation) const {
    // 生成基于时间戳和操作名称的任务ID
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(now.time_since_epoch()) % 1000;
    
    std::ostringstream oss;
    oss << operation << "_" << std::put_time(std::localtime(&time_t), "%Y%m%d_%H%M%S") << "_" << ms.count();
    return oss.str();
}

void VirtualDiskWrapper::ValidateInitialization() const {
    if (!m_initialized.load()) {
        throw std::runtime_error("VirtualDiskWrapper not initialized. Call InitializeVirtualDiskLib() first.");
    }
}

// ========================================
// 核心接口函数实现
// ========================================

std::string VirtualDiskWrapper::GetLibraryInfo(
    const std::string& params,
    SimpleProgressCallback progressCallback,
    const std::string& taskId,
    SimpleQueryTaskControlCallback queryTaskControlCb) {
    
    std::lock_guard<std::mutex> lock(m_operation_mutex);
    
    // 设置当前回调
    s_current_progress_callback = progressCallback;
    s_current_control_callback = queryTaskControlCb;
    
    // 生成任务ID
    std::string actualTaskId = taskId.empty() ? GenerateTaskId("GetLibraryInfo") : taskId;
    
    try {
        // 调用原始DLL函数
        const char* result = ::GetLibraryInfo(
            params.c_str(),
            progressCallback ? ProgressCallbackAdapter : nullptr,
            actualTaskId.c_str(),
            queryTaskControlCb ? QueryTaskControlCallbackAdapter : nullptr
        );
        
        return result ? std::string(result) : R"({"status": "error", "message": "GetLibraryInfo returned null"})";
        
    } catch (const std::exception& e) {
        std::ostringstream oss;
        oss << R"({"status": "error", "message": "Exception in GetLibraryInfo: )" << e.what() << R"("})";
        return oss.str();
    } catch (...) {
        return R"({"status": "error", "message": "Unknown exception in GetLibraryInfo"})";
    }
}

std::string VirtualDiskWrapper::InitializeVirtualDiskLib(
    const std::string& params,
    SimpleProgressCallback progressCallback,
    const std::string& taskId,
    SimpleQueryTaskControlCallback queryTaskControlCb) {
    
    std::lock_guard<std::mutex> lock(m_operation_mutex);
    
    // 设置当前回调
    s_current_progress_callback = progressCallback;
    s_current_control_callback = queryTaskControlCb;
    
    // 生成任务ID
    std::string actualTaskId = taskId.empty() ? GenerateTaskId("InitializeVirtualDiskLib") : taskId;
    
    try {
        // 调用原始DLL函数
        const char* result = ::InitializeVirtualDiskLib(
            params.c_str(),
            progressCallback ? ProgressCallbackAdapter : nullptr,
            actualTaskId.c_str(),
            queryTaskControlCb ? QueryTaskControlCallbackAdapter : nullptr
        );
        
        std::string resultStr = result ? std::string(result) : R"({"status": "error", "message": "InitializeVirtualDiskLib returned null"})";
        
        // 检查初始化是否成功
        if (resultStr.find(R"("status": "success")") != std::string::npos) {
            m_initialized.store(true);
            OutputDebugStringA("✅ VirtualDiskWrapper: Library initialized successfully\n");
        }
        
        return resultStr;
        
    } catch (const std::exception& e) {
        std::ostringstream oss;
        oss << R"({"status": "error", "message": "Exception in InitializeVirtualDiskLib: )" << e.what() << R"("})";
        return oss.str();
    } catch (...) {
        return R"({"status": "error", "message": "Unknown exception in InitializeVirtualDiskLib"})";
    }
}

std::string VirtualDiskWrapper::CleanupVirtualDiskLib(
    const std::string& params,
    SimpleProgressCallback progressCallback,
    const std::string& taskId,
    SimpleQueryTaskControlCallback queryTaskControlCb) {
    
    std::lock_guard<std::mutex> lock(m_operation_mutex);
    
    // 设置当前回调
    s_current_progress_callback = progressCallback;
    s_current_control_callback = queryTaskControlCb;
    
    // 生成任务ID
    std::string actualTaskId = taskId.empty() ? GenerateTaskId("CleanupVirtualDiskLib") : taskId;
    
    try {
        // 调用原始DLL函数
        const char* result = ::CleanupVirtualDiskLib(
            params.c_str(),
            progressCallback ? ProgressCallbackAdapter : nullptr,
            actualTaskId.c_str(),
            queryTaskControlCb ? QueryTaskControlCallbackAdapter : nullptr
        );
        
        std::string resultStr = result ? std::string(result) : R"({"status": "error", "message": "CleanupVirtualDiskLib returned null"})";
        
        // 检查清理是否成功
        if (resultStr.find(R"("status": "success")") != std::string::npos) {
            m_initialized.store(false);
            OutputDebugStringA("✅ VirtualDiskWrapper: Library cleaned up successfully\n");
        }
        
        return resultStr;
        
    } catch (const std::exception& e) {
        std::ostringstream oss;
        oss << R"({"status": "error", "message": "Exception in CleanupVirtualDiskLib: )" << e.what() << R"("})";
        return oss.str();
    } catch (...) {
        return R"({"status": "error", "message": "Unknown exception in CleanupVirtualDiskLib"})";
    }
}

std::string VirtualDiskWrapper::MountVirtualDisk(
    const std::string& params,
    SimpleProgressCallback progressCallback,
    const std::string& taskId,
    SimpleQueryTaskControlCallback queryTaskControlCb) {

    std::lock_guard<std::mutex> lock(m_operation_mutex);

    // 验证初始化状态
    try {
        ValidateInitialization();
    } catch (const std::exception& e) {
        std::ostringstream oss;
        oss << R"({"status": "error", "message": ")" << e.what() << R"("})";
        return oss.str();
    }

    // 设置当前回调
    s_current_progress_callback = progressCallback;
    s_current_control_callback = queryTaskControlCb;

    // 生成任务ID
    std::string actualTaskId = taskId.empty() ? GenerateTaskId("MountVirtualDisk") : taskId;

    try {
        // 调用原始DLL函数
        const char* result = ::MountVirtualDisk(
            params.c_str(),
            progressCallback ? ProgressCallbackAdapter : nullptr,
            actualTaskId.c_str(),
            queryTaskControlCb ? QueryTaskControlCallbackAdapter : nullptr
        );

        return result ? std::string(result) : R"({"status": "error", "message": "MountVirtualDisk returned null"})";

    } catch (const std::exception& e) {
        std::ostringstream oss;
        oss << R"({"status": "error", "message": "Exception in MountVirtualDisk: )" << e.what() << R"("})";
        return oss.str();
    } catch (...) {
        return R"({"status": "error", "message": "Unknown exception in MountVirtualDisk"})";
    }
}

std::string VirtualDiskWrapper::UnmountVirtualDisk(
    const std::string& params,
    SimpleProgressCallback progressCallback,
    const std::string& taskId,
    SimpleQueryTaskControlCallback queryTaskControlCb) {

    std::lock_guard<std::mutex> lock(m_operation_mutex);

    // 验证初始化状态
    try {
        ValidateInitialization();
    } catch (const std::exception& e) {
        std::ostringstream oss;
        oss << R"({"status": "error", "message": ")" << e.what() << R"("})";
        return oss.str();
    }

    // 设置当前回调
    s_current_progress_callback = progressCallback;
    s_current_control_callback = queryTaskControlCb;

    // 生成任务ID
    std::string actualTaskId = taskId.empty() ? GenerateTaskId("UnmountVirtualDisk") : taskId;

    try {
        // 调用原始DLL函数
        const char* result = ::UnmountVirtualDisk(
            params.c_str(),
            progressCallback ? ProgressCallbackAdapter : nullptr,
            actualTaskId.c_str(),
            queryTaskControlCb ? QueryTaskControlCallbackAdapter : nullptr
        );

        return result ? std::string(result) : R"({"status": "error", "message": "UnmountVirtualDisk returned null"})";

    } catch (const std::exception& e) {
        std::ostringstream oss;
        oss << R"({"status": "error", "message": "Exception in UnmountVirtualDisk: )" << e.what() << R"("})";
        return oss.str();
    } catch (...) {
        return R"({"status": "error", "message": "Unknown exception in UnmountVirtualDisk"})";
    }
}

std::string VirtualDiskWrapper::GetMountStatus(
    const std::string& params,
    SimpleProgressCallback progressCallback,
    const std::string& taskId,
    SimpleQueryTaskControlCallback queryTaskControlCb) {

    std::lock_guard<std::mutex> lock(m_operation_mutex);

    // 验证初始化状态
    try {
        ValidateInitialization();
    } catch (const std::exception& e) {
        std::ostringstream oss;
        oss << R"({"status": "error", "message": ")" << e.what() << R"("})";
        return oss.str();
    }

    // 设置当前回调
    s_current_progress_callback = progressCallback;
    s_current_control_callback = queryTaskControlCb;

    // 生成任务ID
    std::string actualTaskId = taskId.empty() ? GenerateTaskId("GetMountStatus") : taskId;

    try {
        // 调用原始DLL函数
        const char* result = ::GetMountStatus(
            params.c_str(),
            progressCallback ? ProgressCallbackAdapter : nullptr,
            actualTaskId.c_str(),
            queryTaskControlCb ? QueryTaskControlCallbackAdapter : nullptr
        );

        return result ? std::string(result) : R"({"status": "error", "message": "GetMountStatus returned null"})";

    } catch (const std::exception& e) {
        std::ostringstream oss;
        oss << R"({"status": "error", "message": "Exception in GetMountStatus: )" << e.what() << R"("})";
        return oss.str();
    } catch (...) {
        return R"({"status": "error", "message": "Unknown exception in GetMountStatus"})";
    }
}
