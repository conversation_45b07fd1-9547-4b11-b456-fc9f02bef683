@echo off
echo ========================================
echo Testing Background Mode Implementation
echo ========================================

echo.
echo Testing MountImg32.exe background mode...
echo.

echo Starting MountImg32.exe with JSON input...
echo 2 | .\MountImg32.exe

echo.
echo Program finished with exit code: %ERRORLEVEL%

if %ERRORLEVEL% EQU 0 (
    echo SUCCESS: Background mode executed successfully
) else (
    echo FAILED: Program error, exit code: %ERRORLEVEL%
)

echo.
echo Checking X: drive...
if exist X:\ (
    echo SUCCESS: X: drive is mounted
    dir X: /w
) else (
    echo FAILED: X: drive is not mounted
)

echo.
echo Test completed.
pause
