# PowerShell script to create complete CAB file using makecab
Write-Host "Creating complete CAB file using makecab..." -ForegroundColor Green

# Clean up existing files
if (Test-Path "files_complete.cab") { Remove-Item "files_complete.cab" -Force }
if (Test-Path "complete.ddf") { Remove-Item "complete.ddf" -Force }

# Create DDF file content
Write-Host "Creating DDF directive file..." -ForegroundColor Yellow

$ddfContent = @"
.OPTION EXPLICIT
.Set CabinetNameTemplate=files_complete.cab
.Set DiskDirectoryTemplate=.
.Set CompressionType=MSZIP
.Set UniqueFiles=OFF
.Set Cabinet=ON
.Set Compress=ON
.Set SourceDir=files

"@

# Get all files from files directory
$allFiles = Get-ChildItem "files" -Recurse -File
Write-Host "Found $($allFiles.Count) files to include" -ForegroundColor Green

# Add each file to DDF with proper path mapping
foreach ($file in $allFiles) {
    $relativePath = $file.FullName.Substring((Get-Item "files").FullName.Length + 1)
    $ddfContent += "`"$relativePath`" `"$relativePath`"`n"
    Write-Host "  Adding: $relativePath"
}

# Write DDF file
$ddfContent | Out-File "complete.ddf" -Encoding ASCII

Write-Host "Running makecab..." -ForegroundColor Yellow
$result = Start-Process -FilePath "makecab" -ArgumentList "/F", "complete.ddf" -Wait -PassThru -NoNewWindow

if ($result.ExitCode -eq 0) {
    Write-Host "SUCCESS: CAB file created!" -ForegroundColor Green
    
    if (Test-Path "files_complete.cab") {
        $cabFile = Get-Item "files_complete.cab"
        Write-Host "CAB file size: $($cabFile.Length) bytes" -ForegroundColor Green
        
        # Test the CAB file
        Write-Host "Testing CAB file..." -ForegroundColor Yellow
        if (Test-Path "test_complete") { Remove-Item "test_complete" -Recurse -Force }
        New-Item -ItemType Directory -Path "test_complete" -Force | Out-Null
        
        $extractResult = Start-Process -FilePath "extrac32.exe" -ArgumentList "/e", "/l", "test_complete", "files_complete.cab" -Wait -PassThru -NoNewWindow
        
        if ($extractResult.ExitCode -eq 0) {
            $extractedFiles = Get-ChildItem "test_complete" -Recurse -File
            Write-Host "Extracted $($extractedFiles.Count) files" -ForegroundColor Green
            
            if (Test-Path "test_complete\config.exe") {
                Write-Host "SUCCESS: config.exe found in root directory!" -ForegroundColor Green
            } else {
                Write-Host "WARNING: config.exe not found in root directory" -ForegroundColor Yellow
            }
            
            if (Test-Path "test_complete\driver") {
                Write-Host "SUCCESS: driver directory found!" -ForegroundColor Green
            }
            
            if (Test-Path "test_complete\lang") {
                Write-Host "SUCCESS: lang directory found!" -ForegroundColor Green
            }
        } else {
            Write-Host "ERROR: Failed to extract CAB file" -ForegroundColor Red
        }
        
        # Clean up test directory
        Remove-Item "test_complete" -Recurse -Force -ErrorAction SilentlyContinue
        
    } else {
        Write-Host "ERROR: CAB file was not created" -ForegroundColor Red
    }
} else {
    Write-Host "ERROR: makecab failed with exit code $($result.ExitCode)" -ForegroundColor Red
}

# Clean up DDF file
Remove-Item "complete.ddf" -Force -ErrorAction SilentlyContinue

Write-Host "Done!" -ForegroundColor Green
