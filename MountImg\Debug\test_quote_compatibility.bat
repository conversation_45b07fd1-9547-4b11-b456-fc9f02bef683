@echo off
chcp 65001 >nul
echo ========================================
echo Testing JSON Quote Compatibility Parser
echo ========================================

echo.
echo 这个测试验证 JSON 解析器的引号兼容性功能
echo.
echo 问题说明:
echo 输入: "{"file_path":"E:\\004_VMDK\\666666.vmdk","drive":"X:","readonly":false,"partition":1}"
echo 接收: {file_path:E:\\004_VMDK\\666666.vmdk,drive:X:,readonly:false,partition:1}
echo 原因: Windows 命令行解析器移除了双引号
echo.

echo 解决方案:
echo 1. 转义双引号输入法:
echo    MountImg32.exe /JSON "{\"file_path\":\"E:\\\\004_VMDK\\\\666666.vmdk\",\"drive\":\"X:\",\"readonly\":false,\"partition\":1}"
echo.
echo 2. 兼容性解析器 (已实现):
echo    - 支持标准格式: "key": "value"
echo    - 支持无引号格式: key: value
echo    - 自动检测和处理两种格式
echo.

echo 启动 MountImg.exe 测试兼容性解析器...
echo ----------------------------------------

echo 2 | MountImg.exe

echo.
echo ----------------------------------------
echo 程序执行完毕，退出码: %ERRORLEVEL%
echo.

echo 检查挂载结果...
if exist X:\ (
    echo ✅ SUCCESS: X: 驱动器已挂载
    echo 目录列表:
    dir X: /w
) else (
    echo ❌ FAILED: X: 驱动器未挂载
)

echo.
echo ========================================
echo 兼容性解析器技术说明:
echo ========================================
echo.
echo 1. 双重键名查找:
echo    - 首先查找: "key" (标准格式)
echo    - 失败则查找: key (无引号格式)
echo.
echo 2. 多种值格式支持:
echo    - 带引号字符串: "value"
echo    - 无引号字符串: value (到逗号或右括号)
echo    - 布尔值: true/false
echo    - 数字值: 123
echo.
echo 3. Windows 命令行解析问题:
echo    原始输入: "{"key":"value"}"
echo    系统解析后: {key:value}
echo    解析器处理: ✅ 兼容两种格式
echo.
echo 4. 推荐的输入方法:
echo    方法1: 使用转义 \"key\":\"value\"
echo    方法2: 依赖兼容性解析器处理
echo.

pause
