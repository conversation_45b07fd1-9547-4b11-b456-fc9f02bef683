@echo off
chcp 65001 >nul
echo ========================================
echo Testing Direct RM Logic Implementation
echo ========================================

echo.
echo 这个测试验证直接实现 ImDisk-Dlg.c RM 代码块逻辑的 UnmountVirtualDisk 函数
echo.
echo 实现特点:
echo 1. 直接使用 ImDisk-Dlg.c 第446-509行的 RM 处理逻辑
echo 2. 不调用外部 ImDisk-Dlg.exe 程序
echo 3. 直接进行底层设备操作
echo 4. 完整的8步卸载流程
echo 5. 详细的调试信息输出
echo.

echo 卸载流程 (8个步骤):
echo Step 1: Opening ImDisk device (ImDiskOpenDeviceByMountPoint)
echo Step 2: Querying device info (IOCTL_IMDISK_QUERY_DEVICE)
echo Step 3: Sending device remove notification (WM_DEVICECHANGE)
echo Step 4: Flushing file buffers (FlushFileBuffers)
echo Step 5: Locking volume (FSCTL_LOCK_VOLUME)
echo Step 6: Dismounting volume (FSCTL_DISMOUNT_VOLUME)
echo Step 7: Ejecting media (IOCTL_STORAGE_EJECT_MEDIA)
echo Step 8: Removing mount point (ImDiskRemoveMountPoint)
echo.

echo 启动 VirtualDiskTool32.exe 测试直接 RM 逻辑...
echo ----------------------------------------

VirtualDiskTool32.exe --test-unmount

echo.
echo ----------------------------------------
echo 测试完毕，退出码: %ERRORLEVEL%
echo.

echo 检查 X: 驱动器状态...
if exist X:\ (
    echo ❌ FAILED: X: 驱动器仍然存在 (卸载失败)
    echo 目录列表:
    dir X: /w
) else (
    echo ✅ SUCCESS: X: 驱动器已卸载
)

echo.
echo ========================================
echo 直接 RM 逻辑实现技术说明:
echo ========================================
echo.
echo 1. 设备打开 (参考 ImDisk-Dlg.c 第449-454行):
echo    DWORD access_list[] = { GENERIC_READ | GENERIC_WRITE, GENERIC_READ, GENERIC_WRITE };
echo    for (n_access = 0; n_access < _countof(access_list); n_access++) {
echo        h = (HANDLE)ImDiskOpenDeviceByMountPoint(mount_point, access_list[n_access]);
echo        if (h != INVALID_HANDLE_VALUE) break;
echo    }
echo.
echo 2. 设备信息查询 (参考 ImDisk-Dlg.c 第455-460行):
echo    struct { IMDISK_CREATE_DATA icd; WCHAR buff[MAX_PATH + 15]; } create_data = {};
echo    DeviceIoControl(h, IOCTL_IMDISK_QUERY_DEVICE, NULL, 0, &create_data, sizeof(create_data), &dw, NULL);
echo.
echo 3. 设备移除通知 (参考 ImDisk-Dlg.c 第469-472行):
echo    DEV_BROADCAST_VOLUME dbv = { sizeof(dbv), DBT_DEVTYP_VOLUME };
echo    dbv.dbcv_unitmask = 1 << (mount_point[0] - L'A');
echo    SendMessageTimeout(HWND_BROADCAST, WM_DEVICECHANGE, DBT_DEVICEREMOVEPENDING, ...);
echo.
echo 4. 文件缓冲区刷新 (参考 ImDisk-Dlg.c 第474-475行):
echo    FlushFileBuffers(h);
echo.
echo 5. 卷锁定 (参考 ImDisk-Dlg.c 第477-481行):
echo    DeviceIoControl(h, FSCTL_LOCK_VOLUME, NULL, 0, NULL, 0, &dw, NULL);
echo.
echo 6. 卷卸载 (参考 ImDisk-Dlg.c 第483-484行):
echo    DeviceIoControl(h, FSCTL_DISMOUNT_VOLUME, NULL, 0, NULL, 0, &dw, NULL);
echo.
echo 7. 媒体弹出 (参考 ImDisk-Dlg.c 第497-501行):
echo    DeviceIoControl(h, IOCTL_STORAGE_EJECT_MEDIA, NULL, 0, NULL, 0, &dw, NULL);
echo    // 如果失败，尝试强制移除:
echo    ImDiskForceRemoveDevice(h, 0);
echo.
echo 8. 挂载点移除 (参考 ImDisk-Dlg.c 第504-508行):
echo    ImDiskRemoveMountPoint(mount_point);
echo.
echo 错误处理:
echo - 设备打开失败: "Cannot open ImDisk device"
echo - 设备查询失败: "X: is not an ImDisk virtual disk"
echo - 媒体弹出失败: "Cannot remove device"
echo - 挂载点移除失败: "Cannot remove mount point"
echo.
echo 调试输出示例:
echo   === Starting Unmount Operation (Direct RM Logic) ===
echo   Step 1: Opening ImDisk device...
echo   ✅ Device opened with access mode 0
echo   Step 2: Querying device info...
echo   ✅ Device info queried successfully
echo   Step 3: Sending device remove notification...
echo   ✅ Device remove notification sent
echo   Step 4: Flushing file buffers...
echo   ✅ File buffers flushed
echo   Step 5: Locking volume...
echo   ✅ Volume locked
echo   Step 6: Dismounting volume...
echo   ✅ Volume dismounted
echo   Step 7: Ejecting media...
echo   ✅ Media ejected
echo   Step 8: Removing mount point...
echo   ✅ Mount point removed
echo   ✅ Unmount operation completed successfully
echo.

pause
