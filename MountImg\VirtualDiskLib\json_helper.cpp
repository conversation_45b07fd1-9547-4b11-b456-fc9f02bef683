﻿/*
 * json_helper.cpp
 * 轻量级JSON处理实现
 */

#define _CRT_SECURE_NO_WARNINGS
#include <windows.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdarg.h>
#include <winternl.h>

#include "json_helper.h"

/*
 * 跳过空白字符
 */
static const char* SkipWhitespace(const char* str)
{
    while (*str && (*str == ' ' || *str == '\t' || *str == '\n' || *str == '\r')) {
        str++;
    }
    return str;
}

/*
 * 查找字符串结束位置
 */
static const char* FindStringEnd(const char* str)
{
    if (*str != '"') return NULL;

    const char* start = str;
    str++; // 跳过开始的引号

    while (*str) {
        if (*str == '"') {
            // 检查是否为转义的引号
            int backslashCount = 0;
            const char* check = str - 1;

            // 向前计算连续的反斜杠数量
            while (check >= start && *check == '\\') {
                backslashCount++;
                check--;
            }

            // 如果反斜杠数量为偶数（包括0），则这是真正的结束引号
            if (backslashCount % 2 == 0) {
                return str;
            }
        }
        str++;
    }
    return NULL;
}

/*
 * 提取字符串值（去除引号）
 */
static char* ExtractString(const char* start, const char* end)
{
    if (!start || !end || end <= start) return NULL;

    // 改进的边界检查逻辑
    const char* actualStart = start;
    const char* actualEnd = end;

    // 更仔细地跳过开始引号
    if (*actualStart == '"') {
        actualStart++;
    }

    // 更仔细地处理结束引号
    if (actualEnd > actualStart && *(actualEnd - 1) == '"') {
        actualEnd--;
    }

    // 重新计算长度
    int len = (int)(actualEnd - actualStart);
    if (len <= 0) return NULL;

    // 分配内存
    char* result = (char*)malloc(len + 1);
    if (!result) return NULL;

    // 使用更安全的字符复制方法
    int i;
    for (i = 0; i < len && actualStart + i < actualEnd; i++) {
        result[i] = actualStart[i];
    }
    result[i] = '\0';

    return result;
}

/*
 * 解析JSON字符串中的键值对
 */
JsonNode* ParseSimpleJson(const char* json)
{
    if (!json) return NULL;
    
    JsonNode* head = NULL;
    JsonNode* current = NULL;
    
    const char* ptr = SkipWhitespace(json);
    
    // 查找开始的大括号
    if (*ptr != '{') return NULL;
    ptr++;
    
    while (*ptr) {
        ptr = SkipWhitespace(ptr);
        
        // 检查结束大括号
        if (*ptr == '}') break;
        
        // 查找键名
        if (*ptr != '"') break;
        
        const char* keyStart = ptr;
        const char* keyEnd = FindStringEnd(ptr);
        if (!keyEnd) break;
        
        ptr = keyEnd + 1;
        ptr = SkipWhitespace(ptr);
        
        // 查找冒号
        if (*ptr != ':') break;
        ptr++;
        ptr = SkipWhitespace(ptr);
        
        // 查找值
        const char* valueStart = ptr;
        const char* valueEnd = NULL;
        
        if (*ptr == '"') {
            // 字符串值
            valueEnd = FindStringEnd(ptr);
            if (!valueEnd) break;
            ptr = valueEnd + 1;
        } else {
            // 数字或布尔值
            while (*ptr && *ptr != ',' && *ptr != '}' && *ptr != ' ' && *ptr != '\t' && *ptr != '\n' && *ptr != '\r') {
                ptr++;
            }
            valueEnd = ptr;
        }
        
        // 创建新节点
        JsonNode* node = (JsonNode*)malloc(sizeof(JsonNode));
        if (!node) break;
        
        node->key = ExtractString(keyStart, keyEnd);
        node->value = ExtractString(valueStart, valueEnd);
        node->next = NULL;
        
        if (!node->key || !node->value) {
            free(node->key);
            free(node->value);
            free(node);
            break;
        }
        
        // 添加到链表
        if (!head) {
            head = current = node;
        } else {
            current->next = node;
            current = node;
        }
        
        ptr = SkipWhitespace(ptr);
        
        // 查找逗号或结束大括号
        if (*ptr == ',') {
            ptr++;
        } else if (*ptr == '}') {
            break;
        }
    }
    
    return head;
}

/*
 * 释放JSON节点链表
 */
void FreeJsonNodes(JsonNode* nodes)
{
    while (nodes) {
        JsonNode* next = nodes->next;
        free(nodes->key);
        free(nodes->value);
        free(nodes);
        nodes = next;
    }
}

/*
 * 从JSON节点中获取字符串值
 */
const char* GetJsonString(JsonNode* nodes, const char* key, const char* defaultValue)
{
    if (!nodes || !key) return defaultValue;
    
    while (nodes) {
        if (nodes->key && strcmp(nodes->key, key) == 0) {
            return nodes->value ? nodes->value : defaultValue;
        }
        nodes = nodes->next;
    }
    
    return defaultValue;
}

/*
 * 从JSON节点中获取整数值
 */
int GetJsonInt(JsonNode* nodes, const char* key, int defaultValue)
{
    const char* str = GetJsonString(nodes, key, NULL);
    if (!str) return defaultValue;
    
    return atoi(str);
}

/*
 * 从JSON节点中获取布尔值
 */
int GetJsonBool(JsonNode* nodes, const char* key, int defaultValue)
{
    const char* str = GetJsonString(nodes, key, NULL);
    if (!str) return defaultValue;
    
    if (strcmp(str, "true") == 0 || strcmp(str, "1") == 0) {
        return 1;
    } else if (strcmp(str, "false") == 0 || strcmp(str, "0") == 0) {
        return 0;
    }
    
    return defaultValue;
}

/*
 * 解析挂载请求JSON
 */
int ParseMountRequest(const char* json, MountRequest* request)
{
    if (!json || !request) return -1;
    
    // 初始化请求结构
    memset(request, 0, sizeof(MountRequest));
    request->readonly = 0;
    request->partition = 1;
    request->auto_assign = 0;
    request->force = 0;
    
    // 解析JSON
    JsonNode* nodes = ParseSimpleJson(json);
    if (!nodes) return -1;
    
    // 提取字段
    const char* file_path = GetJsonString(nodes, "file_path", "");
    const char* drive = GetJsonString(nodes, "drive", "");
    const char* mount_point = GetJsonString(nodes, "mount_point", "");
    
    strncpy(request->file_path, file_path, sizeof(request->file_path) - 1);
    strncpy(request->drive, drive, sizeof(request->drive) - 1);
    strncpy(request->mount_point, mount_point, sizeof(request->mount_point) - 1);
    
    request->readonly = GetJsonBool(nodes, "readonly", 0);
    request->partition = GetJsonInt(nodes, "partition", 1);
    request->auto_assign = GetJsonBool(nodes, "auto_assign", 0);
    request->force = GetJsonBool(nodes, "force", 0);
    
    FreeJsonNodes(nodes);
    return 0;
}

/*
 * 安全的JSON字符串格式化
 */
int SafeJsonPrintf(char* buffer, int bufferSize, const char* format, ...)
{
    if (!buffer || bufferSize <= 0 || !format) return -1;
    
    va_list args;
    va_start(args, format);
    
    int result = vsnprintf_s(buffer, bufferSize, _TRUNCATE, format, args);
    
    va_end(args);
    
    // 确保字符串以null结尾
    if (result > 0 && result < bufferSize) {
        buffer[result] = '\0';
        return 0;
    } else {
        // 缓冲区太小，返回错误JSON
        const char* error_json = "{\"success\":false,\"error_code\":1002,\"error_message\":\"Buffer too small\"}";
        int error_len = (int)strlen(error_json);
        if (error_len < bufferSize) {
            strcpy(buffer, error_json);
        } else if (bufferSize > 0) {
            buffer[0] = '\0';
        }
        return -1;
    }
}

/*
 * 转义JSON字符串中的特殊字符
 */
int EscapeJsonString(const char* input, char* output, int outputSize)
{
    if (!input || !output || outputSize <= 0) return -1;
    
    int inputLen = (int)strlen(input);
    int outputPos = 0;
    
    for (int i = 0; i < inputLen && outputPos < outputSize - 1; i++) {
        char c = input[i];
        
        switch (c) {
            case '"':
                if (outputPos < outputSize - 2) {
                    output[outputPos++] = '\\';
                    output[outputPos++] = '"';
                }
                break;
            case '\\':
                if (outputPos < outputSize - 2) {
                    output[outputPos++] = '\\';
                    output[outputPos++] = '\\';
                }
                break;
            case '\n':
                if (outputPos < outputSize - 2) {
                    output[outputPos++] = '\\';
                    output[outputPos++] = 'n';
                }
                break;
            case '\r':
                if (outputPos < outputSize - 2) {
                    output[outputPos++] = '\\';
                    output[outputPos++] = 'r';
                }
                break;
            case '\t':
                if (outputPos < outputSize - 2) {
                    output[outputPos++] = '\\';
                    output[outputPos++] = 't';
                }
                break;
            default:
                output[outputPos++] = c;
                break;
        }
    }
    
    output[outputPos] = '\0';
    return 0;
}

/*
 * 生成挂载响应JSON
 */
int GenerateMountResponse(const MountResponse* response, char* jsonOutput, int bufferSize)
{
    if (!response || !jsonOutput || bufferSize <= 0) return -1;

    char escaped_message[512];
    char escaped_error[512];
    char escaped_path[1024];
    char escaped_fs[64];
    char escaped_time[64];

    // 转义字符串
    EscapeJsonString(response->message, escaped_message, sizeof(escaped_message));
    EscapeJsonString(response->error_message, escaped_error, sizeof(escaped_error));
    EscapeJsonString(response->image_path, escaped_path, sizeof(escaped_path));
    EscapeJsonString(response->file_system, escaped_fs, sizeof(escaped_fs));
    EscapeJsonString(response->mount_time, escaped_time, sizeof(escaped_time));

    if (response->success) {
        // 成功响应
        return SafeJsonPrintf(jsonOutput, bufferSize,
            "{"
            "\"success\":true,"
            "\"error_code\":%d,"
            "\"drive_letter\":\"%s\","
            "\"file_system\":\"%s\","
            "\"image_path\":\"%s\","
            "\"size_bytes\":%lld,"
            "\"size_mb\":%d,"
            "\"readonly\":%s,"
            "\"is_mounted\":%s,"
            "\"mount_time\":\"%s\","
            "\"message\":\"%s\""
            "}",
            response->error_code,
            response->drive_letter,
            escaped_fs,
            escaped_path,
            response->size_bytes,
            response->size_mb,
            response->readonly ? "true" : "false",
            response->is_mounted ? "true" : "false",
            escaped_time,
            escaped_message);
    } else {
        // 错误响应
        return SafeJsonPrintf(jsonOutput, bufferSize,
            "{"
            "\"success\":false,"
            "\"error_code\":%d,"
            "\"error_message\":\"%s\","
            "\"drive_letter\":\"%s\""
            "}",
            response->error_code,
            escaped_error,
            response->drive_letter);
    }
}

/*
 * 生成错误响应JSON
 */
int GenerateErrorResponse(int errorCode, const char* errorMessage, char* jsonOutput, int bufferSize)
{
    if (!jsonOutput || bufferSize <= 0) return -1;

    char escaped_error[512];
    const char* safeMessage = errorMessage ? errorMessage : "Unknown error";

    EscapeJsonString(safeMessage, escaped_error, sizeof(escaped_error));

    return SafeJsonPrintf(jsonOutput, bufferSize,
        "{"
        "\"success\":false,"
        "\"error_code\":%d,"
        "\"error_message\":\"%s\""
        "}",
        errorCode,
        escaped_error);
}
