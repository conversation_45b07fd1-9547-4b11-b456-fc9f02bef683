# VirtualDiskLib 用户指南

## 概述

VirtualDiskLib 是一个强大的虚拟磁盘挂载库，支持多种虚拟磁盘格式的挂载和管理。本库基于 ImDisk 和 DiscUtils 技术，提供了企业级的虚拟磁盘挂载能力。

## 支持的文件格式

### 🟢 VHD (Virtual Hard Disk)
- **权限要求**: 普通用户权限
- **技术实现**: 直接通过 ImDisk 驱动挂载
- **兼容性**: Windows XP 及以上版本
- **推荐用途**: 日常使用，无需特殊权限

### 🟡 VHDX (Virtual Hard Disk v2)
- **权限要求**: 管理员权限
- **技术实现**: 通过 ImDiskImg 服务和 DiscUtils 挂载
- **兼容性**: Windows Vista 及以上版本
- **推荐用途**: 企业环境，需要管理员权限

### 🟡 VMDK (VMware Virtual Disk)
- **权限要求**: 管理员权限
- **技术实现**: 通过 ImDiskImg 服务和 DiscUtils 挂载
- **兼容性**: Windows XP 及以上版本
- **推荐用途**: VMware 环境，需要管理员权限

### 🟡 VDI (VirtualBox Disk Image)
- **权限要求**: 管理员权限
- **技术实现**: 通过 ImDiskImg 服务和 DiscUtils 挂载
- **兼容性**: Windows XP 及以上版本
- **推荐用途**: VirtualBox 环境，需要管理员权限

### 🟡 DMG (Apple Disk Image)
- **权限要求**: 管理员权限
- **技术实现**: 通过 ImDiskImg 服务和 DiscUtils 挂载
- **兼容性**: Windows XP 及以上版本
- **推荐用途**: 跨平台文件交换，需要管理员权限

## 权限要求详解

### 普通用户权限 (VHD 格式)
```
✅ 可以挂载: VHD 文件
❌ 无法挂载: VHDX, VMDK, VDI, DMG 文件
🔧 技术原理: 直接使用 ImDisk 驱动，无需系统服务
```

### 管理员权限 (所有格式)
```
✅ 可以挂载: 所有支持的格式 (VHD, VHDX, VMDK, VDI, DMG)
🔧 技术原理: 可以启动 ImDiskImg 系统服务，使用 DiscUtils 库
```

## 使用方法

### 1. 普通用户模式 (推荐用于 VHD 文件)
```cpp
#include "VirtualDiskLib.h"

// 挂载 VHD 文件 (无需管理员权限)
int result = MountVirtualDisk("C:\\path\\to\\file.vhd", "Z:", false, 1);
if (result == 0) {
    printf("VHD 文件挂载成功到 Z: 驱动器\n");
}
```

### 2. 管理员模式 (支持所有格式)
```cpp
// 以管理员身份运行程序后，可以挂载所有格式
int result = MountVirtualDisk("C:\\path\\to\\file.vhdx", "Y:", false, 1);
if (result == 0) {
    printf("VHDX 文件挂载成功到 Y: 驱动器\n");
}
```

## 错误处理和故障排除

### 常见错误码
- **0**: 挂载成功
- **1004**: 挂载失败，通常是权限不足或服务问题
- **1001**: 文件不存在
- **1002**: 驱动器号已被占用
- **1003**: 文件格式不支持

### 权限相关问题

#### 问题: VHDX/VMDK 挂载失败，错误码 1004
**原因**: 当前用户权限不足，无法启动 ImDiskImg 服务

**解决方案**:
1. 右键点击应用程序
2. 选择"以管理员身份运行"
3. 重新尝试挂载

#### 问题: 服务启动失败
**原因**: ImDiskImg 服务未正确安装或配置

**解决方案**:
1. 确认 ImDisk 驱动已正确安装
2. 检查 .NET Framework 4.0+ 是否已安装
3. 以管理员身份重新安装 ImDisk

### 系统要求检查

#### 必需组件
- **ImDisk 驱动**: 所有格式都需要
- **.NET Framework 4.0+**: VHDX/VMDK/VDI/DMG 格式需要
- **管理员权限**: VHDX/VMDK/VDI/DMG 格式需要

#### 兼容性
- **Windows XP**: 支持 VHD, VMDK, VDI, DMG (需要管理员权限)
- **Windows Vista+**: 支持所有格式
- **Windows 10/11**: 完全支持，推荐环境

## 最佳实践

### 1. 格式选择建议
- **日常使用**: 优先选择 VHD 格式，无需管理员权限
- **企业环境**: 可以使用 VHDX 格式，提供更好的性能和功能
- **跨平台**: 根据虚拟化平台选择对应格式 (VMDK for VMware, VDI for VirtualBox)

### 2. 权限管理
- **开发环境**: 建议以管理员身份运行，支持所有格式测试
- **生产环境**: 根据实际需求选择合适的权限级别
- **用户程序**: 提供权限检查和用户提示功能

### 3. 错误处理
```cpp
int result = MountVirtualDisk(imagePath, driveLetter, readonly, partition);
if (result != 0) {
    // 检查是否是权限问题
    if (RequiresAdminPrivileges(imagePath) && !IsRunAsAdministrator()) {
        printf("此文件格式需要管理员权限，请以管理员身份运行程序\n");
    } else {
        printf("挂载失败，错误码: %d\n", result);
    }
}
```

## API 参考

### 核心函数
- `MountVirtualDisk()`: 挂载虚拟磁盘
- `UnmountVirtualDisk()`: 卸载虚拟磁盘
- `IsRunAsAdministrator()`: 检查管理员权限
- `RequiresAdminPrivileges()`: 检查文件格式权限要求
- `CheckImDiskImgServiceStatus()`: 检查服务状态

### 权限检查函数
- `IsRunAsAdministrator()`: 返回当前进程是否以管理员身份运行
- `RequiresAdminPrivileges(filename)`: 返回指定文件是否需要管理员权限
- `GeneratePrivilegeWarning()`: 生成权限提示信息

## 技术架构

### 双重挂载策略
1. **第一层**: ImDisk 直接挂载 (VHD 格式)
2. **第二层**: DiscUtils + ImDiskImg 服务挂载 (VHDX/VMDK/VDI/DMG 格式)

### 服务管理
- **ImDiskImg 服务**: 负责复杂格式的挂载
- **自动降级**: 如果服务不可用，自动尝试直接挂载
- **权限检测**: 自动检测当前权限并提供相应提示

## 联系和支持

如有问题或建议，请参考：
- ImDisk 官方文档
- DiscUtils 项目文档
- Windows 虚拟磁盘管理指南

---
*VirtualDiskLib - 企业级虚拟磁盘挂载解决方案*
