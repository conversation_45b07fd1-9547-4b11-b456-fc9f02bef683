@echo off
echo 使用makecab创建CAB文件...

rem 清理旧文件
if exist files.cab del files.cab

echo 创建简单的CAB文件...
makecab files\config.exe files.cab

if exist files.cab (
    echo 成功创建files.cab
    dir files.cab
    
    echo 测试CAB文件...
    mkdir test_simple
    extrac32.exe /e /l test_simple files.cab
    
    if exist test_simple\config.exe (
        echo 测试成功: config.exe已解压
    ) else (
        echo 测试失败: config.exe未找到
    )
    
    rmdir /s /q test_simple 2>nul
) else (
    echo makecab执行失败
)

echo 完成
pause
