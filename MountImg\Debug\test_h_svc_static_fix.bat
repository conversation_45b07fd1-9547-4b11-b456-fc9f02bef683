@echo off
chcp 65001 >nul
echo ========================================
echo Testing h_svc Static Keyword Fix
echo ========================================

echo.
echo 这个测试验证 h_svc static 关键字问题的修复
echo.
echo 链接错误:
echo error LNK2001: unresolved external symbol _h_svc
echo fatal error LNK1120: 1 unresolved externals
echo.
echo 问题原因:
echo h_svc 在 MountImg.c 中被声明为 static SC_HANDLE h_svc
echo static 关键字限制了变量的作用域，只在当前文件内可见
echo VirtualDiskLib 无法访问 MountImg.c 中的 static 变量
echo 导致链接时找不到 h_svc 符号
echo.

echo 修复方案:
echo 将 static SC_HANDLE h_svc 改为 SC_HANDLE h_svc
echo 移除 static 关键字，使变量成为全局变量
echo 在 MountImg.h 中添加 extern SC_HANDLE h_svc 声明
echo 确保 VirtualDiskLib 可以正确访问 h_svc
echo.

echo 修复前后对比:
echo ❌ 修复前: static SC_HANDLE h_svc;  // 只在 MountImg.c 内可见
echo ✅ 修复后: SC_HANDLE h_svc;         // 全局可见，可被外部访问
echo ✅ 头文件: extern SC_HANDLE h_svc;  // 外部声明
echo.

echo 启动 VirtualDiskTool32.exe 测试链接修复...
echo ----------------------------------------

VirtualDiskTool32.exe --test-mount

echo.
echo ----------------------------------------
echo 程序执行完毕，退出码: %ERRORLEVEL%
echo.

if %ERRORLEVEL% EQU 0 (
    echo ✅ SUCCESS: h_svc static 关键字问题已修复，链接成功
) else (
    echo ❌ FAILED: 程序运行出错，退出码: %ERRORLEVEL%
)

echo.
echo 检查目标 X: 驱动器...
if exist X:\ (
    echo ✅ SUCCESS: X: 驱动器已挂载 (h_svc 链接修复成功)
    echo 目录列表:
    dir X: /w
) else (
    echo ❌ FAILED: X: 驱动器未挂载
)

echo.
echo ========================================
echo h_svc Static 关键字修复技术说明:
echo ========================================
echo.
echo ✅ 问题分析:
echo   static 关键字的作用域限制:
echo   - static 变量只在声明它的文件内可见
echo   - 其他文件无法访问 static 变量
echo   - 链接器无法解析外部对 static 变量的引用
echo.
echo   h_svc 的使用场景:
echo   - MountImg.c: 定义和初始化 h_svc
echo   - VirtualDiskLib.cpp: 需要访问 h_svc 进行调试输出
echo   - DiscUtils_Mount(): 需要使用 h_svc 启动服务
echo.
echo ✅ 修复实现:
echo   // MountImg.c 第48行 - 移除 static 关键字
echo   // 修复前:
echo   static SC_HANDLE h_svc;  // 文件内部可见
echo   
echo   // 修复后:
echo   SC_HANDLE h_svc;         // 全局可见
echo   
echo   // MountImg.h 第38行 - 添加外部声明
echo   extern SC_HANDLE h_svc;  // 允许外部文件访问
echo.
echo ✅ 变量作用域对比:
echo   static 变量:
echo   - 文件作用域 (file scope)
echo   - 内部链接 (internal linkage)
echo   - 只在当前编译单元内可见
echo   - 无法被其他文件访问
echo   
echo   全局变量:
echo   - 全局作用域 (global scope)
echo   - 外部链接 (external linkage)
echo   - 可被其他文件访问 (通过 extern 声明)
echo   - 链接器可以解析外部引用
echo.
echo ✅ 其他相关变量:
echo   保持 static 的变量 (内部使用):
echo   - static SERVICE_STATUS_HANDLE SvcStatusHandle;
echo   - static SERVICE_STATUS SvcStatus;
echo   - static HINSTANCE hinst;
echo   
echo   这些变量只在 MountImg.c 内部使用，不需要外部访问
echo.
echo ✅ 链接过程:
echo   编译阶段:
echo   - MountImg.c 编译为 MountImg.obj
echo   - VirtualDiskLib.cpp 编译为 VirtualDiskLib.obj
echo   
echo   链接阶段:
echo   - 链接器查找 VirtualDiskLib.obj 中引用的 h_svc 符号
echo   - 在 MountImg.obj 中找到 h_svc 的定义
echo   - 成功解析符号引用，生成 VirtualDiskLib32.dll
echo.
echo ✅ 调试验证:
echo   编译输出应该显示:
echo   - 无 LNK2001 错误
echo   - 无 LNK1120 错误
echo   - 成功生成 VirtualDiskLib32.dll
echo   
echo   运行时输出应该显示:
echo   - h_svc: VALID (如果服务初始化成功)
echo   - h_svc: NULL (如果服务初始化失败)
echo.

pause
