/*
 * 测试外部进程调用
 */

#include <windows.h>
#include <stdio.h>

int main()
{
    printf("Testing external process launch...\n");
    
    char exePath[MAX_PATH];
    char commandLine[2048];
    STARTUPINFOA si = {0};
    PROCESS_INFORMATION pi = {0};
    DWORD exitCode = 0;
    DWORD waitResult;
    
    // 构建MountImg32.exe路径
    sprintf_s(exePath, sizeof(exePath), ".\\MountImg32.exe");
    
    printf("MountImg32.exe path: %s\n", exePath);
    
    // 检查文件是否存在
    if (GetFileAttributesA(exePath) == INVALID_FILE_ATTRIBUTES) {
        printf("ERROR: MountImg32.exe not found\n");
        return 1;
    }
    
    // 构建命令行参数
    const char* jsonInput = "{\"file_path\":\"E:\\\\002_VHD\\\\vhd.vhd\",\"drive\":\"Z:\",\"readonly\":false,\"partition\":1}";
    sprintf_s(commandLine, sizeof(commandLine), "\"%s\" /JSON \"%s\"", exePath, jsonInput);
    
    printf("Command line: %s\n", commandLine);
    
    // 设置启动信息
    si.cb = sizeof(STARTUPINFOA);
    si.dwFlags = STARTF_USESHOWWINDOW;
    si.wShowWindow = SW_SHOW; // 显示窗口以便调试
    
    printf("Launching MountImg32.exe...\n");
    
    // 启动进程
    if (!CreateProcessA(NULL, commandLine, NULL, NULL, FALSE, 
                       0, NULL, NULL, &si, &pi)) {
        DWORD error = GetLastError();
        printf("ERROR: Failed to create process, error code: %lu\n", error);
        return 1;
    }
    
    printf("Process created successfully\n");
    printf("Process ID: %lu\n", pi.dwProcessId);
    
    // 关闭线程句柄
    CloseHandle(pi.hThread);
    
    // 等待进程完成，最多30秒
    printf("Waiting for process completion (timeout: 30 seconds)...\n");
    DWORD startTime = GetTickCount();
    
    waitResult = WaitForSingleObject(pi.hProcess, 30000); // 30秒超时
    
    DWORD elapsedTime = GetTickCount() - startTime;
    printf("Wait completed: result=%lu, elapsed=%lu ms\n", waitResult, elapsedTime);
    
    if (waitResult == WAIT_TIMEOUT) {
        printf("Process timeout (30 seconds)\n");
        TerminateProcess(pi.hProcess, 1);
        CloseHandle(pi.hProcess);
        return 2;
    } else if (waitResult != WAIT_OBJECT_0) {
        printf("Wait failed\n");
        CloseHandle(pi.hProcess);
        return 3;
    }
    
    // 获取进程退出码
    if (!GetExitCodeProcess(pi.hProcess, &exitCode)) {
        printf("Failed to get exit code\n");
        CloseHandle(pi.hProcess);
        return 4;
    }
    
    CloseHandle(pi.hProcess);
    
    printf("Process completed with exit code: %lu\n", exitCode);
    
    if (exitCode == 0) {
        printf("SUCCESS: Mount operation completed successfully\n");
    } else {
        printf("FAILED: Mount operation failed with code %lu\n", exitCode);
    }
    
    printf("\nPress any key to exit...\n");
    getchar();
    
    return 0;
}
