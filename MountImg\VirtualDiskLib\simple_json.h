/*
 * simple_json.h
 * 简化的JSON处理库 - 专为Windows XP和C++11设计
 * 
 * 功能：
 * - 基本的JSON解析和生成
 * - C++11兼容
 * - Windows XP支持
 * - 轻量级实现
 */

#ifndef SIMPLE_JSON_H
#define SIMPLE_JSON_H

#include <string>
#include <map>
#include <vector>
#include <sstream>
#include <memory>
#include <type_traits>  // for std::is_same

namespace SimpleJson {

// 前向声明
class Value;

// JSON值类 - 简化版本，兼容VS2013
class Value {
private:
    enum Type {
        TYPE_NULL,
        TYPE_BOOL,
        TYPE_NUMBER,
        TYPE_STRING,
        TYPE_ARRAY,
        TYPE_OBJECT
    };

    Type m_type;

    // 数据存储
    union {
        bool bool_value;
        double number_value;
    };
    std::string string_value;
    std::vector<Value> array_value;
    std::map<std::string, Value> object_value;

public:
    // 构造函数
    Value() : m_type(TYPE_NULL) {}
    Value(bool b) : m_type(TYPE_BOOL), bool_value(b) {}
    Value(int i) : m_type(TYPE_NUMBER), number_value(static_cast<double>(i)) {}
    Value(double d) : m_type(TYPE_NUMBER), number_value(d) {}
    Value(const std::string& s) : m_type(TYPE_STRING), string_value(s) {}
    Value(const char* s) : m_type(TYPE_STRING), string_value(s) {}
    
    // 数组构造
    static Value array() {
        Value v;
        v.m_type = TYPE_ARRAY;
        return v;
    }

    // 对象构造
    static Value object() {
        Value v;
        v.m_type = TYPE_OBJECT;
        return v;
    }
    
    // 类型检查
    bool is_null() const { return m_type == TYPE_NULL; }
    bool is_bool() const { return m_type == TYPE_BOOL; }
    bool is_number() const { return m_type == TYPE_NUMBER; }
    bool is_string() const { return m_type == TYPE_STRING; }
    bool is_array() const { return m_type == TYPE_ARRAY; }
    bool is_object() const { return m_type == TYPE_OBJECT; }
    
    // 值获取
    bool as_bool() const { return bool_value; }
    int as_int() const { return static_cast<int>(number_value); }
    double as_double() const { return number_value; }
    const std::string& as_string() const { return string_value; }
    
    // 数组操作
    void push_back(const Value& v) {
        if (m_type != TYPE_ARRAY) {
            m_type = TYPE_ARRAY;
            array_value.clear();
        }
        array_value.push_back(v);
    }

    size_t size() const {
        if (m_type == TYPE_ARRAY) return array_value.size();
        if (m_type == TYPE_OBJECT) return object_value.size();
        return 0;
    }

    Value& operator[](size_t index) {
        if (m_type != TYPE_ARRAY) {
            m_type = TYPE_ARRAY;
            array_value.clear();
        }
        if (index >= array_value.size()) {
            array_value.resize(index + 1);
        }
        return array_value[index];
    }
    
    // 对象操作
    Value& operator[](const std::string& key) {
        if (m_type != TYPE_OBJECT) {
            m_type = TYPE_OBJECT;
            object_value.clear();
        }
        return object_value[key];
    }

    bool contains(const std::string& key) const {
        return m_type == TYPE_OBJECT && object_value.find(key) != object_value.end();
    }
    
    // 带默认值的获取
    template<typename T>
    T value(const std::string& key, const T& default_value) const {
        if (m_type != TYPE_OBJECT) return default_value;
        auto it = object_value.find(key);
        if (it == object_value.end()) return default_value;
        
        // 类型转换
        if (std::is_same<T, bool>::value && it->second.is_bool()) {
            return static_cast<T>(it->second.as_bool());
        }
        if (std::is_same<T, int>::value && it->second.is_number()) {
            return static_cast<T>(it->second.as_int());
        }
        if (std::is_same<T, std::string>::value && it->second.is_string()) {
            return static_cast<T>(it->second.as_string());
        }
        
        return default_value;
    }
    
    // JSON字符串生成
    std::string dump() const {
        std::ostringstream oss;
        dump_to_stream(oss);
        return oss.str();
    }
    
private:
    void dump_to_stream(std::ostringstream& oss) const {
        switch (m_type) {
            case TYPE_NULL:
                oss << "null";
                break;
            case TYPE_BOOL:
                oss << (bool_value ? "true" : "false");
                break;
            case TYPE_NUMBER:
                oss << number_value;
                break;
            case TYPE_STRING:
                oss << "\"" << escape_string(string_value) << "\"";
                break;
            case TYPE_ARRAY:
                oss << "[";
                for (size_t i = 0; i < array_value.size(); ++i) {
                    if (i > 0) oss << ",";
                    array_value[i].dump_to_stream(oss);
                }
                oss << "]";
                break;
            case TYPE_OBJECT:
                oss << "{";
                bool first = true;
                for (const auto& pair : object_value) {
                    if (!first) oss << ",";
                    first = false;
                    oss << "\"" << escape_string(pair.first) << "\":";
                    pair.second.dump_to_stream(oss);
                }
                oss << "}";
                break;
        }
    }
    
    std::string escape_string(const std::string& str) const {
        std::string result;
        for (char c : str) {
            switch (c) {
                case '"': result += "\\\""; break;
                case '\\': result += "\\\\"; break;
                case '\b': result += "\\b"; break;
                case '\f': result += "\\f"; break;
                case '\n': result += "\\n"; break;
                case '\r': result += "\\r"; break;
                case '\t': result += "\\t"; break;
                default: result += c; break;
            }
        }
        return result;
    }
};

// 简化的JSON解析函数
Value parse(const std::string& json_str);

// 便利函数
inline Value create_success_response(const std::string& message) {
    Value response = Value::object();
    response["status"] = "success";
    response["message"] = message;
    return response;
}

inline Value create_error_response(const std::string& message) {
    Value response = Value::object();
    response["status"] = "error";
    response["message"] = message;
    return response;
}

inline Value create_cancelled_response(const std::string& message) {
    Value response = Value::object();
    response["status"] = "cancelled";
    response["message"] = message;
    return response;
}

} // namespace SimpleJson

// 类型别名，方便使用
using json = SimpleJson::Value;

#endif // SIMPLE_JSON_H
