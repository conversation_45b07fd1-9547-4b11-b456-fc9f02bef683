/*
 * test_init_mountdisk.cpp
 * Init_Mountdisk接口测试程序
 * 
 * 功能：演示如何使用新的一体化虚拟磁盘管理接口
 * 编译：使用Visual Studio 2013 (v120_xp工具集)
 */

#define _CRT_SECURE_NO_WARNINGS
#include <windows.h>
#include <iostream>
#include <string>
#include <cstdio>

// 引入VirtualDiskLib头文件
#include "VirtualDiskLib.h"

// 函数指针类型定义
typedef const char* (*Init_MountdiskFunc)(
    const char* params,
    ProgressCallback progressCallback,
    const char* taskId,
    QueryTaskControlCallback queryTaskControlCb
);

/*
 * 进度回调函数
 */
void TestProgressCallback(const std::string& taskId, int progress, const std::string& matchResult) {
    if (progress >= 0 && progress <= 100) {
        printf("[PROGRESS] Task: %s, Progress: %d%%\n", taskId.c_str(), progress);
    } else if (progress == -1) {
        printf("[MATCH] Task: %s, Result: %s\n", taskId.c_str(), matchResult.c_str());
    }
}

/*
 * 任务控制回调函数
 */
bool TestQueryTaskControlCallback(const std::string& taskId, int controlType) {
    // controlType: 0=取消查询, 1=暂停查询
    printf("[CONTROL] Task: %s, Control Type: %d\n", taskId.c_str(), controlType);
    
    // 这里可以根据需要返回true来取消或暂停任务
    // 为了测试，我们不取消任何任务
    return false;
}

/*
 * 格式化输出JSON结果
 */
void PrintJsonResult(const char* result, const char* operation_name) {
    printf("\n=== %s 结果 ===\n", operation_name);
    if (result) {
        printf("%s\n", result);
        
        // 简单的成功/失败判断
        if (strstr(result, "\"status\":\"success\"")) {
            printf("✅ 操作成功\n");
        } else if (strstr(result, "\"status\":\"error\"")) {
            printf("❌ 操作失败\n");
        } else if (strstr(result, "\"status\":\"cancelled\"")) {
            printf("⚠️  操作被取消\n");
        }
    } else {
        printf("❌ 返回结果为空\n");
    }
    printf("=====================================\n\n");
}

/*
 * 测试挂载操作
 */
void TestMountOperation(Init_MountdiskFunc Init_Mountdisk) {
    printf("🔧 测试挂载操作...\n");
    
    const char* mount_params = 
        "{"
        "\"operation\":\"mount\","
        "\"file_path\":\"C:\\\\test\\\\disk.vmdk\","
        "\"drive\":\"Z:\","
        "\"readonly\":false,"
        "\"partition\":1,"
        "\"auto_cleanup\":true,"
        "\"check_dependencies\":true"
        "}";
    
    const char* result = Init_Mountdisk(
        mount_params,
        TestProgressCallback,
        "mount_test_001",
        TestQueryTaskControlCallback
    );
    
    PrintJsonResult(result, "挂载操作");
}

/*
 * 测试卸载操作
 */
void TestUnmountOperation(Init_MountdiskFunc Init_Mountdisk) {
    printf("🗑️  测试卸载操作...\n");
    
    const char* unmount_params = 
        "{"
        "\"operation\":\"unmount\","
        "\"drive\":\"Z:\","
        "\"force_unmount\":false,"
        "\"auto_cleanup\":true"
        "}";
    
    const char* result = Init_Mountdisk(
        unmount_params,
        TestProgressCallback,
        "unmount_test_001",
        TestQueryTaskControlCallback
    );
    
    PrintJsonResult(result, "卸载操作");
}

/*
 * 测试状态查询操作
 */
void TestStatusOperation(Init_MountdiskFunc Init_Mountdisk) {
    printf("📋 测试状态查询操作...\n");
    
    const char* status_params = 
        "{"
        "\"operation\":\"status\","
        "\"drive\":\"Z:\","
        "\"auto_cleanup\":false"
        "}";
    
    const char* result = Init_Mountdisk(
        status_params,
        TestProgressCallback,
        "status_test_001",
        TestQueryTaskControlCallback
    );
    
    PrintJsonResult(result, "状态查询");
}

/*
 * 测试完整周期操作
 */
void TestFullCycleOperation(Init_MountdiskFunc Init_Mountdisk) {
    printf("🔄 测试完整周期操作（挂载->卸载）...\n");
    
    const char* full_cycle_params = 
        "{"
        "\"operation\":\"full_cycle\","
        "\"file_path\":\"C:\\\\test\\\\disk.vmdk\","
        "\"drive\":\"Z:\","
        "\"readonly\":false,"
        "\"partition\":1,"
        "\"force_unmount\":false,"
        "\"auto_cleanup\":true,"
        "\"check_dependencies\":true"
        "}";
    
    const char* result = Init_Mountdisk(
        full_cycle_params,
        TestProgressCallback,
        "full_cycle_test_001",
        TestQueryTaskControlCallback
    );
    
    PrintJsonResult(result, "完整周期操作");
}

/*
 * 主函数
 */
int main() {
    printf("===========================================\n");
    printf("    Init_Mountdisk 接口测试程序\n");
    printf("===========================================\n\n");

    // 加载DLL
    HMODULE hDll = LoadLibraryA("VirtualDiskLib32.dll");
    if (!hDll) {
        printf("❌ 无法加载 VirtualDiskLib32.dll\n");
        printf("错误代码: %lu\n", GetLastError());
        return 1;
    }

    printf("✅ 成功加载 VirtualDiskLib32.dll\n\n");

    // 获取函数指针
    Init_MountdiskFunc Init_Mountdisk = (Init_MountdiskFunc)GetProcAddress(hDll, "Init_Mountdisk");
    if (!Init_Mountdisk) {
        printf("❌ 无法获取 Init_Mountdisk 函数指针\n");
        printf("错误代码: %lu\n", GetLastError());
        FreeLibrary(hDll);
        return 1;
    }

    printf("✅ 成功获取 Init_Mountdisk 函数指针\n\n");

    // 执行各种测试
    TestMountOperation(Init_Mountdisk);
    TestUnmountOperation(Init_Mountdisk);
    TestStatusOperation(Init_Mountdisk);
    TestFullCycleOperation(Init_Mountdisk);

    // 清理
    FreeLibrary(hDll);
    
    printf("===========================================\n");
    printf("    测试完成\n");
    printf("===========================================\n");
    
    printf("\n按任意键退出...");
    getchar();
    
    return 0;
}
