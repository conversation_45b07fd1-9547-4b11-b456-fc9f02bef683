/*
 * VirtualDiskLib.cpp
 * VirtualDiskLib实现，兼容VS2013，符合006_Dll标准
 * 使用C风格JSON生成，避免C++11兼容性问题
 */

// Windows头文件
#define _WIN32_WINNT 0x0501
#define OEMRESOURCE
#define _CRT_SECURE_NO_WARNINGS

#include <windows.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <dbt.h>
#include <shlwapi.h>
#include <winternl.h>

// 项目头文件
#include "VirtualDiskLib.h"
#include "../MountImg.h"
#include "json_helper.h"

// C++标准库
#include <string>
#include <map>
#include <vector>
#include <mutex>
#include <atomic>
#include <memory>

// ========================================
// 全局变量和状态管理 (C++11现代化)
// ========================================

// 初始化状态
static std::atomic<bool> g_initialized(false);
static std::mutex g_init_mutex;

// 任务管理
static std::mutex g_task_mutex;
static std::map<std::string, bool> g_cancel_flags;
static std::map<std::string, bool> g_pause_flags;

// ========================================
// 辅助函数
// ========================================

// 简化的JSON响应生成函数
std::string create_success_response(const std::string& message) {
    char buffer[1024];
    sprintf_s(buffer, sizeof(buffer), 
        "{\"status\":\"success\",\"message\":\"%s\"}", 
        message.c_str());
    return std::string(buffer);
}

std::string create_error_response(const std::string& message) {
    char buffer[1024];
    sprintf_s(buffer, sizeof(buffer), 
        "{\"status\":\"error\",\"message\":\"%s\"}", 
        message.c_str());
    return std::string(buffer);
}

std::string create_cancelled_response(const std::string& message) {
    char buffer[1024];
    sprintf_s(buffer, sizeof(buffer), 
        "{\"status\":\"cancelled\",\"message\":\"%s\"}", 
        message.c_str());
    return std::string(buffer);
}

// 简化的JSON解析函数
std::string get_json_string_value(const std::string& json, const std::string& key, const std::string& default_value) {
    std::string search_key = "\"" + key + "\":\"";
    size_t pos = json.find(search_key);
    if (pos == std::string::npos) {
        return default_value;
    }
    
    pos += search_key.length();
    size_t end_pos = json.find("\"", pos);
    if (end_pos == std::string::npos) {
        return default_value;
    }
    
    return json.substr(pos, end_pos - pos);
}

bool get_json_bool_value(const std::string& json, const std::string& key, bool default_value) {
    std::string search_key = "\"" + key + "\":";
    size_t pos = json.find(search_key);
    if (pos == std::string::npos) {
        return default_value;
    }
    
    pos += search_key.length();
    if (json.substr(pos, 4) == "true") {
        return true;
    } else if (json.substr(pos, 5) == "false") {
        return false;
    }
    
    return default_value;
}

// 字符串转换函数
std::wstring utf8_to_wstring(const std::string& utf8) {
    if (utf8.empty()) return std::wstring();
    
    int size_needed = MultiByteToWideChar(CP_UTF8, 0, utf8.c_str(), -1, NULL, 0);
    if (size_needed <= 0) return std::wstring();
    
    std::wstring result(size_needed - 1, 0);
    MultiByteToWideChar(CP_UTF8, 0, utf8.c_str(), -1, &result[0], size_needed);
    return result;
}

std::string wstring_to_utf8(const std::wstring& wide) {
    if (wide.empty()) return std::string();
    
    int size_needed = WideCharToMultiByte(CP_UTF8, 0, wide.c_str(), -1, NULL, 0, NULL, NULL);
    if (size_needed <= 0) return std::string();
    
    std::string result(size_needed - 1, 0);
    WideCharToMultiByte(CP_UTF8, 0, wide.c_str(), -1, &result[0], size_needed, NULL, NULL);
    return result;
}

// 任务管理函数
void register_task(const std::string& taskId) {
    std::lock_guard<std::mutex> lock(g_task_mutex);
    g_cancel_flags[taskId] = false;
    g_pause_flags[taskId] = false;
}

void unregister_task(const std::string& taskId) {
    std::lock_guard<std::mutex> lock(g_task_mutex);
    g_cancel_flags.erase(taskId);
    g_pause_flags.erase(taskId);
}

bool check_task_control(const std::string& taskId, QueryTaskControlCallback queryTaskControlCb, ProgressCallback progressCallback) {
    if (!queryTaskControlCb) return false;
    
    // 检查取消
    if (queryTaskControlCb(taskId, 1)) { // 1 = cancel
        std::lock_guard<std::mutex> lock(g_task_mutex);
        g_cancel_flags[taskId] = true;
        return true;
    }
    
    // 检查暂停
    if (queryTaskControlCb(taskId, 2)) { // 2 = pause
        std::lock_guard<std::mutex> lock(g_task_mutex);
        g_pause_flags[taskId] = true;
        // 暂停处理逻辑可以在这里添加
    }
    
    return false;
}

void report_progress(const std::string& taskId, int progress, ProgressCallback progressCallback) {
    if (progressCallback) {
        progressCallback(taskId, progress, "");
    }
}

// ========================================
// 006_Dll标准接口实现
// ========================================

/*
 * 获取库版本信息 - 符合006_Dll标准
 */
std::string GetLibraryInfo(
    const std::string& params,
    ProgressCallback progressCallback,
    const std::string& taskId,
    QueryTaskControlCallback queryTaskControlCb)
{
    register_task(taskId);
    
    try {
        report_progress(taskId, 0, progressCallback);
        
        if (check_task_control(taskId, queryTaskControlCb, progressCallback)) {
            unregister_task(taskId);
            return create_cancelled_response("Task cancelled by user");
        }
        
        report_progress(taskId, 100, progressCallback);
        
        // 生成库信息响应
        char response[2048];
        sprintf_s(response, sizeof(response),
            "{"
            "\"status\":\"success\","
            "\"message\":\"Library info retrieved successfully\","
            "\"version\":\"2.0.0\","
            "\"build_date\":\"2025-01-16\","
            "\"supported_formats\":[\"VMDK\",\"VHDX\",\"VHD\",\"ISO\",\"IMG\"],"
            "\"min_windows_version\":\"Windows XP SP3\","
            "\"architecture\":\"x86\","
            "\"cpp_standard\":\"C++11\","
            "\"dll_standard\":\"006_Dll_v1.0\","
            "\"compiler_toolset\":\"v120_xp\""
            "}");
        
        unregister_task(taskId);
        return std::string(response);
        
    } catch (const std::exception& e) {
        unregister_task(taskId);
        return create_error_response(std::string("Exception: ") + e.what());
    } catch (...) {
        unregister_task(taskId);
        return create_error_response("Unknown exception occurred");
    }
}

/*
 * 初始化VirtualDiskLib - 符合006_Dll标准
 */
std::string InitializeVirtualDiskLib(
    const std::string& params,
    ProgressCallback progressCallback,
    const std::string& taskId,
    QueryTaskControlCallback queryTaskControlCb)
{
    register_task(taskId);
    
    try {
        report_progress(taskId, 0, progressCallback);
        
        if (check_task_control(taskId, queryTaskControlCb, progressCallback)) {
            unregister_task(taskId);
            return create_cancelled_response("Task cancelled by user");
        }
        
        // 提取参数
        bool check_dependencies = get_json_bool_value(params, "check_dependencies", true);
        bool enable_debug = get_json_bool_value(params, "enable_debug", false);
        
        report_progress(taskId, 20, progressCallback);
        
        // 执行初始化
        std::lock_guard<std::mutex> lock(g_init_mutex);
        
        if (!g_initialized.load()) {
            // 初始化MountImg.c
            int init_result = InitializeImDisk();
            if (init_result != 0 && check_dependencies) {
                unregister_task(taskId);
                char error_response[512];
                sprintf_s(error_response, sizeof(error_response),
                    "{\"status\":\"error\",\"message\":\"Failed to initialize ImDisk dependencies\",\"error_code\":%d}",
                    init_result);
                return std::string(error_response);
            }
            
            report_progress(taskId, 80, progressCallback);
            g_initialized = true;
        }
        
        report_progress(taskId, 100, progressCallback);
        
        // 生成响应
        char response[1024];
        sprintf_s(response, sizeof(response),
            "{"
            "\"status\":\"success\","
            "\"message\":\"VirtualDiskLib initialized successfully\","
            "\"dependencies_checked\":%s,"
            "\"debug_enabled\":%s,"
            "\"already_initialized\":%s"
            "}",
            check_dependencies ? "true" : "false",
            enable_debug ? "true" : "false",
            g_initialized.load() ? "true" : "false");
        
        unregister_task(taskId);
        return std::string(response);
        
    } catch (const std::exception& e) {
        unregister_task(taskId);
        return create_error_response(std::string("Exception: ") + e.what());
    } catch (...) {
        unregister_task(taskId);
        return create_error_response("Unknown exception occurred");
    }
}
