VirtualDiskTool执行了此代码以后，进程里面开启了好多VirtualDiskTool32.exe进程，
一直在增加VirtualDiskTool32.exe进程,
分析原因，给出详细解决方案

问题没有修改，
请再详细分析原因



管理员运行  VirtualDiskTool项目 VirtualDiskTool32.exe 
执行到到代码位置，
// 尝试启动服务
	if (!StartService(h_svc, 3, (void*)cmdline_ptr)) {

服务无法开启，请系统检查一下，详细介绍原因，不要修改代码

MountImg.c
953行
printf("%s\n", errorMsg);
代码位置，
打印信息乱码，
先分析原因，不限次u该代码

VirtualDiskLib项目是否架构要求

参照
E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\006_Dll要求\docs\
E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\006_Dll要求\example_progress_dll.cpp

根据分析，
修改VirtualDiskLib项目，VirtualDiskTool项目，其他项目不修改
程序必须支持XP系统，C++标准使用C++11，

已经完成，请知悉，

discutils

test_functions.cpp 运行到298行弹出此窗口

