@echo off
chcp 65001 >nul
echo ========================================
echo Testing ID_COMBO2 Current Item Update
echo ========================================

echo.
echo 这个测试验证 ID_COMBO2 当前项更新功能
echo.
echo ID_COMBO2 当前项更新流程:
echo 1. 枚举系统可用驱动器 (A-Z)
echo 2. 构建可用驱动器列表
echo 3. 查找目标驱动器在列表中的索引
echo 4. 模拟 CB_SETCURSEL 消息设置当前选中项
echo 5. 触发选择变更事件
echo.

echo 启动 MountImg.exe 测试 ID_COMBO2 当前项更新...
echo ----------------------------------------

echo 2 | MountImg.exe

echo.
echo ----------------------------------------
echo 程序执行完毕，退出码: %ERRORLEVEL%
echo.

echo 验证系统驱动器状态...
echo.
echo 已占用的驱动器:
for %%d in (A B C D E F G H I J K L M N O P Q R S T U V W X Y Z) do (
    if exist %%d:\ (
        echo   %%d: - 已占用
    )
)

echo.
echo 可用的驱动器:
for %%d in (A B C D E F G H I J K L M N O P Q R S T U V W X Y Z) do (
    if not exist %%d:\ (
        echo   %%d: - 可用
    )
)

echo.
echo 检查目标 X: 驱动器...
if exist X:\ (
    echo ✅ SUCCESS: X: 驱动器已挂载
    echo 目录列表:
    dir X: /w
) else (
    echo ❌ FAILED: X: 驱动器未挂载
)

echo.
echo ========================================
echo ID_COMBO2 当前项更新技术说明:
echo ========================================
echo.
echo 1. 可用驱动器枚举:
echo    - 遍历 A-Z 所有字母
echo    - 使用 GetDriveType() 检测占用状态
echo    - DRIVE_NO_ROOT_DIR = 可用
echo    - 构建可用驱动器数组
echo.
echo 2. 索引查找算法:
echo    - 在可用驱动器列表中查找目标驱动器
echo    - 返回在列表中的位置索引
echo    - 索引从 0 开始计数
echo.
echo 3. 下拉框操作模拟:
echo    - SendMessage(ID_COMBO2, CB_SETCURSEL, index, 0)
echo    - 设置当前选中项为指定索引
echo    - 触发选择变更事件
echo.
echo 4. 事件处理:
echo    - 模拟用户选择驱动器的操作
echo    - 更新界面显示状态
echo    - 同步全局变量
echo.
echo 5. 错误处理:
echo    - 驱动器不在可用列表中的处理
echo    - 强制设置文本模式
echo    - 警告信息提示
echo.
echo 示例输出:
echo   可用驱动器列表: E: F: G: X: Y: Z: (共 6 个)
echo   ✓ 找到驱动器 X: 在列表中的位置: 索引 3
echo   ✓ ID_COMBO2 当前选中项设置为: 索引 3 (X:)
echo   📡 模拟: SendMessage(ID_COMBO2, CB_SETCURSEL, 3, 0)
echo.

pause
