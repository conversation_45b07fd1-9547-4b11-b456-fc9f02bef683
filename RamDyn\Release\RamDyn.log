﻿C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\MSBuild\Microsoft\VC\v150\Platforms\Win32\PlatformToolsets\v141_xp\Toolset.targets(39,5): warning MSB8051: 面向 Windows XP 的支持已被弃用，将来的 Visual Studio 版本不再提供该支持。请访问 https://go.microsoft.com/fwlink/?linkid=2023588，获取详细信息。
  RamDyn.c
E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\RamDyn\RamDyn.c(329): warning C4133: “=”: 从“unsigned char *”到“long *”的类型不兼容
E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\RamDyn\RamDyn.c(335): warning C4133: “!=”: 从“unsigned char *”到“long *”的类型不兼容
E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\RamDyn\RamDyn.c(487): warning C4244: “=”: 从“LONGLONG”转换到“size_t”，可能丢失数据
E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\RamDyn\RamDyn.c(490): warning C4244: “=”: 从“__int64”转换到“int”，可能丢失数据
E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\RamDyn\RamDyn.c(599): warning C4018: “>”: 有符号/无符号不匹配
E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\RamDyn\RamDyn.c(600): warning C4244: “=”: 从“ULONGLONG”转换到“ssize_t”，可能丢失数据
E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\RamDyn\RamDyn.c(601): warning C4018: “>”: 有符号/无符号不匹配
E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\RamDyn\RamDyn.c(627): warning C4244: “=”: 从“__int64”转换到“ssize_t”，可能丢失数据
E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\RamDyn\RamDyn.c(637): warning C4018: “<”: 有符号/无符号不匹配
E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\RamDyn\RamDyn.c(649): warning C4244: “=”: 从“__int64”转换到“DWORD”，可能丢失数据
E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\RamDyn\RamDyn.c(724): warning C4244: “=”: 从“ULONGLONG”转换到“DWORD”，可能丢失数据
E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\RamDyn\RamDyn.c(731): warning C4244: “=”: 从“__int64”转换到“size_t”，可能丢失数据
E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\RamDyn\RamDyn.c(807): warning C4244: “函数”: 从“ULONGLONG”转换到“int”，可能丢失数据
E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\RamDyn\RamDyn.c(809): warning C4244: “函数”: 从“ULONGLONG”转换到“int”，可能丢失数据
E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\RamDyn\RamDyn.c(823): warning C4244: “函数”: 从“ULONGLONG”转换到“int”，可能丢失数据
E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\RamDyn\RamDyn.c(825): warning C4244: “函数”: 从“ULONGLONG”转换到“int”，可能丢失数据
E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\RamDyn\RamDyn.c(973): warning C4244: “=”: 从“ULONGLONG”转换到“size_t”，可能丢失数据
E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\RamDyn\RamDyn.c(1022): warning C4133: “函数”: 从“void (__stdcall *)(LPVOID)”到“LPTHREAD_START_ROUTINE”的类型不兼容
E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\RamDyn\RamDyn.c(905): warning C4101: “edx”: 未引用的局部变量
C:\Program Files (x86)\Windows Kits\10\Include\10.0.10240.0\ucrt\corecrt_wstdio.h(1062): warning C4717: “_vsnwprintf_l”: 如递归所有控件路径，函数将导致运行时堆栈溢出
C:\Program Files (x86)\Windows Kits\10\Include\10.0.10240.0\ucrt\stdio.h(1391): warning C4717: “_vsnprintf_l”: 如递归所有控件路径，函数将导致运行时堆栈溢出
C:\Program Files (x86)\Windows Kits\10\Include\10.0.10240.0\ucrt\stdio.h(1410): warning C4717: “_vsnprintf”: 如递归所有控件路径，函数将导致运行时堆栈溢出
C:\Program Files (x86)\Windows Kits\10\Include\10.0.10240.0\ucrt\corecrt_wstdio.h(1134): warning C4717: “_vsnwprintf”: 如递归所有控件路径，函数将导致运行时堆栈溢出
E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\RamDyn\RamDyn.c(241): warning C4717: “__stdio_common_vswprintf”: 如递归所有控件路径，函数将导致运行时堆栈溢出
E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\RamDyn\RamDyn.c(236): warning C4717: “__stdio_common_vsprintf”: 如递归所有控件路径，函数将导致运行时堆栈溢出
  RamDyn.vcxproj -> E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\Release\RamDyn32.exe
