/*
 * VirtualDiskWrapper.h
 * 虚拟磁盘库统一封装接口
 * 
 * 功能：将VirtualDiskLib的所有函数封装成统一的C++接口
 * 支持：进度回调、任务控制、异常处理
 * 兼容性：Windows XP及以上版本，C++14标准
 */

#ifndef VIRTUALDISKWRAPPER_H
#define VIRTUALDISKWRAPPER_H

#include <string>
#include <functional>
#include <memory>
#include <map>
#include <mutex>
#include <atomic>
#include "VirtualDiskLib.h"

#ifdef __cplusplus
extern "C" {
#endif

// ========================================
// 封装接口回调函数类型定义
// ========================================

/*
 * 简化的进度回调函数类型
 * 参数：
 *   taskId: 任务唯一标识符
 *   progress: 进度值 (0-100)
 */
typedef void (*SimpleProgressCallback)(const std::string& taskId, int progress);

/*
 * 简化的任务控制回调函数类型
 * 参数：
 *   taskId: 任务唯一标识符
 *   controlType: 控制类型 (1=取消, 2=暂停)
 * 返回值：
 *   true: 执行相应控制操作
 *   false: 继续执行
 */
typedef bool (*SimpleQueryTaskControlCallback)(const std::string& taskId, int controlType);

// ========================================
// VirtualDiskWrapper 统一封装类
// ========================================

class VirtualDiskWrapper {
private:
    // 内部状态管理
    std::atomic<bool> m_initialized{false};
    std::mutex m_operation_mutex;
    
    // 回调函数适配器
    static void ProgressCallbackAdapter(const std::string& taskId, int progress, const std::string& matchResult);
    static bool QueryTaskControlCallbackAdapter(const std::string& taskId, int controlType);
    
    // 静态回调存储（用于适配器）
    static thread_local SimpleProgressCallback s_current_progress_callback;
    static thread_local SimpleQueryTaskControlCallback s_current_control_callback;
    
    // 辅助函数
    std::string GenerateTaskId(const std::string& operation) const;
    void ValidateInitialization() const;

public:
    // ========================================
    // 构造函数和析构函数
    // ========================================
    
    VirtualDiskWrapper();
    ~VirtualDiskWrapper();
    
    // 禁用拷贝构造和赋值
    VirtualDiskWrapper(const VirtualDiskWrapper&) = delete;
    VirtualDiskWrapper& operator=(const VirtualDiskWrapper&) = delete;
    
    // ========================================
    // 核心接口函数
    // ========================================
    
    /*
     * 获取库版本信息
     */
    std::string GetLibraryInfo(
        const std::string& params = "{}",
        SimpleProgressCallback progressCallback = nullptr,
        const std::string& taskId = "",
        SimpleQueryTaskControlCallback queryTaskControlCb = nullptr
    );
    
    /*
     * 初始化虚拟磁盘库
     */
    std::string InitializeVirtualDiskLib(
        const std::string& params = R"({"check_dependencies": true, "enable_debug": false})",
        SimpleProgressCallback progressCallback = nullptr,
        const std::string& taskId = "",
        SimpleQueryTaskControlCallback queryTaskControlCb = nullptr
    );
    
    /*
     * 清理虚拟磁盘库
     */
    std::string CleanupVirtualDiskLib(
        const std::string& params = R"({"force_cleanup": false})",
        SimpleProgressCallback progressCallback = nullptr,
        const std::string& taskId = "",
        SimpleQueryTaskControlCallback queryTaskControlCb = nullptr
    );
    
    /*
     * 挂载虚拟磁盘 - 主要封装接口
     */
    std::string MountVirtualDisk(
        const std::string& params,
        SimpleProgressCallback progressCallback = nullptr,
        const std::string& taskId = "",
        SimpleQueryTaskControlCallback queryTaskControlCb = nullptr
    );
    
    /*
     * 卸载虚拟磁盘
     */
    std::string UnmountVirtualDisk(
        const std::string& params,
        SimpleProgressCallback progressCallback = nullptr,
        const std::string& taskId = "",
        SimpleQueryTaskControlCallback queryTaskControlCb = nullptr
    );
    
    /*
     * 获取挂载状态
     */
    std::string GetMountStatus(
        const std::string& params = R"({"drive": ""})",
        SimpleProgressCallback progressCallback = nullptr,
        const std::string& taskId = "",
        SimpleQueryTaskControlCallback queryTaskControlCb = nullptr
    );
    
    // ========================================
    // 便捷接口函数
    // ========================================
    
    /*
     * 简化的挂载接口
     */
    std::string MountDisk(
        const std::string& imagePath,
        const std::string& driveLetter,
        bool readonly = false,
        int partition = 1,
        SimpleProgressCallback progressCallback = nullptr
    );
    
    /*
     * 简化的卸载接口
     */
    std::string UnmountDisk(
        const std::string& driveLetter,
        bool force = false,
        SimpleProgressCallback progressCallback = nullptr
    );
    
    // ========================================
    // 状态查询函数
    // ========================================
    
    /*
     * 检查库是否已初始化
     */
    bool IsInitialized() const { return m_initialized.load(); }
    
    /*
     * 获取库版本号
     */
    std::string GetVersion();
    
    /*
     * 检查驱动器是否已挂载
     */
    bool IsDriveMounted(const std::string& driveLetter);
};

#ifdef __cplusplus
}
#endif

#endif // VIRTUALDISKWRAPPER_H
