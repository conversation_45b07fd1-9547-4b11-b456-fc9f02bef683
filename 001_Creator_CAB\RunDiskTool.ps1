# PowerShell版磁盘挂载工具启动器 (Windows 7+)
# 编码: UTF-8

Write-Host "磁盘挂载工具启动器" -ForegroundColor Green
Write-Host "===================" -ForegroundColor Green

# 获取脚本所在目录
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$ZipFile = Join-Path $ScriptDir "files.zip"
$TempDir = Join-Path $env:TEMP "DiskUp"

try {
    # 检查ZIP文件是否存在
    if (-not (Test-Path $ZipFile)) {
        Write-Host "错误: 找不到files.zip文件" -ForegroundColor Red
        Write-Host "路径: $ZipFile" -ForegroundColor Yellow
        Read-Host "按回车键退出"
        exit 1
    }

    Write-Host "找到ZIP文件: $ZipFile" -ForegroundColor Green

    # 清理旧的临时目录
    if (Test-Path $TempDir) {
        Write-Host "清理旧的临时文件..." -ForegroundColor Yellow
        Remove-Item $TempDir -Recurse -Force -ErrorAction SilentlyContinue
    }

    # 创建临时目录
    Write-Host "创建临时目录: $TempDir" -ForegroundColor Green
    New-Item -ItemType Directory -Path $TempDir -Force | Out-Null

    # 解压ZIP文件
    Write-Host "正在解压文件..." -ForegroundColor Yellow
    
    # 使用.NET Framework的ZIP解压功能
    Add-Type -AssemblyName System.IO.Compression.FileSystem
    [System.IO.Compression.ZipFile]::ExtractToDirectory($ZipFile, $TempDir)
    
    Write-Host "解压完成" -ForegroundColor Green

    # 查找config.exe
    $ConfigExe = $null
    $ConfigPath = Join-Path $TempDir "config.exe"
    
    if (Test-Path $ConfigPath) {
        $ConfigExe = $ConfigPath
    } else {
        # 在子目录中查找
        $ConfigExe = Get-ChildItem -Path $TempDir -Name "config.exe" -Recurse | Select-Object -First 1
        if ($ConfigExe) {
            $ConfigExe = Join-Path $TempDir $ConfigExe
        }
    }

    if (-not $ConfigExe -or -not (Test-Path $ConfigExe)) {
        Write-Host "错误: 找不到config.exe" -ForegroundColor Red
        Write-Host "解压目录内容:" -ForegroundColor Yellow
        Get-ChildItem $TempDir | Format-Table Name, Length, LastWriteTime
        Read-Host "按回车键退出"
        exit 1
    }

    Write-Host "找到config.exe: $ConfigExe" -ForegroundColor Green

    # 运行config.exe
    Write-Host "正在启动配置程序..." -ForegroundColor Yellow
    Write-Host "执行: $ConfigExe" -ForegroundColor Cyan

    # 传递命令行参数
    $Arguments = $args -join " "
    if ($Arguments) {
        Write-Host "参数: $Arguments" -ForegroundColor Cyan
    }

    # 切换到config.exe所在目录并执行
    $ConfigDir = Split-Path -Parent $ConfigExe
    Push-Location $ConfigDir
    
    try {
        if ($Arguments) {
            $Process = Start-Process -FilePath $ConfigExe -ArgumentList $Arguments -Wait -PassThru
        } else {
            $Process = Start-Process -FilePath $ConfigExe -Wait -PassThru
        }
        
        $ExitCode = $Process.ExitCode
        Write-Host "配置程序执行完成，退出代码: $ExitCode" -ForegroundColor Green
    }
    finally {
        Pop-Location
    }

    # 询问是否清理临时文件
    Write-Host ""
    $Cleanup = Read-Host "是否清理临时文件? (Y/N)"
    if ($Cleanup -match "^[Yy]") {
        Write-Host "正在清理临时文件..." -ForegroundColor Yellow
        Remove-Item $TempDir -Recurse -Force -ErrorAction SilentlyContinue
        if (-not (Test-Path $TempDir)) {
            Write-Host "临时文件清理完成" -ForegroundColor Green
        } else {
            Write-Host "警告: 部分临时文件可能未能清理" -ForegroundColor Yellow
        }
    } else {
        Write-Host "临时文件保留在: $TempDir" -ForegroundColor Yellow
    }

    Write-Host ""
    Write-Host "脚本执行完成" -ForegroundColor Green
    exit $ExitCode

} catch {
    Write-Host "发生错误: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "错误详情: $($_.Exception.ToString())" -ForegroundColor Red
    Read-Host "按回车键退出"
    exit 1
}
