@echo off
rem 使用extrac32.exe解压的脚本
rem 完全模仿原始脚本结构

if not "%1"=="7" start /min cmd /c ""%~0" 7 %*" & exit /b
set F=%TEMP%\DiskUp%TIME::=%

rem 临时复制ZIP文件为CAB格式（extrac32.exe可以处理ZIP格式的CAB文件）
copy "%~dp0files.zip" "%TEMP%\temp_%RANDOM%.cab" >nul
set TEMP_CAB=%TEMP%\temp_%RANDOM%.cab
for %%i in ("%TEMP%\temp_*.cab") do set TEMP_CAB=%%i

extrac32.exe /e /l "%F%" "%TEMP_CAB%"
del "%TEMP_CAB%" 2>nul

if exist "%F%\config.exe" ("%F%\config.exe" %2 %3 %4) else for /r "%F%" %%i in (config.exe) do if exist "%%i" "%%i" %2 %3 %4
rd /s /q "%F%"
