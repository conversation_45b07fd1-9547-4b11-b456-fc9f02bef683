@echo off
chcp 65001 >nul
echo ========================================
echo Testing net_installed Initialization Fix
echo ========================================

echo.
echo 这个测试验证 net_installed 变量正确初始化的修复
echo.
echo 问题描述:
echo net_installed 变量控制挂载策略的选择
echo 原本在 wWinMain 中初始化，检查 .NET Framework 是否安装
echo VirtualDiskLib 直接调用挂载函数时，初始化可能未执行
echo 导致挂载策略判断错误
echo.

echo 修复方案:
echo 1. 在 InitializeImDisk() 中添加 net_installed 初始化
echo 2. 检查 .NET Framework v4.0.30319 注册表项
echo 3. 在 MountImg.h 中添加外部声明
echo 4. 添加详细的调试信息输出
echo.

echo 挂载策略说明:
echo ✅ ImDisk 挂载:
echo   - 参数: no_check_fs = new_file || !net_installed
echo   - 如果 .NET 未安装，跳过文件系统检查
echo   - 如果是新文件，跳过文件系统检查
echo.
echo ✅ DiscUtils 挂载 (备选):
echo   - 条件: error && !new_file && net_installed
echo   - 只有在 ImDisk 失败且 .NET 已安装时才尝试
echo   - 不适用于新文件创建
echo.

echo 检查当前系统 .NET Framework 状态...
echo ----------------------------------------

reg query "HKLM\SOFTWARE\Microsoft\.NETFramework\v4.0.30319" >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo ✅ .NET Framework v4.0.30319 已安装
    echo   预期: net_installed = TRUE
    echo   挂载策略: 优先 ImDisk，失败时尝试 DiscUtils
) else (
    echo ❌ .NET Framework v4.0.30319 未安装
    echo   预期: net_installed = FALSE
    echo   挂载策略: 仅使用 ImDisk，跳过文件系统检查
)

echo.
echo 启动 VirtualDiskTool32.exe 测试 net_installed 初始化...
echo ----------------------------------------

VirtualDiskTool32.exe --test-mount

echo.
echo ----------------------------------------
echo 程序执行完毕，退出码: %ERRORLEVEL%
echo.

if %ERRORLEVEL% EQU 0 (
    echo ✅ SUCCESS: net_installed 初始化修复成功
) else (
    echo ❌ FAILED: 程序运行出错，退出码: %ERRORLEVEL%
)

echo.
echo 检查目标 X: 驱动器...
if exist X:\ (
    echo ✅ SUCCESS: X: 驱动器已挂载 (net_installed 修复成功)
    echo 目录列表:
    dir X: /w
) else (
    echo ❌ FAILED: X: 驱动器未挂载
)

echo.
echo ========================================
echo net_installed 初始化修复技术说明:
echo ========================================
echo.
echo ✅ 问题分析:
echo   net_installed 控制 ImDisk 和 DiscUtils 的挂载策略
echo   原始初始化在 wWinMain 中: 检查 .NET Framework 注册表
echo   VirtualDiskLib 直接调用时可能跳过这个初始化
echo   导致 net_installed 保持默认值 FALSE
echo.
echo ✅ 修复实现:
echo   // InitializeImDisk() 中添加 .NET 检查
echo   HKEY h_key;
echo   if (RegOpenKeyExA(HKEY_LOCAL_MACHINE, 
echo                     "SOFTWARE\\Microsoft\\.NETFramework\\v4.0.30319", 
echo                     0, KEY_QUERY_VALUE | KEY_WOW64_64KEY, &h_key) == ERROR_SUCCESS) {
echo       RegCloseKey(h_key);
echo       net_installed = TRUE;
echo   } else {
echo       net_installed = FALSE;
echo   }
echo.
echo ✅ 外部声明 (MountImg.h):
echo   extern BYTE net_installed;
echo   extern BYTE init_ok;
echo.
echo ✅ 调试信息输出:
echo   net_installed: TRUE/FALSE, init_ok: TRUE/FALSE
echo   ImDisk mount parameters: no_check_fs=TRUE/FALSE
echo   DiscUtils conditions: error=X, new_file=TRUE/FALSE, net_installed=TRUE/FALSE
echo.
echo ✅ 挂载策略逻辑:
echo   1. ImDisk 挂载:
echo      no_check_fs = new_file || !net_installed
echo      - 新文件或 .NET 未安装时跳过文件系统检查
echo   
echo   2. DiscUtils 挂载 (备选):
echo      if (error && !new_file && net_installed)
echo      - ImDisk 失败 && 非新文件 && .NET 已安装
echo.
echo ✅ 优势:
echo   - 正确的挂载策略选择
echo   - 充分利用 DiscUtils 功能 (当 .NET 可用时)
echo   - 兼容性保证 (当 .NET 不可用时)
echo   - 详细的调试信息便于问题定位
echo.

pause
