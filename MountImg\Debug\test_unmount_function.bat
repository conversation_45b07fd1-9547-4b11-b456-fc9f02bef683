@echo off
chcp 65001 >nul
echo ========================================
echo Testing UnmountVirtualDisk Function
echo ========================================

echo.
echo 这个测试验证基于 ImDisk-Dlg.c RM 逻辑的 UnmountVirtualDisk 函数
echo.
echo 实现特点:
echo 1. 参考 ImDisk-Dlg.c 第446行的 RM 处理逻辑
echo 2. 使用 CreateProcessW 启动 ImDisk-Dlg.exe RM 命令
echo 3. 等待进程完成并获取退出码
echo 4. 30秒超时保护
echo 5. 详细的调试信息输出
echo.

echo 测试 JSON 输入格式:
echo {
echo   "drive": "X:"
echo }
echo.

echo 启动 VirtualDiskTool32.exe 测试卸载功能...
echo ----------------------------------------

VirtualDiskTool32.exe --test-unmount

echo.
echo ----------------------------------------
echo 测试完毕，退出码: %ERRORLEVEL%
echo.

echo 检查 X: 驱动器状态...
if exist X:\ (
    echo ❌ FAILED: X: 驱动器仍然存在 (卸载失败)
    echo 目录列表:
    dir X: /w
) else (
    echo ✅ SUCCESS: X: 驱动器已卸载
)

echo.
echo ========================================
echo UnmountVirtualDisk 实现技术说明:
echo ========================================
echo.
echo 1. 参考代码来源 (ImDisk-Dlg.c):
echo    if (!wcscmp(argv[1], L"RM")) {
echo        // 创建状态对话框
echo        CreateDialog(hinst, L"STATUS_DLG", NULL, StatusProc);
echo        // 打开设备
echo        h = ImDiskOpenDeviceByMountPoint(mount_point, access_list[n_access]);
echo        // 查询设备信息
echo        DeviceIoControl(h, IOCTL_IMDISK_QUERY_DEVICE, ...);
echo        // 刷新缓冲区
echo        FlushFileBuffers(h);
echo        // 锁定卷
echo        DeviceIoControl(h, FSCTL_LOCK_VOLUME, ...);
echo        // 卸载卷
echo        DeviceIoControl(h, FSCTL_DISMOUNT_VOLUME, ...);
echo        // 弹出媒体
echo        DeviceIoControl(h, IOCTL_STORAGE_EJECT_MEDIA, ...);
echo        // 移除挂载点
echo        ImDiskRemoveMountPoint(mount_point);
echo    }
echo.
echo 2. 简化实现方式:
echo    // 构建命令行
echo    _snwprintf(cmdLine, _countof(cmdLine), L"ImDisk-Dlg.exe RM \"%%s\"", wDrive);
echo    
echo    // 创建进程
echo    CreateProcessW(NULL, cmdLine, NULL, NULL, FALSE, 0, NULL, NULL, &si, &pi);
echo    
echo    // 等待完成
echo    WaitForSingleObject(pi.hProcess, 30000);
echo    GetExitCodeProcess(pi.hProcess, &exitCode);
echo.
echo 3. 错误处理:
echo    - 进程创建失败: VDL_ERROR_UNMOUNT_FAILED
echo    - 进程超时: VDL_ERROR_UNMOUNT_FAILED  
echo    - 退出码非0: VDL_ERROR_UNMOUNT_FAILED
echo    - 成功: VDL_SUCCESS
echo.
echo 4. 调试信息:
echo    OutputDebugStringA("=== Starting Unmount Operation (ImDisk-Dlg RM) ===");
echo    sprintf(debug_info, "Unmounting drive: %%s", request.drive);
echo    sprintf(debug_info, "Executing: ImDisk-Dlg.exe RM \"%%S\"", wDrive);
echo    sprintf(debug_info, "ImDisk-Dlg.exe exit code: %%d", exitCode);
echo.
echo 5. JSON 响应格式:
echo    成功: {"success": 1, "error_code": 0, "drive_letter": "X:", "message": "Unmount successful"}
echo    失败: {"success": 0, "error_code": 错误码, "error_message": "错误描述"}
echo.

pause
