@echo off
chcp 65001 >nul
echo ========================================
echo Testing MountImg.exe JSON Parameter Processing
echo ========================================

echo.
echo Test 1: JSON Parameter Processing in wWinMain
echo Command: MountImg.exe /JSON "{\"file_path\":\"E:\\004_VMDK\\666666.vmdk\",\"drive\":\"X:\",\"readonly\":false,\"partition\":1}"
echo.

echo Running MountImg.exe with JSON parameters...
echo ----------------------------------------

MountImg.exe /JSON "{\"file_path\":\"E:\\004_VMDK\\666666.vmdk\",\"drive\":\"X:\",\"readonly\":false,\"partition\":1}"

echo.
echo ----------------------------------------
echo Exit code: %ERRORLEVEL%
echo.

echo Test 2: Checking mount result...
if exist X:\ (
    echo ✅ SUCCESS: X: drive is mounted
    echo Directory listing:
    dir X: /w
) else (
    echo ❌ FAILED: X: drive is not mounted
)

echo.
echo ========================================
echo JSON Processing Analysis:
echo ========================================
echo This test verifies:
echo 1. JSON parameter detection in wWinMain (line 1252)
echo 2. JSON string parsing for all fields:
echo    - file_path → filename global variable
echo    - drive → drive global variable  
echo    - readonly → readonly global variable
echo    - partition → partition global variable
echo 3. File extension detection and device type setting
echo 4. Automatic mount execution via cmdline_mount flag
echo.
echo Key features:
echo - Direct integration in MountImg.c wWinMain function
echo - Console output for debugging JSON parsing
echo - Automatic device type detection (.iso/.vmdk/.vhd)
echo - Seamless integration with existing mount logic
echo.

pause
