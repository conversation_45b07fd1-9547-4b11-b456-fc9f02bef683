@echo off
chcp 65001 >nul
echo ========================================
echo Testing Link Error Fix
echo ========================================

echo.
echo 这个测试验证修复链接错误后的 MountVirtualDisk 功能
echo.
echo 链接错误修复说明:
echo 错误: LNK2019: unresolved external symbol _Imdisk_Mount
echo 错误: LNK2019: unresolved external symbol _DiscUtils_Mount
echo 错误: LNK2019: unresolved external symbol _reg_save
echo 错误: LNK2001: unresolved external symbol _mount_mutex
echo.
echo 修复方案:
echo 原计划: 直接调用 MountImg.c 中的函数
echo 遇到问题: 链接错误，无法解析外部符号
echo 最终方案: 使用 MountImg32.exe 进程调用方式 (简化可靠)
echo.

echo 修复后的实现特点:
echo 1. 避免复杂的链接配置问题
echo 2. 使用本地变量存储参数，不依赖外部全局变量
echo 3. 通过 MountImg32.exe 进程调用实现挂载
echo 4. 保持功能完整性和可靠性
echo 5. 简单直接，易于维护
echo.

echo 实现流程:
echo Step 1: 解析 JSON 输入参数
echo Step 2: 设置本地变量 (g_filename, g_drive, g_readonly, g_partition)
echo Step 3: 构建 MountImg32.exe 命令行
echo Step 4: 启动 MountImg32.exe 进程 (后台模式)
echo Step 5: 等待进程完成并验证挂载结果
echo.

echo 启动 VirtualDiskTool32.exe 测试修复结果...
echo ----------------------------------------

VirtualDiskTool32.exe --test-mount

echo.
echo ----------------------------------------
echo 程序执行完毕，退出码: %ERRORLEVEL%
echo.

if %ERRORLEVEL% EQU 0 (
    echo ✅ SUCCESS: 链接错误已修复，程序正常运行
) else (
    echo ❌ FAILED: 程序运行出错，退出码: %ERRORLEVEL%
)

echo.
echo 检查目标 X: 驱动器...
if exist X:\ (
    echo ✅ SUCCESS: X: 驱动器已挂载 (修复后的实现正常工作)
    echo 目录列表:
    dir X: /w
) else (
    echo ❌ FAILED: X: 驱动器未挂载
)

echo.
echo ========================================
echo 修复后的实现技术说明:
echo ========================================
echo.
echo ✅ 本地变量存储 (避免外部依赖):
echo   static WCHAR g_filename[MAX_PATH];
echo   static WCHAR g_drive[MAX_PATH + 2];
echo   static BOOL g_readonly;
echo   static UINT g_partition;
echo.
echo ✅ 参数映射函数 (SetMountImgParameters):
echo   MultiByteToWideChar(CP_UTF8, 0, request->file_path, -1, g_filename, MAX_PATH);
echo   MultiByteToWideChar(CP_UTF8, 0, request->drive, -1, g_drive, MAX_PATH + 2);
echo   g_readonly = request->readonly;
echo   g_partition = request->partition;
echo.
echo ✅ 挂载执行函数 (ExecuteMountOperation):
echo   _snwprintf(cmdLine, _countof(cmdLine), 
echo              L"MountImg32.exe /JSON \"{...JSON参数...}\"");
echo   CreateProcessW(NULL, cmdLine, NULL, NULL, FALSE, 0, NULL, NULL, &si, &pi);
echo   WaitForSingleObject(pi.hProcess, 30000);
echo   GetExitCodeProcess(pi.hProcess, &exitCode);
echo.
echo ✅ 挂载验证函数 (VerifyMountResult):
echo   Sleep(1000); // 等待挂载完成
echo   if (GetVolumeInformation(temp_drive, NULL, 0, NULL, NULL, NULL, NULL, 0)) {
echo       return 0; // 挂载成功
echo   }
echo.
echo ✅ 优势:
echo   - 简单可靠: 避免复杂的链接配置
echo   - 无外部依赖: 不依赖 MountImg.c 全局变量
echo   - 功能完整: 重用现有的挂载逻辑
echo   - 易维护: 代码结构清晰
echo   - 兼容性好: 与现有系统完全兼容
echo.
echo ✅ 错误处理:
echo   - 进程创建失败: "Failed to start MountImg32.exe"
echo   - 进程超时: "MountImg32.exe timeout or failed"
echo   - 挂载验证失败: "Unrecognized volume" 或 "Mount verification timeout"
echo.

pause
