@echo off
echo 快速测试 VirtualDiskTool32.exe 进程问题
echo ========================================

echo 测试前进程数量:
tasklist /fi "imagename eq VirtualDiskTool32.exe" | find /c "VirtualDiskTool32.exe"

echo.
echo 启动 VirtualDiskTool32.exe...
start /wait VirtualDiskTool32.exe --test-mount

echo.
echo 测试后进程数量:
tasklist /fi "imagename eq VirtualDiskTool32.exe" | find /c "VirtualDiskTool32.exe"

echo.
echo 当前所有 VirtualDiskTool32.exe 进程:
tasklist /fi "imagename eq VirtualDiskTool32.exe"

pause
