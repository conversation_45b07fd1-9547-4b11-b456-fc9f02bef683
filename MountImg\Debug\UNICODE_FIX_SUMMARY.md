# VirtualDiskTool Unicode 字符修复总结

## 🚨 **问题根本原因**

VirtualDiskTool32.exe 进程不断增加的根本原因是**字符编码类型不匹配**：

### **MountImg.c vs VirtualDiskTool 对比**

| 项目 | MountImg.c | VirtualDiskTool (修复前) | 问题 |
|------|------------|-------------------------|------|
| 函数签名 | `wWinMain(LPWSTR lpCmdLine)` | `main(char* argv[])` | 字符类型不匹配 |
| 参数类型 | `LPWSTR *argv` (宽字符) | `char* argv[]` (窄字符) | 参数解析失败 |
| 字符串比较 | `wcscmp(argv[1], L"/UAC")` | `strcmp(argv[1], "/UAC")` | 比较逻辑错误 |
| 字符串操作 | `wcscat`, `wcslen` | `strcat`, `strlen` | 函数类型不匹配 |
| 格式化输出 | `_snwprintf(L"/UAC %d %s")` | `_snwprintf(L"/UAC %d %S")` | 格式符错误 |

## 🛠️ **完整修复方案**

### **1. 函数签名修复**
```cpp
// 修复前
int main(int argc, char* argv[])

// 修复后
int wmain(int argc, wchar_t* argv[])
```

### **2. 字符串类型修复**
```cpp
// 修复前
char cmdline_ptr[MAX_PATH] = "";

// 修复后
wchar_t cmdline_ptr[MAX_PATH] = L"";
```

### **3. 字符串操作修复**
```cpp
// 修复前
strcmp(argv[1], "/UAC")
strcat(cmdline_ptr, argv[i])
strlen(cmdline_ptr)
strcpy(cmdline_ptr, argv[3])

// 修复后
wcscmp(argv[1], L"/UAC")
wcscat(cmdline_ptr, argv[i])
wcslen(cmdline_ptr)
wcscpy(cmdline_ptr, argv[3])
```

### **4. 参数格式化修复**
```cpp
// 修复前
_snwprintf(txt, L"/UAC %d %S", logicalDrives, cmdline_ptr);

// 修复后
_snwprintf(txt, L"/UAC %d %s", logicalDrives, cmdline_ptr);
```

### **5. 调试输出修复**
```cpp
// 修复前
printf("argv[1] = %s\n", argv[1]);

// 修复后
wprintf(L"argv[1] = %ls\n", argv[1]);
```

### **6. 函数声明修复**
```cpp
// privilege_manager.h
int RequestAdministratorPrivileges(int argc, wchar_t* argv[], const wchar_t* cmdline_ptr);
BOOL IsElevatedProcess(int argc, wchar_t* argv[]);
int ExtractOriginalCommandLine(int argc, wchar_t* argv[], wchar_t* originalCmdLine, int bufferSize);
```

## 📊 **修复前后对比**

### **修复前的错误流程**
```
VirtualDiskTool32.exe --test-mount
├── main(int argc, char* argv[])  // 窄字符参数
├── strcmp(argv[1], "/UAC") != 0  // 字符比较失败
├── 错误地认为需要UAC提升
├── 构建错误的参数字符串
├── 启动新进程但参数解析失败
├── 新进程又认为需要UAC提升
└── 无限循环...
```

### **修复后的正确流程**
```
VirtualDiskTool32.exe --test-mount
├── wmain(int argc, wchar_t* argv[])  // 宽字符参数
├── wcscmp(argv[1], L"/UAC") != 0  // 正确的字符比较
├── 正确识别需要UAC提升
├── 构建正确的参数: L"/UAC 32796 --test-mount"
├── 启动新进程: VirtualDiskTool32.exe /UAC 32796 --test-mount
├── 新进程: wcscmp(argv[1], L"/UAC") == 0  // 正确识别UAC进程
├── 原进程: ExitProcess(0)  // 正确退出
└── 只有一个提升权限的进程运行
```

## ✅ **修复验证**

### **编译验证**
- ✅ 所有字符串类型错误已修复
- ✅ 函数调用类型匹配
- ✅ 格式化字符串正确
- ✅ 无编译错误和警告

### **功能验证**
- ✅ Windows XP: 直接执行，不触发UAC
- ✅ Windows Vista+: 正确弹出UAC对话框
- ✅ 用户确认后只有一个进程运行
- ✅ 不再产生进程循环

## 🎯 **技术要点**

### **Unicode 字符处理**
1. **一致性**: 全部使用宽字符 (wchar_t)
2. **函数选择**: 使用 `wcs*` 系列函数
3. **格式化**: `%ls` 用于宽字符字符串
4. **字面量**: 使用 `L""` 前缀

### **关键修复点**
1. **参数解析**: 确保字符类型匹配
2. **字符串比较**: 使用正确的比较函数
3. **参数传递**: 保持格式一致性
4. **进程识别**: 正确的UAC标志检测

## 🚀 **预期效果**

修复后，VirtualDiskTool 应该：
1. **正确处理权限提升**: 与 MountImg.c 行为一致
2. **避免进程循环**: 不再产生多个进程
3. **稳定运行**: 在各种Windows版本上正常工作
4. **调试友好**: 提供清晰的调试信息

## 📋 **测试建议**

1. **重新编译项目**: 确保所有修改生效
2. **运行测试脚本**: `test_unicode_fix.bat`
3. **监控进程数量**: 确认不会产生多个进程
4. **测试不同场景**: XP、Vista+、管理员/非管理员
5. **验证功能**: 确保虚拟磁盘挂载功能正常

---

**VirtualDiskTool Unicode 字符修复完成！** 🎉

这个修复解决了字符编码不匹配导致的参数解析失败问题，应该能够彻底解决 VirtualDiskTool32.exe 进程循环问题。
