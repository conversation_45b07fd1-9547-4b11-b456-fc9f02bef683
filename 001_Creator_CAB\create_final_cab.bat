@echo off
echo Creating final CAB file with complete structure...

rem Clean up
if exist files_final.cab del files_final.cab
if exist final.ddf del final.ddf

echo Creating DDF file with proper structure...

rem Create DDF header
echo .OPTION EXPLICIT > final.ddf
echo .Set CabinetNameTemplate=files_final.cab >> final.ddf
echo .Set DiskDirectoryTemplate=. >> final.ddf
echo .Set CompressionType=MSZIP >> final.ddf
echo .Set UniqueFiles=OFF >> final.ddf
echo .Set Cabinet=ON >> final.ddf
echo .Set Compress=ON >> final.ddf
echo .Set SourceDir=files >> final.ddf
echo. >> final.ddf

rem Add root files first
echo Adding root files...
echo "config.exe" >> final.ddf
echo "config32.exe" >> final.ddf
echo "config32_uninstall.exe" >> final.ddf
echo "devio.exe" >> final.ddf
echo "ImDisk-Dlg.exe" >> final.ddf
echo "ImDiskTk-svc.exe" >> final.ddf
echo "ImDiskTk-svc32.exe" >> final.ddf
echo "ImDiskTk-svc64.exe" >> final.ddf
echo "MountImg.exe" >> final.ddf
echo "MountImg32.exe" >> final.ddf
echo "RamDiskUI.exe" >> final.ddf
echo "RamDyn.exe" >> final.ddf
echo "RamDyn32.exe" >> final.ddf
echo "RamDyn64.exe" >> final.ddf
echo "DiscUtilsDevio.exe" >> final.ddf
echo "DevioNet.dll" >> final.ddf
echo "ImDiskNet.dll" >> final.ddf
echo "DiscUtils.Core.dll" >> final.ddf
echo "DiscUtils.Dmg.dll" >> final.ddf
echo "DiscUtils.Streams.dll" >> final.ddf
echo "DiscUtils.Vdi.dll" >> final.ddf
echo "DiscUtils.Vhd.dll" >> final.ddf
echo "DiscUtils.Vhdx.dll" >> final.ddf
echo "DiscUtils.Vmdk.dll" >> final.ddf
echo "DiscUtils.Xva.dll" >> final.ddf
echo "cp-admin.lnk" >> final.ddf
echo "cp.lnk" >> final.ddf
echo "lang.txt" >> final.ddf
echo "filelist.txt" >> final.ddf

rem Add driver files with directory structure
echo Adding driver files...
echo "driver\gpl.txt" >> final.ddf
echo "driver\imdisk.inf" >> final.ddf
echo "driver\install.cmd" >> final.ddf
echo "driver\msgboxw.exe" >> final.ddf
echo "driver\readme.txt" >> final.ddf
echo "driver\runwaitw.exe" >> final.ddf
echo "driver\uninstall_imdisk.cmd" >> final.ddf

rem Add awealloc files
echo "driver\awealloc\amd64\awealloc.cer" >> final.ddf
echo "driver\awealloc\amd64\awealloc.sys" >> final.ddf
echo "driver\awealloc\i386\awealloc.cer" >> final.ddf
echo "driver\awealloc\i386\awealloc.sys" >> final.ddf

rem Add CLI files
echo "driver\cli\amd64\imdisk.exe" >> final.ddf
echo "driver\cli\i386\imdisk.exe" >> final.ddf

rem Add CPL files
echo "driver\cpl\amd64\imdisk.cpl" >> final.ddf
echo "driver\cpl\amd64\imdisk.lib" >> final.ddf
echo "driver\cpl\i386\imdisk.cpl" >> final.ddf
echo "driver\cpl\i386\imdisk.lib" >> final.ddf

rem Add service files
echo "driver\svc\amd64\imdsksvc.exe" >> final.ddf
echo "driver\svc\i386\imdsksvc.exe" >> final.ddf

rem Add system files
echo "driver\sys\amd64\imdisk.cer" >> final.ddf
echo "driver\sys\amd64\imdisk.sys" >> final.ddf
echo "driver\sys\i386\imdisk.cer" >> final.ddf
echo "driver\sys\i386\imdisk.sys" >> final.ddf

rem Add language files
echo Adding language files...
echo "lang\brazilian-portuguese.txt" >> final.ddf
echo "lang\english.txt" >> final.ddf
echo "lang\french.txt" >> final.ddf
echo "lang\german.txt" >> final.ddf
echo "lang\italian.txt" >> final.ddf
echo "lang\russian.txt" >> final.ddf
echo "lang\schinese.txt" >> final.ddf
echo "lang\spanish.txt" >> final.ddf
echo "lang\swedish.txt" >> final.ddf

echo Running makecab...
makecab /F final.ddf

if exist files_final.cab (
    echo SUCCESS: files_final.cab created!
    dir files_final.cab
    
    echo Testing CAB file...
    mkdir test_final
    extrac32.exe /e /l test_final files_final.cab
    
    echo Checking structure...
    if exist test_final\config.exe (
        echo SUCCESS: config.exe found in root!
    ) else (
        echo ERROR: config.exe not found
    )
    
    if exist test_final\driver (
        echo SUCCESS: driver directory found!
    ) else (
        echo ERROR: driver directory missing
    )
    
    if exist test_final\lang (
        echo SUCCESS: lang directory found!
    ) else (
        echo ERROR: lang directory missing
    )
    
    echo Files in root:
    dir test_final\*.exe /b
    
    echo Directories:
    dir test_final /ad /b
    
    rmdir /s /q test_final
) else (
    echo ERROR: CAB file not created
)

rem Clean up
del final.ddf

echo Done!
pause
