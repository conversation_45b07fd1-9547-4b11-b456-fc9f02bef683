﻿/*
 * main.cpp
 * VirtualDiskTool - 虚拟磁盘挂载命令行工具
 * 
 * 功能：调用VirtualDiskLib.dll提供命令行接口
 */

#define _CRT_SECURE_NO_WARNINGS
#include <windows.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <string>

#include "VirtualDiskLib.h"
#include "cmdline_parser.h"
#include "json_builder.h"
#include "test_functions.h"
#include "privilege_manager.h"

// 版本信息
#define TOOL_VERSION "1.0.0"
#define TOOL_BUILD_DATE "2025-01-11"

/*
 * 显示帮助信息
 */
void ShowHelp(void)
{
    printf("VirtualDiskTool v%s - Virtual Disk Mount Tool\n", TOOL_VERSION);
    printf("Build Date: %s\n\n", TOOL_BUILD_DATE);
    
    printf("Usage:\n");
    printf("  VirtualDiskTool <command> [options]\n\n");
    
    printf("Commands:\n");
    printf("  mount     Mount a virtual disk image\n");
    printf("  unmount   Unmount a virtual disk\n");
    printf("  status    Get mount status of a drive\n");
    printf("  info      Get library information\n");
    printf("  test      Run comprehensive tests for VirtualDiskLib\n");
    printf("  help      Show this help message\n\n");
    
    printf("Mount Options:\n");
    printf("  --file <path>     Virtual disk image file path (required)\n");
    printf("  --drive <letter>  Target drive letter (e.g., Z:)\n");
    printf("  --readonly        Mount as read-only\n");
    printf("  --partition <n>   Partition number (default: 1)\n");
    printf("  --auto-assign     Auto assign available drive letter\n\n");
    
    printf("Unmount Options:\n");
    printf("  --drive <letter>  Drive letter to unmount (required)\n");
    printf("  --force           Force unmount\n\n");
    
    printf("Status Options:\n");
    printf("  --drive <letter>  Drive letter to query (required)\n\n");
    
    printf("JSON Mode:\n");
    printf("  --json <string>   Use JSON input format\n\n");

    printf("Test Options:\n");
    printf("  --test-all        Run all tests\n");
    printf("  --test-mount      Test mount function only\n");
    printf("  --test-unmount    Test unmount function only\n");
    printf("  --test-status     Test status function only\n");
    printf("  --test-error      Test error description function\n");
    printf("  --test-info       Test library info function\n");
    printf("  --test-callbacks  Test callback functionality (progress & control)\n");
    printf("  --test-file <path> Use specific test file (default: creates temp file)\n\n");
    
    printf("Examples:\n");
    printf("  VirtualDiskTool mount --file \"C:\\disk.vmdk\" --drive Z: --readonly\n");
    printf("  VirtualDiskTool unmount --drive Z:\n");
    printf("  VirtualDiskTool status --drive Z:\n");
    printf("  VirtualDiskTool test --test-all\n");
    printf("  VirtualDiskTool test --test-mount --test-file \"C:\\test.iso\"\n");
    printf("  VirtualDiskTool --json '{\"action\":\"mount\",\"file_path\":\"C:\\\\disk.vmdk\"}'\n\n");
    
    printf("Supported Formats: VMDK, VHDX, VHD, ISO, IMG\n");
    printf("Compatibility: Windows XP and above\n");
}

/*
 * 显示库信息
 */
int ShowLibraryInfo(void)
{
    // 使用新的006_Dll标准接口
    const char* input_json = "{}";
    const char* result = GetLibraryInfo(input_json, nullptr, "info_task", nullptr);
    std::string result_str = result ? result : "";

    if (result_str.find("\"status\":\"success\"") != std::string::npos) {
        printf("Library Information:\n");
        printf("%s\n", result);
        return 0;
    } else {
        printf("Error: Failed to get library information\n");
        printf("Response: %s\n", result);
        return 1;
    }
}

/*
 * 处理挂载命令
 */
int HandleMountCommand(const CommandLineArgs* args)
{
    if (!args->file_path || strlen(args->file_path) == 0) {
        printf("Error: File path is required for mount command\n");
        printf("Use: VirtualDiskTool mount --file <path> [options]\n");
        return 1;
    }
    
    // 构建JSON请求
    char request[1024];

    int result = BuildMountRequest(args, request, sizeof(request));
    if (result != 0) {
        printf("Error: Failed to build mount request\n");
        return 1;
    }

    // 调用DLL（使用新的006_Dll标准接口）
    const char* mount_result = MountVirtualDisk(request, nullptr, "mount_task", nullptr);
    std::string mount_result_str = mount_result ? mount_result : "";

    // 处理响应
    if (mount_result_str.find("\"status\":\"success\"") != std::string::npos) {
        if (args->json_mode) {
            printf("%s\n", mount_result);
        } else {
            printf("Mount operation completed:\n");
            PrintMountResponse(mount_result);
        }
        return 0;
    } else {
        if (args->json_mode) {
            printf("%s\n", mount_result);
        } else {
            printf("Mount failed:\n");
            PrintErrorResponse(mount_result);
        }
        return 1;
    }
}

/*
 * 处理卸载命令
 */
int HandleUnmountCommand(const CommandLineArgs* args)
{
    if (!args->drive_letter || strlen(args->drive_letter) == 0) {
        printf("Error: Drive letter is required for unmount command\n");
        printf("Use: VirtualDiskTool unmount --drive <letter>\n");
        return 1;
    }
    
    // 构建JSON请求
    char request[512];

    int result = BuildUnmountRequest(args, request, sizeof(request));
    if (result != 0) {
        printf("Error: Failed to build unmount request\n");
        return 1;
    }

    // 调用DLL（使用006_Dll标准接口）
    const char* unmount_result = UnmountVirtualDisk(request, nullptr, "unmount_task", nullptr);
    std::string unmount_result_str = unmount_result ? unmount_result : "";

    // 处理响应
    if (unmount_result_str.find("\"status\":\"success\"") != std::string::npos) {
        if (args->json_mode) {
            printf("%s\n", unmount_result);
        } else {
            printf("Unmount operation completed:\n");
            PrintUnmountResponse(unmount_result);
        }
        return 0;
    } else {
        if (args->json_mode) {
            printf("%s\n", unmount_result);
        } else {
            printf("Unmount failed:\n");
            PrintErrorResponse(unmount_result);
        }
        return 1;
    }
}

/*
 * 处理状态查询命令
 */
int HandleStatusCommand(const CommandLineArgs* args)
{
    if (!args->drive_letter || strlen(args->drive_letter) == 0) {
        printf("Error: Drive letter is required for status command\n");
        printf("Use: VirtualDiskTool status --drive <letter>\n");
        return 1;
    }
    
    // 构建JSON请求
    char request[256];

    int result = BuildStatusRequest(args, request, sizeof(request));
    if (result != 0) {
        printf("Error: Failed to build status request\n");
        return 1;
    }

    // 调用DLL（使用新的006_Dll标准接口）
    const char* status_result = GetMountStatus(request, nullptr, "status_task", nullptr);
    std::string status_result_str = status_result ? status_result : "";

    // 处理响应
    if (status_result_str.find("\"status\":\"success\"") != std::string::npos) {
        if (args->json_mode) {
            printf("%s\n", status_result);
        } else {
            printf("Mount status:\n");
            PrintStatusResponse(status_result);
        }
        return 0;
    } else {
        if (args->json_mode) {
            printf("%s\n", status_result);
        } else {
            printf("Status query failed:\n");
            PrintErrorResponse(status_result);
        }
        return 1;
    }
}

/*
 * 处理JSON模式
 */
int HandleJsonMode(const char* jsonInput)
{
    if (!jsonInput || strlen(jsonInput) == 0) {
        printf("Error: JSON input is empty\n");
        return 1;
    }
    
    const char* result_ptr = nullptr;
    std::string result_str;

    // 简单解析action字段来决定调用哪个函数（使用新的006_Dll标准接口）
    if (strstr(jsonInput, "\"action\":\"mount\"") || strstr(jsonInput, "\"file_path\"")) {
        result_ptr = MountVirtualDisk(jsonInput, nullptr, "json_mount_task", nullptr);
    } else if (strstr(jsonInput, "\"action\":\"unmount\"")) {
        result_ptr = UnmountVirtualDisk(jsonInput, nullptr, "json_unmount_task", nullptr);
    } else if (strstr(jsonInput, "\"action\":\"status\"")) {
        result_ptr = GetMountStatus(jsonInput, nullptr, "json_status_task", nullptr);
    } else {
        printf("Error: Unknown action in JSON input\n");
        return 1;
    }

    result_str = result_ptr ? result_ptr : "";

    // 输出响应
    printf("%s\n", result_str.c_str());

    return (result_str.find("\"status\":\"success\"") != std::string::npos) ? 0 : 1;
}

/*
 * 主函数
 */
int wmain(int argc, wchar_t* argv[])
{
    // === 权限检查和提升 (完全参照 MountImg.c 实现) ===
    printf("VirtualDiskTool v%s - Virtual Disk Mount Tool\n", TOOL_VERSION);
    printf("Build Date: %s\n", TOOL_BUILD_DATE);
    printf("Process ID: %d\n\n", GetCurrentProcessId());

    // 获取操作系统版本信息 (参照 MountImg.c)
    OSVERSIONINFO os_ver;
    if (!GetOSVersion(&os_ver)) {
        printf("WARNING: Failed to get OS version\n");
        os_ver.dwMajorVersion = 5; // 假设为 XP
    }

    printf("OS Version: %d.%d\n", os_ver.dwMajorVersion, os_ver.dwMinorVersion);

    // 构建命令行参数字符串 (参照 MountImg.c)
    wchar_t cmdline_ptr[MAX_PATH] = L"";
    if (argc > 1)
    {
        // 将所有参数合并为一个字符串
        for (int i = 1; i < argc; i++) {
            if (i > 1) wcscat(cmdline_ptr, L" ");
            wcscat(cmdline_ptr, argv[i]);
        }
    }

    // 打印命令行参数调试信息
    printf("========================================\n");
    printf("命令行参数调试信息:\n");
    printf("========================================\n");
    printf("argc = %d\n", argc);

    if (argc >= 1) {
        wprintf(L"argv[0] = %ls\n", argv[0] ? argv[0] : L"(null)");
    }
    else {
        printf("argv[0] = (不存在)\n");
    }

    if (argc >= 2) {
        wprintf(L"argv[1] = %ls\n", argv[1] ? argv[1] : L"(null)");
    }
    else {
        printf("argv[1] = (不存在)\n");
    }

    if (argc >= 3) {
        wprintf(L"argv[2] = %ls\n", argv[2] ? argv[2] : L"(null)");
    }
    else {
        printf("argv[2] = (不存在)\n");
    }

    if (argc >= 4) {
        wprintf(L"argv[3] = %ls\n", argv[3] ? argv[3] : L"(null)");
    }
    else {
        printf("argv[3] = (不存在)\n");
    }

    if (argc >= 5) {
        wprintf(L"argv[4] = %ls\n", argv[4] ? argv[4] : L"(null)");
    }
    else {
        printf("argv[4] = (不存在)\n");
    }

    if (argc >= 6) {
        wprintf(L"argv[5] = %ls\n", argv[5] ? argv[5] : L"(null)");
    }
    else {
        printf("argv[5] = (不存在)\n");
    }

    printf("========================================\n\n");

    // 详细的参数调试信息
    printf("🔍 详细参数分析:\n");
    printf("   argc = %d\n", argc);
    for (int i = 0; i < argc; i++) {
        wprintf(L"   argv[%d] = \"%ls\"\n", i, argv[i]);
    }
    wprintf(L"   cmdline_ptr = \"%ls\"\n", cmdline_ptr);
    printf("   os_ver.dwMajorVersion = %d\n", os_ver.dwMajorVersion);
    printf("\n");

    // === 严格的管理员权限检查 ===
    printf("🔍 检查管理员权限...\n");

    // 检查是否有强制运行参数 (仅用于调试)
    BOOL forceRun = FALSE;
    if (argc > 1) {
        for (int i = 1; i < argc; i++) {
            if (wcscmp(argv[i], L"--force-run") == 0 || wcscmp(argv[i], L"/force") == 0) {
                forceRun = TRUE;
                printf("⚠️  检测到强制运行参数，跳过权限检查 (仅用于调试)\n");
                break;
            }
        }
    }

    BOOL isAdmin = IsRunningAsAdministrator();
    printf("   权限检查结果: %s\n", isAdmin ? "TRUE (管理员)" : "FALSE (非管理员)");

    if (isAdmin) {
        printf("✅ 当前以管理员权限运行\n");
        printf("   可以执行虚拟磁盘挂载操作\n");
    } else if (forceRun) {
        printf("⚠️  强制运行模式：跳过管理员权限检查\n");
        printf("   注意：某些功能可能无法正常工作\n");
        printf("   建议：仅用于调试和测试目的\n");
    } else {
        printf("❌ 当前未以管理员权限运行\n");
        printf("\n");
        printf("🚫 错误：VirtualDiskTool 需要管理员权限才能运行\n");
        printf("📋 原因：虚拟磁盘挂载操作需要以下系统级权限：\n");
        printf("   • 访问磁盘设备驱动程序\n");
        printf("   • 创建虚拟磁盘设备\n");
        printf("   • 修改系统磁盘配置\n");
        printf("   • 操作 ImDisk 内核驱动\n");
        printf("\n");
        printf("🛠️  解决方案：\n");
        printf("   方法1: 右键运行\n");
        printf("     1. 右键点击 VirtualDiskTool32.exe\n");
        printf("     2. 选择 \"以管理员身份运行\"\n");
        printf("\n");
        printf("   方法2: 命令行运行\n");
        printf("     1. 按 Win+R，输入 cmd\n");
        printf("     2. 按 Ctrl+Shift+Enter (以管理员身份打开)\n");
        printf("     3. 在管理员命令提示符中运行此程序\n");
        printf("\n");
        printf("   方法3: PowerShell 运行\n");
        printf("     1. 右键开始菜单，选择 \"Windows PowerShell (管理员)\"\n");
        printf("     2. 在 PowerShell 中运行此程序\n");
        printf("\n");
        printf("⚠️  程序将在 5 秒后自动退出...\n");

        // 倒计时退出
        for (int i = 5; i > 0; i--) {
            printf("\r   剩余时间: %d 秒 (按任意键立即退出)", i);
            Sleep(1000);
        }
        printf("\n\n");

        return 1; // 返回错误码
    }

    printf("\n");

    //// 权限检查和提升逻辑 (完全参照 MountImg.c)
    //if ((os_ver.dwMajorVersion >= 6)
    //    && (argc <= 1 || wcscmp(argv[1], L"/UAC") != 0)
    //    && !isAdmin
    //    )
    //{
    //    printf("🔍 Windows Vista+ detected, checking UAC requirements\n");
    //    wprintf(L"📋 Command line: %ls\n", cmdline_ptr);
    //    printf("🔍 Current argc: %d\n", argc);
    //    if (argc > 1) {
    //        wprintf(L"🔍 argv[1]: %ls\n", argv[1]);
    //    }
    //    printf("🚀 Requesting administrator privileges...\n\n");

    //    // 发送非提升的驱动器列表到提升的进程 (完全参照 MountImg.c)
    //    return RequestAdministratorPrivileges(argc, argv, cmdline_ptr);
    //}
    //else if (argc > 1 && wcscmp(argv[1], L"/UAC") == 0)
    {
        //printf("/UAC*****************************************************************************************************\n");

        //printf("✅ Running as elevated process (UAC parameter detected)\n");

        // 提取原始命令行参数 (参照 MountImg.c)
        if (argc >= 4)
        {
            wprintf(L"Logical drives from non-elevated process: %ls\n", argv[2]);
            wprintf(L"Original command line: %ls\n", argv[3]);

            // 重新构建命令行参数
            wcscpy(cmdline_ptr, argv[3]);
        }
        else if (argc >= 3)
        {
            wprintf(L"Logical drives from non-elevated process: %ls\n", argv[2]);
            printf("Original command line: (empty)\n");

            // 清空命令行参数
            cmdline_ptr[0] = L'\0';
        }
        //else
        //{
        //    printf("WARNING: UAC process but insufficient parameters\n");
        //    cmdline_ptr[0] = L'\0';
        //}
        printf("\n");
    }
   // else
   // {
   //     printf("ℹ️  Windows XP or earlier detected, UAC not required\n\n");
   // }








    //// 检查参数数量
    //if (argc < 2) {
    //    ShowHelp();
    //    return 1;
    //}
    
    //// 检查帮助命令
    //if (strcmp(argv[1], "help") == 0 || strcmp(argv[1], "--help") == 0 || strcmp(argv[1], "-h") == 0) {
    //    ShowHelp();
    //    return 0;
    //}
    
    //// 检查库信息命令
    //if (strcmp(argv[1], "info") == 0) {
    //    return ShowLibraryInfo();
    //}
    
    //// 解析命令行参数
    //CommandLineArgs args;
    //int parseResult = ParseCommandLine(argc, argv, &args);
    //
    //if (parseResult != 0) {
    //    printf("Error: Failed to parse command line arguments\n");
    //    printf("Use 'VirtualDiskTool help' for usage information\n");
    //    return 1;
    //}
    //
    //// 处理JSON模式
    //if (args.json_mode && args.json_input) {
    //    return HandleJsonMode(args.json_input);
    //}
    
    //// 处理命令
    //if (strcmp(args.command, "mount") == 0) {
    //    return HandleMountCommand(&args);
    //} else if (strcmp(args.command, "unmount") == 0) {
    //    return HandleUnmountCommand(&args);
    //} else if (strcmp(args.command, "status") == 0) {
    //    return HandleStatusCommand(&args);
    //} else if (strcmp(args.command, "test") == 0) {
    //    return HandleTestCommand(&args);
    //} else if (strcmp(args.command, "help") == 0) {
    //    ShowHelp();
    //    return 0;
    //} else if (argc == 1)
    {
        // 无参数时运行测试
        printf("No command specified, running tests...\n\n");

        //// 初始化VirtualDiskLib
        //printf("Initializing VirtualDiskLib...\n");
        //int initResult = InitializeVirtualDiskLib();
        //if (initResult != VDL_SUCCESS) {
        //    printf("ERROR: Failed to initialize VirtualDiskLib (code: %d)\n", initResult);
        //    return 1;
        //}
        //printf("VirtualDiskLib initialized successfully\n\n");

        for (int iIndex = 0; iIndex < 100; iIndex++)
        {
            printf("\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n********************************************************************************************************%d\n", iIndex + 1);

            // 测试MountVirtualDisk函数
            TestMountVirtualDisk();

            // 测试UnmountVirtualDisk函数
            TestUnmountVirtualDisk();

            // 等待 5 秒
            printf("      Waiting 5 seconds...\n");
            Sleep(5000);
        }

        //// 清理VirtualDiskLib
        //printf("\nCleaning up VirtualDiskLib...\n");
        //CleanupVirtualDiskLib();
        //printf("VirtualDiskLib cleanup completed\n");

        return 0;
    }
    /*else {
        printf("Error: Unknown command '%s'\n", args.command);
        printf("Use 'VirtualDiskTool help' for usage information\n");
        return 1;
    }*/

}

///*
// * 处理测试命令
// */
//int HandleTestCommand(const CommandLineArgs* args)
//{
//    printf("🧪 VirtualDiskLib Function Tests\n");
//    printf("================================\n\n");
//
//    int totalTests = 0;
//    int passedTests = 0;
//
//    // 检查测试选项
//    int testAll = args->test_all;
//    int testMount = testAll || args->test_mount;
//    int testUnmount = testAll || args->test_unmount;
//    int testStatus = testAll || args->test_status;
//    int testError = testAll || args->test_error;
//    int testInfo = testAll || args->test_info;
//    int testCallbacks = testAll || args->test_callbacks;
//
//    // 如果没有指定测试，默认运行所有测试
//    if (!testMount && !testUnmount && !testStatus && !testError && !testInfo && !testCallbacks) {
//        testAll = testMount = testUnmount = testStatus = testError = testInfo = testCallbacks = 1;
//    }
//
//    // 测试GetLibraryInfo函数
//    if (testInfo) {
//        printf("📋 Test 1: GetLibraryInfo Function\n");
//        totalTests++;
//        if (TestGetLibraryInfo()) {
//            printf("✅ PASSED: GetLibraryInfo function works correctly\n\n");
//            passedTests++;
//        } else {
//            printf("❌ FAILED: GetLibraryInfo function failed\n\n");
//        }
//    }
//
//    // 测试GetErrorDescription函数
//    if (testError) {
//        printf("📋 Test 2: GetErrorDescription Function\n");
//        totalTests++;
//        if (TestGetErrorDescription()) {
//            printf("✅ PASSED: GetErrorDescription function works correctly\n\n");
//            passedTests++;
//        } else {
//            printf("❌ FAILED: GetErrorDescription function failed\n\n");
//        }
//    }
//
//    // 测试MountVirtualDisk函数（使用明文字符串）
//    if (testMount) {
//        printf("📋 Test 3: MountVirtualDisk Function\n");
//        totalTests++;
//        if (TestMountVirtualDisk()) {
//            printf("✅ PASSED: MountVirtualDisk function works correctly\n\n");
//            passedTests++;
//        } else {
//            printf("❌ FAILED: MountVirtualDisk function failed\n\n");
//        }
//    }
//
//    // 测试GetMountStatus函数
//    if (testStatus) {
//        printf("📋 Test 4: GetMountStatus Function\n");
//        totalTests++;
//        if (TestGetMountStatus()) {
//            printf("✅ PASSED: GetMountStatus function works correctly\n\n");
//            passedTests++;
//        } else {
//            printf("❌ FAILED: GetMountStatus function failed\n\n");
//        }
//    }
//
//    // 测试UnmountVirtualDisk函数
//    if (testUnmount) {
//        printf("📋 Test 5: UnmountVirtualDisk Function\n");
//        totalTests++;
//        if (TestUnmountVirtualDisk()) {
//            printf("✅ PASSED: UnmountVirtualDisk function works correctly\n\n");
//            passedTests++;
//        } else {
//            printf("❌ FAILED: UnmountVirtualDisk function failed\n\n");
//        }
//    }
//
//    // 测试回调功能
//    if (testCallbacks) {
//        printf("📋 Test 6: Callback Functionality (Progress & Control)\n");
//        totalTests++;
//
//        // 确定测试文件
//        const char* testFile = (args->test_file[0] != '\0') ? args->test_file : "C:\\Windows\\System32\\drivers\\etc\\hosts";
//
//        printf("   🎯 Using test file: %s\n", testFile);
//        printf("   📊 Testing comprehensive callback functionality...\n");
//
//        if (TestMountUnmountWithCallbacks(testFile)) {
//            printf("✅ PASSED: Callback functionality works correctly\n\n");
//            passedTests++;
//        } else {
//            printf("❌ FAILED: Callback functionality failed\n\n");
//        }
//    }
//
//    // 显示测试总结
//    printf("🎯 Test Summary\n");
//    printf("===============\n");
//    printf("Total Tests: %d\n", totalTests);
//    printf("Passed: %d\n", passedTests);
//    printf("Failed: %d\n", totalTests - passedTests);
//    printf("Success Rate: %.1f%%\n", totalTests > 0 ? (float)passedTests / totalTests * 100 : 0);
//
//    return (passedTests == totalTests) ? 0 : 1;
//}
