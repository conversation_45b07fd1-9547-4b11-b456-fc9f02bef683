@echo off
set SRC=E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20-New_ReBuild_Src\001_Creator_CAB\files
set CAB=files.cab

rem 1. 生成“相对路径”文件清单
forfiles /p "%SRC%" /s /c "cmd /c echo @relpath" > filelist_raw.txt
powershell -NoP -C "(gc filelist_raw.txt)|%%{$_.TrimStart('.\\')}|sc filelist.txt"

rem 2. 写 makecab 描述文件
(
echo .Set CabinetNameTemplate=%CAB%
echo .Set DiskDirectoryTemplate=.
echo .Set CompressionType=MSZIP
echo .Set Cabinet=on
echo .Set Compress=on
echo .Set SourceDir=%SRC%
echo @filelist.txt
) > package.ddf

rem 3. 执行打包
makecab /f package.ddf
echo 完成！生成的 CAB：%cd%\%CAB%
pause