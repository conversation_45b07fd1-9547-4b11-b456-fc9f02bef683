﻿/*
 * test_functions.h
 * VirtualDiskLib测试函数声明
 */

#ifndef TEST_FUNCTIONS_H
#define TEST_FUNCTIONS_H

#include "cmdline_parser.h"

#ifdef __cplusplus
extern "C" {
#endif

/*
 * 回调函数测试相关
 */
void TestProgressCallback(const std::string& taskId, int progress, const std::string& matchResult);
bool TestQueryTaskControlCallback(const std::string& taskId, int controlType);
void ResetTestCallbackState(void);
void PrintCallbackTestStats(void);

/*
 * 测试MountVirtualDisk和UnmountVirtualDisk的回调功能
 */
int TestMountUnmountWithCallbacks(const char* testFile);
int TestMountWithProgressCallback(const char* testFile);
int TestMountWithTaskControl(const char* testFile);
int TestUnmountWithCallbacks(const char* driveLetter);

/*
 * 测试GetLibraryInfo函数
 *
 * 返回值：
 *   1: 测试通过，0: 测试失败
 */
int TestGetLibraryInfo(void);

/*
 * 测试GetErrorDescription函数
 * 
 * 返回值：
 *   1: 测试通过，0: 测试失败
 */
int TestGetErrorDescription(void);

/*
 * 测试MountVirtualDisk函数
 * 使用明文字符串传入JSON参数
 * 
 * 返回值：
 *   1: 测试通过，0: 测试失败
 */
int TestMountVirtualDisk(void);

/*
 * 测试UnmountVirtualDisk函数
 * 使用明文字符串传入JSON参数
 * 
 * 返回值：
 *   1: 测试通过，0: 测试失败
 */
int TestUnmountVirtualDisk(void);

/*
 * 测试GetMountStatus函数
 * 使用明文字符串传入JSON参数
 * 
 * 返回值：
 *   1: 测试通过，0: 测试失败
 */
int TestGetMountStatus(void);

///*
// * 处理测试命令
// *
// * 参数：
// *   args: 命令行参数
// *
// * 返回值：
// *   0: 成功，非0: 失败
// */
//int HandleTestCommand(const CommandLineArgs* args);

/*
 * 创建测试用的临时ISO文件
 * 
 * 参数：
 *   filePath: 输出文件路径
 *   pathSize: 路径缓冲区大小
 * 
 * 返回值：
 *   1: 成功，0: 失败
 */
int CreateTestISOFile(char* filePath, int pathSize);

/*
 * 清理测试文件
 * 
 * 参数：
 *   filePath: 要删除的文件路径
 */
void CleanupTestFile(const char* filePath);

/*
 * 打印测试结果
 * 
 * 参数：
 *   testName: 测试名称
 *   result: 测试结果
 *   details: 详细信息
 */
void PrintTestResult(const char* testName, int result, const char* details);

/*
 * 验证JSON响应格式
 * 
 * 参数：
 *   jsonResponse: JSON响应字符串
 *   expectedFields: 期望的字段列表（以逗号分隔）
 * 
 * 返回值：
 *   1: 格式正确，0: 格式错误
 */
int ValidateJsonResponse(const char* jsonResponse, const char* expectedFields);

#ifdef __cplusplus
}
#endif

#endif // TEST_FUNCTIONS_H
