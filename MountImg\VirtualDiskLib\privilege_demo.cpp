/*
 * VirtualDiskLib 权限检查演示程序
 * 展示权限检查、服务状态检查和用户提示功能
 */

#include <windows.h>
#include <stdio.h>
#include "../MountImg.h"

void PrintSeparator(const char* title) {
    printf("\n");
    printf("═══════════════════════════════════════════════════════════════\n");
    printf("  %s\n", title);
    printf("═══════════════════════════════════════════════════════════════\n");
}

void PrintSubSeparator(const char* title) {
    printf("\n");
    printf("─────────────────────────────────────────────────────────────\n");
    printf("  %s\n", title);
    printf("─────────────────────────────────────────────────────────────\n");
}

void TestPrivilegeCheck() {
    PrintSeparator("权限检查演示");
    
    // 检查当前用户权限
    BOOL isAdmin = IsRunAsAdministrator();
    printf("🔐 当前用户权限: %s\n", isAdmin ? "管理员" : "普通用户");
    
    // 检查服务状态
    int serviceStatus = CheckImDiskImgServiceStatus();
    printf("🔧 ImDiskImg 服务状态: ");
    switch (serviceStatus) {
        case 0: printf("运行中\n"); break;
        case 1: printf("已停止 (可以启动，如果有管理员权限)\n"); break;
        case 2: printf("未安装\n"); break;
        case 3: printf("访问被拒绝 (权限不足)\n"); break;
        default: printf("未知状态 (%d)\n", serviceStatus); break;
    }
    
    printf("\n");
}

void TestFileFormatCheck() {
    PrintSeparator("文件格式权限要求检查");
    
    const WCHAR* testFiles[] = {
        L"test.vhd",
        L"test.vhdx", 
        L"test.vmdk",
        L"test.vdi",
        L"test.dmg",
        L"test.iso",
        L"unknown.xyz"
    };
    
    int numFiles = sizeof(testFiles) / sizeof(testFiles[0]);
    
    for (int i = 0; i < numFiles; i++) {
        BOOL requiresAdmin = RequiresAdminPrivileges(testFiles[i]);
        printf("📁 %-12S: %s\n", testFiles[i], 
               requiresAdmin ? "需要管理员权限" : "普通用户权限即可");
    }
    
    printf("\n");
}

void TestPrivilegeWarnings() {
    PrintSeparator("权限提示信息演示");
    
    const WCHAR* testFiles[] = {
        L"example.vhdx",
        L"example.vmdk",
        L"example.vdi", 
        L"example.dmg"
    };
    
    int numFiles = sizeof(testFiles) / sizeof(testFiles[0]);
    
    for (int i = 0; i < numFiles; i++) {
        PrintSubSeparator("");
        printf("文件: %S\n", testFiles[i]);
        
        char warningBuffer[512];
        GeneratePrivilegeWarning(testFiles[i], warningBuffer, sizeof(warningBuffer));
        printf("提示信息:\n%s\n", warningBuffer);
    }
    
    printf("\n");
}

void TestMountingScenarios() {
    PrintSeparator("挂载场景分析");
    
    BOOL isAdmin = IsRunAsAdministrator();
    int serviceStatus = CheckImDiskImgServiceStatus();
    
    printf("基于当前环境的挂载能力分析:\n\n");
    
    // VHD 格式
    printf("🟢 VHD 格式:\n");
    printf("   权限要求: 普通用户\n");
    printf("   当前状态: %s\n", "✅ 可以挂载");
    printf("   技术实现: 直接通过 ImDisk 驱动\n\n");
    
    // VHDX 格式
    printf("🟡 VHDX 格式:\n");
    printf("   权限要求: 管理员\n");
    if (isAdmin && serviceStatus <= 1) {
        printf("   当前状态: %s\n", "✅ 可以挂载");
    } else if (!isAdmin) {
        printf("   当前状态: %s\n", "❌ 权限不足");
        printf("   解决方案: 以管理员身份运行程序\n");
    } else {
        printf("   当前状态: %s\n", "❌ 服务不可用");
        printf("   解决方案: 检查 ImDisk 安装和 .NET Framework\n");
    }
    printf("   技术实现: ImDiskImg 服务 + DiscUtils\n\n");
    
    // VMDK 格式
    printf("🟡 VMDK 格式:\n");
    printf("   权限要求: 管理员\n");
    if (isAdmin && serviceStatus <= 1) {
        printf("   当前状态: %s\n", "✅ 可以挂载");
    } else if (!isAdmin) {
        printf("   当前状态: %s\n", "❌ 权限不足");
        printf("   解决方案: 以管理员身份运行程序\n");
    } else {
        printf("   当前状态: %s\n", "❌ 服务不可用");
        printf("   解决方案: 检查 ImDisk 安装和 .NET Framework\n");
    }
    printf("   技术实现: ImDiskImg 服务 + DiscUtils\n\n");
}

void PrintRecommendations() {
    PrintSeparator("使用建议");
    
    BOOL isAdmin = IsRunAsAdministrator();
    
    if (isAdmin) {
        printf("✅ 当前以管理员身份运行，支持所有格式:\n");
        printf("   • VHD - 完全支持\n");
        printf("   • VHDX - 完全支持\n");
        printf("   • VMDK - 完全支持\n");
        printf("   • VDI - 完全支持\n");
        printf("   • DMG - 完全支持\n\n");
        
        printf("💡 建议:\n");
        printf("   • 可以使用任何支持的虚拟磁盘格式\n");
        printf("   • 确保 .NET Framework 4.0+ 已安装以获得最佳兼容性\n");
        printf("   • 定期检查 ImDisk 驱动更新\n");
    } else {
        printf("⚠️  当前以普通用户身份运行，功能受限:\n");
        printf("   • VHD - ✅ 完全支持\n");
        printf("   • VHDX - ❌ 需要管理员权限\n");
        printf("   • VMDK - ❌ 需要管理员权限\n");
        printf("   • VDI - ❌ 需要管理员权限\n");
        printf("   • DMG - ❌ 需要管理员权限\n\n");
        
        printf("💡 建议:\n");
        printf("   • 对于日常使用，优先选择 VHD 格式\n");
        printf("   • 如需挂载其他格式，请以管理员身份运行程序\n");
        printf("   • 右键点击程序 → 选择'以管理员身份运行'\n");
    }
    
    printf("\n");
}

int main() {
    // 设置控制台输出为 UTF-8
    SetConsoleOutputCP(CP_UTF8);
    
    printf("VirtualDiskLib 权限检查和用户提示功能演示\n");
    printf("版本: 1.0\n");
    printf("基于: ImDisk + DiscUtils\n");
    
    // 初始化 MountImg 模块
    if (!InitializeMountImg_Complete()) {
        printf("❌ 初始化失败，某些功能可能不可用\n");
    }
    
    // 运行各种测试
    TestPrivilegeCheck();
    TestFileFormatCheck();
    TestPrivilegeWarnings();
    TestMountingScenarios();
    PrintRecommendations();
    
    PrintSeparator("演示完成");
    printf("感谢使用 VirtualDiskLib！\n");
    printf("如需了解更多信息，请参考 VirtualDiskLib_UserGuide.md\n\n");
    
    printf("按任意键退出...");
    getchar();
    
    return 0;
}
