@echo off
echo Debug Information Viewer
echo =======================

echo.
echo This script will help capture debug output from VirtualDiskLib
echo.

echo 1. Starting DebugView (if available)...
if exist "C:\Program Files\Sysinternals\Dbgview.exe" (
    echo Found DebugView at Sysinternals location
    start "DebugView" "C:\Program Files\Sysinternals\Dbgview.exe"
) else if exist "DebugView.exe" (
    echo Found DebugView in current directory
    start "DebugView" "DebugView.exe"
) else (
    echo DebugView not found. Debug output will go to system debug stream.
    echo You can download DebugView from Microsoft Sysinternals.
)

echo.
echo 2. Waiting 3 seconds for DebugView to start...
timeout /t 3 /nobreak >nul

echo.
echo 3. Running VirtualDiskTool with debug output...
echo ================================================

echo.
echo Starting test in 2 seconds...
echo Make sure <PERSON>bug<PERSON><PERSON><PERSON> is running to capture debug output!
timeout /t 2 /nobreak >nul

echo.
echo Running VirtualDiskTool32.exe...
VirtualDiskTool32.exe

echo.
echo Test completed. Check DebugView for detailed debug information.
echo.
echo Debug information includes:
echo - File accessibility checks
echo - ImDisk command execution
echo - Process creation and exit codes
echo - File system verification
echo - Drive accessibility checks
echo.
pause
