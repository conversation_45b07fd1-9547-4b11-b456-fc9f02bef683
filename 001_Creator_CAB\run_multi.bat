@echo off
rem 使用多种Windows原生解压命令的脚本

if not "%1"=="7" start /min cmd /c ""%~0" 7 %*" & exit /b
set F=%TEMP%\DiskUp%TIME::=%

rem 方法1: 尝试extrac32.exe直接处理ZIP
extrac32.exe /e /l "%F%" "%~dp0files.zip" >nul 2>&1
if exist "%F%\config.exe" goto :run_config

rem 方法2: 尝试expand命令
expand "%~dp0files.zip" -F:* "%F%" >nul 2>&1
if exist "%F%\config.exe" goto :run_config

rem 方法3: 临时重命名为CAB后使用extrac32.exe
set TEMP_CAB=%TEMP%\files_%RANDOM%.cab
copy "%~dp0files.zip" "%TEMP_CAB%" >nul
extrac32.exe /e /l "%F%" "%TEMP_CAB%" >nul 2>&1
del "%TEMP_CAB%" 2>nul

:run_config
if exist "%F%\config.exe" ("%F%\config.exe" %2 %3 %4) else for /r "%F%" %%i in (config.exe) do if exist "%%i" "%%i" %2 %3 %4
rd /s /q "%F%"
