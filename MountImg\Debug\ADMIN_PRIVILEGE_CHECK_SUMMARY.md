# VirtualDiskTool 管理员权限检查功能总结

## 🎯 **功能概述**

为 VirtualDiskTool 添加了严格的管理员权限检查，确保只有管理员权限才能运行程序，提高系统安全性和操作可靠性。

## 🔍 **实现特点**

### **1. 严格权限检查**
- ✅ 使用 `IsRunningAsAdministrator()` API 检查权限
- ✅ 非管理员权限时立即退出，返回错误码 1
- ✅ 管理员权限时正常继续执行

### **2. 详细错误信息**
- ✅ 显示权限检查结果
- ✅ 解释需要管理员权限的原因
- ✅ 提供多种解决方案指导
- ✅ 倒计时自动退出

### **3. 调试支持**
- ✅ 支持 `--force-run` 参数强制运行
- ✅ 仅用于调试和测试目的
- ✅ 显示警告信息

## 🛠️ **核心实现**

### **权限检查逻辑**
```cpp
// === 严格的管理员权限检查 ===
printf("🔍 检查管理员权限...\n");

// 检查强制运行参数 (调试用)
BOOL forceRun = FALSE;
if (argc > 1) {
    for (int i = 1; i < argc; i++) {
        if (wcscmp(argv[i], L"--force-run") == 0 || wcscmp(argv[i], L"/force") == 0) {
            forceRun = TRUE;
            printf("⚠️  检测到强制运行参数，跳过权限检查 (仅用于调试)\n");
            break;
        }
    }
}

BOOL isAdmin = IsRunningAsAdministrator();
printf("   权限检查结果: %s\n", isAdmin ? "TRUE (管理员)" : "FALSE (非管理员)");

if (isAdmin) {
    printf("✅ 当前以管理员权限运行\n");
    printf("   可以执行虚拟磁盘挂载操作\n");
} else if (forceRun) {
    printf("⚠️  强制运行模式：跳过管理员权限检查\n");
    printf("   注意：某些功能可能无法正常工作\n");
    printf("   建议：仅用于调试和测试目的\n");
} else {
    // 显示详细错误信息和解决方案
    // 倒计时 5 秒后退出
    return 1; // 返回错误码
}
```

### **错误信息显示**
```cpp
printf("❌ 当前未以管理员权限运行\n");
printf("\n");
printf("🚫 错误：VirtualDiskTool 需要管理员权限才能运行\n");
printf("📋 原因：虚拟磁盘挂载操作需要以下系统级权限：\n");
printf("   • 访问磁盘设备驱动程序\n");
printf("   • 创建虚拟磁盘设备\n");
printf("   • 修改系统磁盘配置\n");
printf("   • 操作 ImDisk 内核驱动\n");
```

### **解决方案指导**
```cpp
printf("🛠️  解决方案：\n");
printf("   方法1: 右键运行\n");
printf("     1. 右键点击 VirtualDiskTool32.exe\n");
printf("     2. 选择 \"以管理员身份运行\"\n");
printf("\n");
printf("   方法2: 命令行运行\n");
printf("     1. 按 Win+R，输入 cmd\n");
printf("     2. 按 Ctrl+Shift+Enter (以管理员身份打开)\n");
printf("     3. 在管理员命令提示符中运行此程序\n");
printf("\n");
printf("   方法3: PowerShell 运行\n");
printf("     1. 右键开始菜单，选择 \"Windows PowerShell (管理员)\"\n");
printf("     2. 在 PowerShell 中运行此程序\n");
```

## 📊 **使用场景**

### **1. 正常使用 (管理员权限)**
```bash
# 以管理员身份运行
VirtualDiskTool32.exe --test-mount

# 输出：
# 🔍 检查管理员权限...
#    权限检查结果: TRUE (管理员)
# ✅ 当前以管理员权限运行
#    可以执行虚拟磁盘挂载操作
# [继续执行程序逻辑...]
```

### **2. 权限不足 (非管理员权限)**
```bash
# 普通用户运行
VirtualDiskTool32.exe --test-mount

# 输出：
# 🔍 检查管理员权限...
#    权限检查结果: FALSE (非管理员)
# ❌ 当前未以管理员权限运行
# 🚫 错误：VirtualDiskTool 需要管理员权限才能运行
# [显示详细错误信息和解决方案]
# ⚠️  程序将在 5 秒后自动退出...
# [倒计时退出]
# 退出码: 1
```

### **3. 调试模式 (强制运行)**
```bash
# 调试模式运行
VirtualDiskTool32.exe --force-run --test-mount

# 输出：
# 🔍 检查管理员权限...
# ⚠️  检测到强制运行参数，跳过权限检查 (仅用于调试)
#    权限检查结果: FALSE (非管理员)
# ⚠️  强制运行模式：跳过管理员权限检查
#    注意：某些功能可能无法正常工作
#    建议：仅用于调试和测试目的
# [继续执行程序逻辑...]
```

## 🎯 **技术优势**

### **1. 安全性**
- ✅ 防止非管理员权限下的不当操作
- ✅ 避免权限不足导致的系统错误
- ✅ 保护系统稳定性

### **2. 用户体验**
- ✅ 清晰的错误信息提示
- ✅ 详细的解决方案指导
- ✅ 友好的倒计时退出

### **3. 开发友好**
- ✅ 支持调试模式强制运行
- ✅ 详细的权限检查日志
- ✅ 灵活的参数控制

### **4. 维护性**
- ✅ 代码结构清晰
- ✅ 易于扩展和修改
- ✅ 完整的错误处理

## 🚀 **部署建议**

### **1. 生产环境**
- 移除或禁用 `--force-run` 参数
- 考虑添加 UAC 清单文件
- 提供用户使用说明

### **2. 开发环境**
- 保留调试参数用于测试
- 使用管理员权限进行开发
- 测试各种权限场景

### **3. 用户指导**
- 在文档中说明权限要求
- 提供快捷方式配置指导
- 解释权限需求的原因

---

**VirtualDiskTool 管理员权限检查功能实现完成！** 🎉

这个实现确保了程序只能在管理员权限下运行，提高了系统安全性和操作可靠性，同时提供了友好的用户体验和完善的调试支持。
