﻿/*
 * json_builder.h
 * JSON请求构建和响应解析器
 */

#ifndef JSON_BUILDER_H
#define JSON_BUILDER_H

#include "cmdline_parser.h"

#ifdef __cplusplus
extern "C" {
#endif

/*
 * 构建挂载请求JSON
 * 
 * 参数：
 *   args: 命令行参数
 *   jsonOutput: 输出缓冲区
 *   bufferSize: 缓冲区大小
 * 
 * 返回值：
 *   0: 成功，非0: 失败
 */
int BuildMountRequest(const CommandLineArgs* args, char* jsonOutput, int bufferSize);

/*
 * 构建卸载请求JSON
 * 
 * 参数：
 *   args: 命令行参数
 *   jsonOutput: 输出缓冲区
 *   bufferSize: 缓冲区大小
 * 
 * 返回值：
 *   0: 成功，非0: 失败
 */
int BuildUnmountRequest(const CommandLineArgs* args, char* jsonOutput, int bufferSize);

/*
 * 构建状态查询请求JSON
 * 
 * 参数：
 *   args: 命令行参数
 *   jsonOutput: 输出缓冲区
 *   bufferSize: 缓冲区大小
 * 
 * 返回值：
 *   0: 成功，非0: 失败
 */
int BuildStatusRequest(const CommandLineArgs* args, char* jsonOutput, int bufferSize);

/*
 * 打印挂载响应
 * 
 * 参数：
 *   jsonResponse: JSON响应字符串
 */
void PrintMountResponse(const char* jsonResponse);

/*
 * 打印卸载响应
 * 
 * 参数：
 *   jsonResponse: JSON响应字符串
 */
void PrintUnmountResponse(const char* jsonResponse);

/*
 * 打印状态响应
 * 
 * 参数：
 *   jsonResponse: JSON响应字符串
 */
void PrintStatusResponse(const char* jsonResponse);

/*
 * 打印错误响应
 * 
 * 参数：
 *   jsonResponse: JSON响应字符串
 */
void PrintErrorResponse(const char* jsonResponse);

/*
 * 转义JSON字符串
 * 
 * 参数：
 *   input: 输入字符串
 *   output: 输出缓冲区
 *   outputSize: 输出缓冲区大小
 * 
 * 返回值：
 *   0: 成功，非0: 失败
 */
int EscapeJsonString(const char* input, char* output, int outputSize);

/*
 * 从JSON中提取字符串值
 * 
 * 参数：
 *   json: JSON字符串
 *   key: 键名
 *   output: 输出缓冲区
 *   outputSize: 输出缓冲区大小
 * 
 * 返回值：
 *   0: 成功，非0: 失败
 */
int ExtractJsonString(const char* json, const char* key, char* output, int outputSize);

/*
 * 从JSON中提取布尔值
 * 
 * 参数：
 *   json: JSON字符串
 *   key: 键名
 *   defaultValue: 默认值
 * 
 * 返回值：
 *   布尔值
 */
int ExtractJsonBool(const char* json, const char* key, int defaultValue);

/*
 * 从JSON中提取整数值
 * 
 * 参数：
 *   json: JSON字符串
 *   key: 键名
 *   defaultValue: 默认值
 * 
 * 返回值：
 *   整数值
 */
int ExtractJsonInt(const char* json, const char* key, int defaultValue);

#ifdef __cplusplus
}
#endif

#endif // JSON_BUILDER_H
