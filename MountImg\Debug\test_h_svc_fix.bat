@echo off
chcp 65001 >nul
echo ========================================
echo Testing h_svc Service Handle Fix
echo ========================================

echo.
echo 这个测试验证 h_svc 服务句柄为空问题的修复
echo.
echo 问题描述:
echo DiscUtils_Mount() 函数中 h_svc 为空
echo 导致 StartService(h_svc, 3, (void*)cmdline_ptr) 调用失败
echo DiscUtils 挂载功能无法正常工作
echo.

echo 问题原因:
echo h_svc 在 wWinMain 函数中初始化 (第2084行)
echo VirtualDiskLib 直接调用挂载函数时，wWinMain 可能未执行
echo 导致 h_svc 保持默认值 NULL
echo.

echo 修复方案:
echo 1. 在 InitializeImDisk() 中添加 ImDisk 服务初始化
echo 2. 参考 wWinMain 中的服务创建逻辑
echo 3. 在 MountImg.h 中添加 h_svc 外部声明
echo 4. 在 DiscUtils_Mount() 中添加 h_svc 有效性检查
echo 5. 添加详细的调试信息输出
echo.

echo 检查 ImDisk 服务状态...
echo ----------------------------------------

sc query ImDiskImg >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo ✅ ImDiskImg 服务已安装
    sc query ImDiskImg | findstr "STATE"
) else (
    echo ❌ ImDiskImg 服务未安装
    echo   InitializeImDisk() 应该会尝试创建服务
)

echo.
echo 检查 ImDisk 驱动状态...
sc query ImDisk >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo ✅ ImDisk 驱动已安装
    sc query ImDisk | findstr "STATE"
) else (
    echo ❌ ImDisk 驱动未安装
    echo   这可能影响 DiscUtils 功能
)

echo.
echo 启动 VirtualDiskTool32.exe 测试 h_svc 修复...
echo ----------------------------------------

VirtualDiskTool32.exe --test-mount

echo.
echo ----------------------------------------
echo 程序执行完毕，退出码: %ERRORLEVEL%
echo.

if %ERRORLEVEL% EQU 0 (
    echo ✅ SUCCESS: h_svc 服务句柄问题已修复
) else (
    echo ❌ FAILED: 程序运行出错，退出码: %ERRORLEVEL%
    echo.
    echo 可能的问题:
    echo 1. 权限不足，无法创建或访问服务
    echo 2. ImDisk 组件未正确安装
    echo 3. 服务创建失败
    echo 4. 其他系统问题
)

echo.
echo 检查目标 X: 驱动器...
if exist X:\ (
    echo ✅ SUCCESS: X: 驱动器已挂载 (h_svc 修复成功)
    echo 目录列表:
    dir X: /w
) else (
    echo ❌ FAILED: X: 驱动器未挂载
)

echo.
echo ========================================
echo h_svc 服务句柄修复技术说明:
echo ========================================
echo.
echo ✅ 问题分析:
echo   h_svc 是 ImDisk 服务的句柄，用于启动 DiscUtils 挂载服务
echo   原始初始化在 wWinMain 中: OpenService() 或 CreateService()
echo   VirtualDiskLib 直接调用时可能跳过这个初始化
echo   导致 h_svc 保持默认值 NULL，StartService() 调用失败
echo.
echo ✅ 修复实现:
echo   // InitializeImDisk() 中添加服务初始化
echo   SC_HANDLE h_scman = OpenSCManager(NULL, NULL, SC_MANAGER_CONNECT | SC_MANAGER_CREATE_SERVICE);
echo   if (h_scman) {
echo       // 尝试打开现有服务
echo       h_svc = OpenService(h_scman, L"ImDiskImg", SERVICE_CHANGE_CONFIG | SERVICE_START | SERVICE_STOP | DELETE);
echo       
echo       if (!h_svc) {
echo           // 服务不存在，尝试创建服务
echo           WCHAR module_name[MAX_PATH];
echo           GetModuleFileNameW(NULL, module_name, MAX_PATH);
echo           wcscat(module_name, L" /SVC");
echo           
echo           h_svc = CreateService(h_scman, L"ImDiskImg", L"ImDisk Image File mounter", 
echo                                 SERVICE_CHANGE_CONFIG | SERVICE_START | SERVICE_STOP | DELETE, 
echo                                 SERVICE_WIN32_OWN_PROCESS, SERVICE_AUTO_START, SERVICE_ERROR_NORMAL,
echo                                 module_name, NULL, NULL, L"ImDisk\\0", NULL, NULL);
echo       }
echo       CloseServiceHandle(h_scman);
echo   }
echo.
echo ✅ DiscUtils_Mount() 安全检查:
echo   if (!h_svc) {
echo       // h_svc 为空，DiscUtils 挂载无法进行
echo       return 1;
echo   }
echo   
echo   if (!StartService(h_svc, 3, (void*)cmdline_ptr)) {
echo       // StartService 失败
echo       DWORD lastError = GetLastError();
echo       CloseHandle(h);
echo       return 1;
echo   }
echo.
echo ✅ 调试信息输出:
echo   net_installed: TRUE/FALSE, init_ok: TRUE/FALSE, h_svc: VALID/NULL
echo   显示关键变量状态，便于问题定位
echo.
echo ✅ 服务管理逻辑:
echo   1. 尝试打开现有的 ImDiskImg 服务
echo   2. 如果服务不存在，创建新的服务
echo   3. 设置服务描述和配置
echo   4. 确保服务句柄有效
echo   5. 在 DiscUtils_Mount 中检查句柄有效性
echo.
echo ✅ 错误处理:
echo   - 服务管理器打开失败: 跳过服务初始化
echo   - 服务创建失败: h_svc 保持 NULL，DiscUtils 不可用
echo   - StartService 失败: 返回错误，尝试其他挂载方式
echo   - 权限不足: 显示相应错误信息
echo.

pause
