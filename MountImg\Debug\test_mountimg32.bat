@echo off
echo ========================================
echo Testing MountImg32.exe JSON Parameter Processing
echo ========================================

echo.
echo Test 1: JSON Parameter Parsing and UI Simulation
echo Command: MountImg32.exe /JSON "{\"file_path\":\"E:\\004_VMDK\\666666.vmdk\",\"drive\":\"X:\",\"readonly\":false,\"partition\":1}"
echo.

echo Running MountImg32.exe with detailed output...
echo ----------------------------------------

MountImg32.exe /JSON "{\"file_path\":\"E:\\004_VMDK\\666666.vmdk\",\"drive\":\"X:\",\"readonly\":false,\"partition\":1}"

echo.
echo ----------------------------------------
echo Exit code: %ERRORLEVEL%
echo.

echo Checking mount result...
if exist X:\ (
    echo ✅ SUCCESS: X: drive is mounted
    echo Directory listing:
    dir X: /w
) else (
    echo ❌ FAILED: X: drive is not mounted
)

echo.
echo ========================================
echo Test Analysis:
echo ========================================
echo This test verifies:
echo 1. JSON parameter parsing (file_path, drive, readonly, partition)
echo 2. UI control simulation (ID_COMBO1, ID_CHECK1, ID_COMBO2, ID_LISTVIEW)
echo 3. ID_PBUTTON1 file selection response simulation
echo 4. Mount operation execution
echo.

echo Press any key to continue...
pause
