/*
 * simple_json.cpp
 * 简化的JSON处理库实现
 */

#include "simple_json.h"
#include <cctype>
#include <stdexcept>
#include <cstdlib>  // for atof

namespace SimpleJson {

class Parser {
private:
    const std::string& json_str;
    size_t pos;
    
public:
    Parser(const std::string& str) : json_str(str), pos(0) {}
    
    Value parse() {
        skip_whitespace();
        return parse_value();
    }
    
private:
    void skip_whitespace() {
        while (pos < json_str.length() && std::isspace(json_str[pos])) {
            pos++;
        }
    }
    
    char current_char() {
        return pos < json_str.length() ? json_str[pos] : '\0';
    }
    
    char next_char() {
        return ++pos < json_str.length() ? json_str[pos] : '\0';
    }
    
    Value parse_value() {
        skip_whitespace();
        char c = current_char();
        
        switch (c) {
            case 'n': return parse_null();
            case 't': case 'f': return parse_bool();
            case '"': return parse_string();
            case '[': return parse_array();
            case '{': return parse_object();
            case '-': case '0': case '1': case '2': case '3': case '4':
            case '5': case '6': case '7': case '8': case '9':
                return parse_number();
            default:
                throw std::runtime_error("Unexpected character in JSON");
        }
    }
    
    Value parse_null() {
        if (json_str.substr(pos, 4) == "null") {
            pos += 4;
            return Value();
        }
        throw std::runtime_error("Invalid null value");
    }
    
    Value parse_bool() {
        if (json_str.substr(pos, 4) == "true") {
            pos += 4;
            return Value(true);
        }
        if (json_str.substr(pos, 5) == "false") {
            pos += 5;
            return Value(false);
        }
        throw std::runtime_error("Invalid boolean value");
    }
    
    Value parse_string() {
        if (current_char() != '"') {
            throw std::runtime_error("Expected '\"' at start of string");
        }
        next_char(); // skip opening quote
        
        std::string result;
        while (current_char() != '"' && current_char() != '\0') {
            if (current_char() == '\\') {
                next_char();
                switch (current_char()) {
                    case '"': result += '"'; break;
                    case '\\': result += '\\'; break;
                    case '/': result += '/'; break;
                    case 'b': result += '\b'; break;
                    case 'f': result += '\f'; break;
                    case 'n': result += '\n'; break;
                    case 'r': result += '\r'; break;
                    case 't': result += '\t'; break;
                    default:
                        throw std::runtime_error("Invalid escape sequence");
                }
            } else {
                result += current_char();
            }
            next_char();
        }
        
        if (current_char() != '"') {
            throw std::runtime_error("Unterminated string");
        }
        next_char(); // skip closing quote
        
        return Value(result);
    }
    
    Value parse_number() {
        std::string number_str;
        
        // Handle negative sign
        if (current_char() == '-') {
            number_str += current_char();
            next_char();
        }
        
        // Parse digits
        while (std::isdigit(current_char())) {
            number_str += current_char();
            next_char();
        }
        
        // Handle decimal point
        if (current_char() == '.') {
            number_str += current_char();
            next_char();
            while (std::isdigit(current_char())) {
                number_str += current_char();
                next_char();
            }
        }
        
        // Handle exponent
        if (current_char() == 'e' || current_char() == 'E') {
            number_str += current_char();
            next_char();
            if (current_char() == '+' || current_char() == '-') {
                number_str += current_char();
                next_char();
            }
            while (std::isdigit(current_char())) {
                number_str += current_char();
                next_char();
            }
        }
        
        // 使用atof代替std::stod以兼容VS2013
        return Value(atof(number_str.c_str()));
    }
    
    Value parse_array() {
        if (current_char() != '[') {
            throw std::runtime_error("Expected '[' at start of array");
        }
        next_char(); // skip opening bracket
        
        Value array = Value::array();
        skip_whitespace();
        
        if (current_char() == ']') {
            next_char(); // skip closing bracket
            return array;
        }
        
        while (true) {
            array.push_back(parse_value());
            skip_whitespace();
            
            if (current_char() == ']') {
                next_char(); // skip closing bracket
                break;
            }
            
            if (current_char() != ',') {
                throw std::runtime_error("Expected ',' or ']' in array");
            }
            next_char(); // skip comma
            skip_whitespace();
        }
        
        return array;
    }
    
    Value parse_object() {
        if (current_char() != '{') {
            throw std::runtime_error("Expected '{' at start of object");
        }
        next_char(); // skip opening brace
        
        Value object = Value::object();
        skip_whitespace();
        
        if (current_char() == '}') {
            next_char(); // skip closing brace
            return object;
        }
        
        while (true) {
            // Parse key
            skip_whitespace();
            if (current_char() != '"') {
                throw std::runtime_error("Expected string key in object");
            }
            Value key = parse_string();
            
            // Parse colon
            skip_whitespace();
            if (current_char() != ':') {
                throw std::runtime_error("Expected ':' after key in object");
            }
            next_char(); // skip colon
            
            // Parse value
            Value value = parse_value();
            object[key.as_string()] = value;
            
            skip_whitespace();
            if (current_char() == '}') {
                next_char(); // skip closing brace
                break;
            }
            
            if (current_char() != ',') {
                throw std::runtime_error("Expected ',' or '}' in object");
            }
            next_char(); // skip comma
        }
        
        return object;
    }
};

Value parse(const std::string& json_str) {
    try {
        Parser parser(json_str);
        return parser.parse();
    } catch (const std::exception&) {
        // 解析失败时返回空对象
        return Value::object();
    }
}

} // namespace SimpleJson
