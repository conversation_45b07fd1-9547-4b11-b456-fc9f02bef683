@echo off
chcp 65001 >nul
echo ========================================
echo Testing MountImg32.exe wWinMain JSON Processing
echo ========================================

echo.
echo Test 1: JSON Parameter with Quotes
echo Command: MountImg32.exe /JSON "{\"file_path\":\"E:\\004_VMDK\\666666.vmdk\",\"drive\":\"X:\",\"readonly\":false,\"partition\":1}"
echo.

echo Running MountImg32.exe...
echo ----------------------------------------

MountImg32.exe /JSON "{\"file_path\":\"E:\\004_VMDK\\666666.vmdk\",\"drive\":\"X:\",\"readonly\":false,\"partition\":1}"

echo.
echo ----------------------------------------
echo Exit code: %ERRORLEVEL%
echo.

echo Test 2: Checking mount result...
if exist X:\ (
    echo ✅ SUCCESS: X: drive is mounted
    echo Directory listing:
    dir X: /w
) else (
    echo ❌ FAILED: X: drive is not mounted
)

echo.
echo ========================================
echo wWinMain Function Analysis:
echo ========================================
echo This test verifies:
echo 1. Unicode command line parameter processing (lpCmdLine)
echo 2. /JSON parameter extraction from lpCmdLine
echo 3. JSON string parsing with proper quote handling
echo 4. Conversion from Unicode to ANSI for JSON processing
echo 5. Interface control simulation based on parsed parameters
echo.
echo Key improvements in wWinMain:
echo - Direct Unicode command line processing
echo - Robust JSON string extraction with quote support
echo - Better error handling for malformed parameters
echo - Integration with existing MountImg.c global variables
echo.

pause
