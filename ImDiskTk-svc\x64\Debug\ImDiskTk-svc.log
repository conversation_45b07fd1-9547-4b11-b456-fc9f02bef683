﻿C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\MSBuild\Microsoft\VC\v150\Platforms\x64\PlatformToolsets\v141_xp\Toolset.targets(39,5): warning MSB8051: 面向 Windows XP 的支持已被弃用，将来的 Visual Studio 版本不再提供该支持。请访问 https://go.microsoft.com/fwlink/?linkid=2023588，获取详细信息。
  ImDiskTk-svc.c
libcmtd.lib(init.obj) : error LNK2019: 无法解析的外部符号 _CrtDbgReport，函数 _CRT_RTC_INIT 中引用了该符号
libcmtd.lib(init.obj) : error LNK2019: 无法解析的外部符号 _CrtDbgReportW，函数 _CRT_RTC_INITW 中引用了该符号
libcmtd.lib(error.obj) : error LNK2019: 无法解析的外部符号 strcpy_s，函数 "void __cdecl _RTC_StackFailure(void *,char const *)" (?_RTC_StackFailure@@YAXPEAXPEBD@Z) 中引用了该符号
libcmtd.lib(error.obj) : error LNK2019: 无法解析的外部符号 strcat_s，函数 "void __cdecl _RTC_StackFailure(void *,char const *)" (?_RTC_StackFailure@@YAXPEAXPEBD@Z) 中引用了该符号
libcmtd.lib(error.obj) : error LNK2019: 无法解析的外部符号 __stdio_common_vsprintf_s，函数 _vsprintf_s_l 中引用了该符号
libcmtd.lib(error.obj) : error LNK2001: 无法解析的外部符号 __C_specific_handler_noexcept
libcmtd.lib(pdblkup.obj) : error LNK2019: 无法解析的外部符号 _wmakepath_s，函数 "int __cdecl GetPdbDllPathFromFilePath(wchar_t const *,wchar_t *,unsigned __int64)" (?GetPdbDllPathFromFilePath@@YAHPEB_WPEA_W_K@Z) 中引用了该符号
libcmtd.lib(pdblkup.obj) : error LNK2019: 无法解析的外部符号 _wsplitpath_s，函数 "int __cdecl GetPdbDllPathFromFilePath(wchar_t const *,wchar_t *,unsigned __int64)" (?GetPdbDllPathFromFilePath@@YAHPEB_WPEA_W_K@Z) 中引用了该符号
libcmtd.lib(pdblkup.obj) : error LNK2019: 无法解析的外部符号 wcscpy_s，函数 "int __cdecl GetPdbDllPathFromFilePath(wchar_t const *,wchar_t *,unsigned __int64)" (?GetPdbDllPathFromFilePath@@YAHPEB_WPEA_W_K@Z) 中引用了该符号
libcmtd.lib(pdblkup.obj) : error LNK2019: 无法解析的外部符号 __vcrt_GetModuleFileNameW，函数 "struct HINSTANCE__ * __cdecl GetPdbDll(void)" (?GetPdbDll@@YAPEAUHINSTANCE__@@XZ) 中引用了该符号
libcmtd.lib(pdblkup.obj) : error LNK2019: 无法解析的外部符号 __vcrt_GetModuleHandleW，函数 "struct HINSTANCE__ * __cdecl GetPdbDll(void)" (?GetPdbDll@@YAPEAUHINSTANCE__@@XZ) 中引用了该符号
libcmtd.lib(pdblkup.obj) : error LNK2019: 无法解析的外部符号 __vcrt_LoadLibraryExW，函数 "struct HINSTANCE__ * __cdecl GetPdbDll(void)" (?GetPdbDll@@YAPEAUHINSTANCE__@@XZ) 中引用了该符号
E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20-New_ReBuild_Src\x64\Debug\ImDiskTk-svc64.exe : fatal error LNK1120: 12 个无法解析的外部命令
