@echo off
chcp 65001 >nul
echo ========================================
echo Testing Compilation Fix for goto cleanup
echo ========================================

echo.
echo 这个测试验证修复 goto cleanup 编译错误的结果
echo.
echo 编译错误修复说明:
echo 错误: C4533: initialization of 'create_data' is skipped by 'goto cleanup'
echo 原因: goto 语句跳过了变量初始化
echo 修复: 将所有变量声明移到函数开始处
echo.

echo 修复前的问题:
echo   if (h == INVALID_HANDLE_VALUE) {
echo       goto cleanup;  // 跳过后面的变量初始化
echo   }
echo   struct { ... } create_data = {};  // 这里的初始化被跳过
echo.

echo 修复后的解决方案:
echo   // 在函数开始处声明所有变量
echo   HANDLE h = INVALID_HANDLE_VALUE;
echo   struct { ... } create_data = {};
echo   DWORD dw;
echo   DEV_BROADCAST_VOLUME dbv = { sizeof(dbv), DBT_DEVTYP_VOLUME };
echo   DWORD_PTR dwp;
echo   HMODULE h_cpl = NULL;
echo   
echo   // 然后使用 goto cleanup
echo   if (h == INVALID_HANDLE_VALUE) {
echo       goto cleanup;  // 现在不会跳过初始化
echo   }
echo.

echo 启动 VirtualDiskTool32.exe 测试修复结果...
echo ----------------------------------------

VirtualDiskTool32.exe --test-unmount

echo.
echo ----------------------------------------
echo 程序执行完毕，退出码: %ERRORLEVEL%
echo.

if %ERRORLEVEL% EQU 0 (
    echo ✅ SUCCESS: 编译错误已修复，程序正常运行
) else (
    echo ❌ FAILED: 程序运行出错，退出码: %ERRORLEVEL%
)

echo.
echo 检查目标 X: 驱动器...
if exist X:\ (
    echo ❌ FAILED: X: 驱动器仍然存在 (卸载失败)
    echo 目录列表:
    dir X: /w
) else (
    echo ✅ SUCCESS: X: 驱动器已卸载
)

echo.
echo ========================================
echo 修复后的代码结构:
echo ========================================
echo.
echo ✅ 修复的变量声明顺序:
echo   1. HANDLE h = INVALID_HANDLE_VALUE;
echo   2. DWORD access_list[] = { ... };
echo   3. int n_access;
echo   4. struct { IMDISK_CREATE_DATA icd; WCHAR buff[MAX_PATH + 15]; } create_data = {};
echo   5. DWORD dw;
echo   6. DEV_BROADCAST_VOLUME dbv = { sizeof(dbv), DBT_DEVTYP_VOLUME };
echo   7. DWORD_PTR dwp;
echo   8. HMODULE h_cpl = NULL;
echo.
echo ✅ 修复的重复声明:
echo   - 移除了重复的 DEV_BROADCAST_VOLUME dbv 声明
echo   - 移除了重复的 DWORD_PTR dwp 声明
echo   - 移除了重复的 HMODULE h_cpl 声明
echo   - 重用已声明的变量
echo.
echo ✅ goto cleanup 流程:
echo   现在所有 goto cleanup 语句都不会跳过变量初始化
echo   所有变量在函数开始时就已经正确初始化
echo.
echo 📋 C++ goto 规则:
echo   - goto 不能跳过变量的初始化
echo   - 所有可能被 goto 跳过的变量必须在 goto 之前声明
echo   - 变量初始化必须在所有可能的 goto 路径之前完成
echo.

pause
