@echo off
echo ImDisk Virtual Disk Test Script
echo ================================

echo.
echo 1. Checking ImDisk installation...
imdisk -l
if %errorlevel% neq 0 (
    echo ERROR: ImDisk is not installed or not in PATH
    pause
    exit /b 1
)

echo.
echo 2. Checking test files...
if exist "E:\2G.vmdk" (
    echo FOUND: E:\2G.vmdk
    dir "E:\2G.vmdk"
) else (
    echo NOT FOUND: E:\2G.vmdk
)

if exist "E:\666666.vmdk" (
    echo FOUND: E:\666666.vmdk
    dir "E:\666666.vmdk"
) else (
    echo NOT FOUND: E:\666666.vmdk
)

if exist "E:\002_VHD、vhd.vhd" (
    echo FOUND: E:\002_VHD、vhd.vhd
    dir "E:\002_VHD、vhd.vhd"
) else (
    echo NOT FOUND: E:\002_VHD、vhd.vhd
)

if exist "E:\003_VHDX\VHDX.vhdx" (
    echo FOUND: E:\003_VHDX\VHDX.vhdx
    dir "E:\003_VHDX\VHDX.vhdx"
) else (
    echo NOT FOUND: E:\003_VHDX\VHDX.vhdx
)

if exist "E:\004_VMDK\666666.vmdk" (
    echo FOUND: E:\004_VMDK\666666.vmdk
    dir "E:\004_VMDK\666666.vmdk"
) else (
    echo NOT FOUND: E:\004_VMDK\666666.vmdk
)

echo.
echo 3. Testing manual ImDisk mount (if first file exists)...
if exist "E:\2G.vmdk" (
    echo Attempting to mount E:\2G.vmdk to X: using ImDisk directly...
    imdisk -a -f "E:\2G.vmdk" -m X: -p "r"
    if %errorlevel% equ 0 (
        echo SUCCESS: Mount successful
        echo Waiting 5 seconds...
        timeout /t 5 /nobreak
        echo Unmounting...
        imdisk -d -m X:
    ) else (
        echo ERROR: Mount failed with error code %errorlevel%
    )
) else (
    echo SKIP: E:\2G.vmdk not found, skipping manual test
)

echo.
echo 4. Testing DiscUtils (if available)...
if exist "DiscUtilsDevio.exe" (
    echo DiscUtilsDevio.exe found
) else (
    echo DiscUtilsDevio.exe not found in current directory
)

echo.
echo Test completed.
pause
