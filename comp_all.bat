@for /f %%I in ('powershell -Command "Get-Date -Format yyyyMMdd"') do @set D=%%I
echo #define APP_VERSION "%D%">inc\build.h
echo #define APP_NUMBER %D%>>inc\build.h
cd ImDisk-Dlg
call comp32.bat -
call comp64.bat -
cd ..\ImDiskTk-svc
call comp32.bat -
call comp64.bat -
cd ..\install
call comp32.bat -
call comp64.bat -
cd ..\MountImg
call comp32.bat -
call comp64.bat -
cd ..\RamDiskUI
call comp32.bat -
call comp64.bat -
cd ..\RamDyn
call comp32.bat -
call comp64.bat -
cd ..
@pause