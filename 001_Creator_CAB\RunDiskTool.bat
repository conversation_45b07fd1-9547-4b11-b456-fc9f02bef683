@echo off
rem ========================================
rem 磁盘挂载工具启动器 (XP兼容版)
rem ========================================

echo 磁盘挂载工具启动中...

rem 设置路径变量
set "ZIP_FILE=%~dp0files.zip"
set "TEMP_DIR=%TEMP%\DiskUp"

rem 检查ZIP文件
if not exist "%ZIP_FILE%" (
    echo 错误: 找不到files.zip文件
    pause
    exit /b 1
)

rem 清理并创建临时目录
if exist "%TEMP_DIR%" rmdir /s /q "%TEMP_DIR%" 2>nul
mkdir "%TEMP_DIR%"

rem 使用VBScript解压 (XP兼容)
echo 正在解压文件...

echo Set fso = CreateObject("Scripting.FileSystemObject") > "%TEMP%\extract.vbs"
echo Set shell = CreateObject("Shell.Application") >> "%TEMP%\extract.vbs"
echo Set zip = shell.NameSpace("%ZIP_FILE%") >> "%TEMP%\extract.vbs"
echo Set folder = shell.NameSpace("%TEMP_DIR%") >> "%TEMP%\extract.vbs"
echo If Not zip Is Nothing Then >> "%TEMP%\extract.vbs"
echo     folder.CopyHere zip.Items, 20 >> "%TEMP%\extract.vbs"
echo     WScript.Sleep 3000 >> "%TEMP%\extract.vbs"
echo End If >> "%TEMP%\extract.vbs"

cscript //nologo "%TEMP%\extract.vbs"
del "%TEMP%\extract.vbs"

rem 查找并运行config.exe
set "CONFIG_EXE="
if exist "%TEMP_DIR%\config.exe" (
    set "CONFIG_EXE=%TEMP_DIR%\config.exe"
) else (
    for /r "%TEMP_DIR%" %%f in (config.exe) do (
        if exist "%%f" set "CONFIG_EXE=%%f"
    )
)

if "%CONFIG_EXE%"=="" (
    echo 错误: 找不到config.exe
    pause
    exit /b 1
)

rem 运行config.exe
echo 启动配置程序...
"%CONFIG_EXE%" %*

rem 清理临时文件
rmdir /s /q "%TEMP_DIR%" 2>nul

echo 完成
exit /b 0
