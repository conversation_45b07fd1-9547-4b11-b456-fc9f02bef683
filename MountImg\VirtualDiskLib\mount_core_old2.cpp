﻿/*
 * mount_core_new.cpp
 * 完全按照MountImg.c真实流程重新设计的挂载核心
 * 
 * 真实的MountImg.c流程：
 * 1. 用户点击确定 → CreateThread(Mount)
 * 2. Mount() → Imdisk_Mount(new_file || !net_installed)
 * 3. 如果失败 → DiscUtils_Mount()
 * 4. 验证结果 → GetVolumeInformation(drive)
 */

#define _WIN32_WINNT 0x0601
#define OEMRESOURCE
#define _CRT_SECURE_NO_WARNINGS

// 兼容性宏定义
#ifndef _countof
#define _countof(array) (sizeof(array) / sizeof(array[0]))
#endif

#include <windows.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <shlwapi.h>
#include <wtsapi32.h>
#include <winsvc.h>
#include <userenv.h>
#include <winternl.h>

#include "mount_core.h"
#include "../../inc/imdisk.h"
#include "../../inc/imdisktk.h"

// ===== 完全按照MountImg.c的全局变量定义 =====

// GetTickCount64运行时检测支持
typedef ULONGLONG (WINAPI *PGetTickCount64)(void);
static PGetTickCount64 g_pGetTickCount64 = NULL;
static BOOL g_bTickCountInitialized = FALSE;

// ImDisk API函数指针
static FARPROC ImDisk_GetDeviceListEx = NULL;
static FARPROC ImDisk_ForceRemoveDevice = NULL; 
static FARPROC ImDisk_NotifyShellDriveLetter = NULL;

// 设备类型和选项数组（完全按照MountImg.c）
static WCHAR dev_list[] = {'h', 'c', 'f'};  // 硬盘、光盘、软盘
static WCHAR ro_list[] = {'w', 'o'};        // 读写、只读
static WCHAR *ro_discutils_list[] = {L"", L" /readonly"};  // DiscUtils只读参数
static WCHAR *rm_list[] = {L"fix", L"rem"}; // 固定、可移动
static WCHAR *boot_list[] = {L"", L" -P"};  // 启动参数

// 全局变量（完全按照MountImg.c）
static WCHAR filename[MAX_PATH] = {L""};
static WCHAR drive[MAX_PATH + 2] = {L""};
static BYTE net_installed = FALSE;
static UINT dev_type = 0;
static UINT partition = 1;
static BOOL readonly = FALSE;
static BOOL removable = FALSE;
static BOOL win_boot = FALSE;
static long device_number = -1;
static ULONG list_device[64000];
static SC_HANDLE h_svc = NULL;

// ===== 完全按照MountImg.c的函数实现 =====

// GetTickCount兼容函数
static void InitTickCountFunction(void)
{
    if (!g_bTickCountInitialized) {
        HMODULE hKernel32 = GetModuleHandle(L"kernel32.dll");
        if (hKernel32) {
            g_pGetTickCount64 = (PGetTickCount64)GetProcAddress(hKernel32, "GetTickCount64");
        }
        g_bTickCountInitialized = TRUE;
    }
}

static __int64 GetTickCount_Compatible(void)
{
    InitTickCountFunction();
    if (g_pGetTickCount64) {
        return (__int64)g_pGetTickCount64();
    } else {
        return (__int64)GetTickCount();
    }
}

// get_imdisk_unit函数（完全按照MountImg.c第175-184行）
static long get_imdisk_unit(void)
{
    long i, j;

    if (ImDisk_GetDeviceListEx && !((BOOL(*)(ULONG, PULONG))ImDisk_GetDeviceListEx)(_countof(list_device), list_device)) {
        return -1;
    }
    
    if (!ImDisk_GetDeviceListEx) {
        static long next_device = 0;
        return next_device++;
    }
    
    i = j = 0;
    while (++j <= list_device[0])
        if (list_device[j] == i) { j = 0; i++; }
    
    return i;
}

// start_process函数（完全按照MountImg.c第135-163行）
static DWORD start_process(WCHAR *cmd, BYTE flag)
{
    STARTUPINFOW si = {sizeof(si)};
    PROCESS_INFORMATION pi;
    TOKEN_LINKED_TOKEN lt = { 0 };
    HANDLE token;
    DWORD dw;
    BOOL result;
    
    // 调试输出
    char debug_cmd[MAX_PATH * 2 + 100];
    WideCharToMultiByte(CP_UTF8, 0, cmd, -1, debug_cmd, sizeof(debug_cmd), NULL, NULL);
    OutputDebugStringA("🔧 Executing: ");
    OutputDebugStringA(debug_cmd);
    OutputDebugStringA("\n");
    
    if (flag == 2 && (dw = WTSGetActiveConsoleSessionId()) != -1 && WTSQueryUserToken(dw, &token)) {
        if (!GetTokenInformation(token, TokenLinkedToken, &lt, sizeof lt, &dw) ||
            !(result = CreateProcessAsUserW(lt.LinkedToken, NULL, cmd, NULL, NULL, FALSE, CREATE_NO_WINDOW, NULL, NULL, &si, &pi)))
            result = CreateProcessAsUserW(token, NULL, cmd, NULL, NULL, FALSE, CREATE_NO_WINDOW, NULL, NULL, &si, &pi);
        CloseHandle(token);
        CloseHandle(lt.LinkedToken);
    } else
        result = CreateProcessW(NULL, cmd, NULL, NULL, FALSE, CREATE_NO_WINDOW, NULL, NULL, &si, &pi);
    
    dw = 1;
    if (result) {
        if (flag) {
            WaitForSingleObject(pi.hProcess, INFINITE);
            GetExitCodeProcess(pi.hProcess, &dw);
            
            char debug_exit[64];
            sprintf(debug_exit, "   Exit code: %lu\n", dw);
            OutputDebugStringA(debug_exit);
        }
        CloseHandle(pi.hProcess);
        CloseHandle(pi.hThread);
    } else {
        DWORD error = GetLastError();
        char debug_error[128];
        sprintf(debug_error, "   ❌ Process creation failed, error=%lu\n", error);
        OutputDebugStringA(debug_error);
        return error;
    }
    
    return (dw != 0) ? dw : 0;
}

// Imdisk_Mount函数（完全按照MountImg.c第603-625行）
static int Imdisk_Mount(BYTE no_check_fs)
{
    WCHAR cmdline[MAX_PATH * 2 + 80], txt_partition[16];
    BOOL fs_ok = FALSE;
    BYTE retry = !partition;

    OutputDebugStringA("\n🎯 ═══ Imdisk_Mount (MountImg.c Logic) ═══\n");
    
    _snwprintf(txt_partition, _countof(txt_partition), partition > 1 ? L" -v %d" : L"", partition);
    
    do {
        OutputDebugStringA("📋 Phase 1: Verification Mount\n");
        
        if ((device_number = get_imdisk_unit()) < 0) {
            OutputDebugStringA("   ❌ Cannot get device unit\n");
            return 1;
        }
        
        char debug_device[64];
        sprintf(debug_device, "   Device number: %ld\n", device_number);
        OutputDebugStringA(debug_device);
        
        _snwprintf(cmdline, _countof(cmdline), L"imdisk -a -u %d -o %cd,ro,%s -f \"%s\"%s%s", 
                  device_number, dev_list[dev_type], rm_list[removable], filename, 
                  retry ? L"" : L" -b auto", txt_partition);
        
        if (start_process(cmdline, TRUE)) {
            OutputDebugStringA("   ❌ Verification mount failed\n");
            return 1;
        }
        
        OutputDebugStringA("   ✅ Verification mount succeeded\n");
        
        _snwprintf(cmdline, _countof(cmdline), L"\\\\?\\ImDisk%d\\", device_number);
        fs_ok = GetVolumeInformation(cmdline, NULL, 0, NULL, NULL, NULL, NULL, 0);
        
        if (fs_ok) {
            OutputDebugStringA("   ✅ File system verification: SUCCESS\n");
        } else {
            DWORD error = GetLastError();
            char debug_fs[128];
            sprintf(debug_fs, "   ⚠️  File system verification: FAILED (error=%lu)\n", error);
            OutputDebugStringA(debug_fs);
        }
        
        _snwprintf(cmdline, _countof(cmdline), L"imdisk -D -u %d", device_number);
        start_process(cmdline, TRUE);
        OutputDebugStringA("   🧹 Verification device cleaned up\n");
        
    } while (!fs_ok && ++retry < 2);
    
    if (fs_ok || no_check_fs) {
        OutputDebugStringA("📋 Phase 2: Final Mount\n");
        
        if ((device_number = get_imdisk_unit()) < 0) {
            OutputDebugStringA("   ❌ Cannot get device unit for final mount\n");
            return 1;
        }
        
        char debug_final[64];
        sprintf(debug_final, "   Final device number: %ld\n", device_number);
        OutputDebugStringA(debug_final);
        
        _snwprintf(cmdline, _countof(cmdline), L"imdisk -a -u %d -m \"%s\" -o %cd,r%c,%s -f \"%s\"%s%s%s",
                 device_number, drive, dev_list[dev_type], ro_list[readonly], rm_list[removable], 
                 filename, retry ? L"" : L" -b auto", txt_partition, boot_list[win_boot]);
        
        DWORD result = start_process(cmdline, TRUE);
        
        if (result == 0) {
            OutputDebugStringA("🎉 ═══ IMDISK MOUNT SUCCESS ═══\n");
        } else {
            OutputDebugStringA("❌ ═══ IMDISK MOUNT FAILED ═══\n");
        }
        
        return result;
    } else {
        OutputDebugStringA("❌ ═══ MOUNT FAILED: File system verification failed ═══\n");
        return 1;
    }
}

// DiscUtils_Mount函数（完全按照MountImg.c第627-647行）
static int DiscUtils_Mount(void)
{
    WCHAR cmdline1[MAX_PATH + 70], cmdline2[MAX_PATH + 70], txt_partition[24];
    WCHAR *cmdline_ptr[3] = {cmdline1, cmdline2, drive};
    HANDLE h;
    int error;
    __int64 pipe;

    OutputDebugStringA("\n🔄 ═══ DiscUtils_Mount (MountImg.c Logic) ═══\n");

    pipe = (GetTickCount_Compatible() << 16) | (GetCurrentProcessId() & 0xFFFF);
    _snwprintf(txt_partition, _countof(txt_partition), partition != 1 ? L" /partition=%d" : L"", partition);
    _snwprintf(cmdline1, _countof(cmdline1), L"/name=ImDisk%I64x%s /filename=\"%s\"%s",
              pipe, txt_partition, filename, ro_discutils_list[readonly]);
    _snwprintf(cmdline2, _countof(cmdline2), L"-o shm,%cd,r%c,%s -f ImDisk%I64x",
              dev_list[dev_type], ro_list[readonly], rm_list[removable], pipe);

    char debug_cmd1[MAX_PATH + 100], debug_cmd2[MAX_PATH + 100];
    WideCharToMultiByte(CP_UTF8, 0, cmdline1, -1, debug_cmd1, sizeof(debug_cmd1), NULL, NULL);
    WideCharToMultiByte(CP_UTF8, 0, cmdline2, -1, debug_cmd2, sizeof(debug_cmd2), NULL, NULL);
    OutputDebugStringA("🔧 DiscUtils cmdline1: ");
    OutputDebugStringA(debug_cmd1);
    OutputDebugStringA("\n");
    OutputDebugStringA("🔧 DiscUtils cmdline2: ");
    OutputDebugStringA(debug_cmd2);
    OutputDebugStringA("\n");

    h = CreateSemaphoreA(NULL, 0, 2, "Global\\MountImgSvcSema");
    if (!h) {
        DWORD semError = GetLastError();
        char debug_sem[128];
        sprintf(debug_sem, "❌ CreateSemaphore failed, error=%lu\n", semError);
        OutputDebugStringA(debug_sem);
        return 1;
    }

    if (!h_svc) {
        OutputDebugStringA("❌ Service handle not available\n");
        CloseHandle(h);
        return 1;
    }

    if (!StartService(h_svc, 3, (LPCWSTR*)cmdline_ptr)) {
        DWORD svcError = GetLastError();
        char debug_svc[128];
        sprintf(debug_svc, "❌ StartService failed, error=%lu\n", svcError);
        OutputDebugStringA(debug_svc);
        CloseHandle(h);
        return 1;
    }

    OutputDebugStringA("⏳ Waiting for DiscUtils service...\n");
    error = WaitForSingleObject(h, 15000) != WAIT_OBJECT_0 || WaitForSingleObject(h, 0) == WAIT_OBJECT_0;
    CloseHandle(h);

    if (!error && ImDisk_NotifyShellDriveLetter) {
        ((void(*)(HANDLE, LPCWSTR))ImDisk_NotifyShellDriveLetter)(NULL, drive);
        OutputDebugStringA("🔔 Shell notification sent\n");
    }

    if (error) {
        OutputDebugStringA("❌ ═══ DISCUTILS MOUNT FAILED ═══\n");
    } else {
        OutputDebugStringA("🎉 ═══ DISCUTILS MOUNT SUCCESS ═══\n");
    }

    return error;
}

// 主要的Mount函数（完全按照MountImg.c第649-709行）
static int Mount_Internal(const WCHAR* imagePath, const WCHAR* driveLetter, int isReadonly, int partitionNum)
{
    int error;
    WCHAR cmdline[MAX_PATH + 16];
    int i;

    OutputDebugStringA("\n🚀 ═══════════════════════════════════════\n");
    OutputDebugStringA("    VirtualDiskLib Mount (MountImg Logic)\n");
    OutputDebugStringA("═══════════════════════════════════════\n");

    // 设置全局变量（完全按照MountImg.c）
    wcscpy(filename, imagePath);
    wcscpy(drive, driveLetter);
    readonly = isReadonly ? TRUE : FALSE;
    partition = partitionNum;

    char debug_info[512];
    WideCharToMultiByte(CP_UTF8, 0, imagePath, -1, debug_info, sizeof(debug_info), NULL, NULL);
    OutputDebugStringA("📁 Image file: ");
    OutputDebugStringA(debug_info);
    OutputDebugStringA("\n");

    WideCharToMultiByte(CP_UTF8, 0, driveLetter, -1, debug_info, sizeof(debug_info), NULL, NULL);
    OutputDebugStringA("💿 Drive letter: ");
    OutputDebugStringA(debug_info);
    OutputDebugStringA("\n");

    sprintf(debug_info, "🔒 Read-only: %s\n", readonly ? "YES" : "NO");
    OutputDebugStringA(debug_info);

    sprintf(debug_info, "📊 Partition: %d\n", partition);
    OutputDebugStringA(debug_info);

    // 完全按照MountImg.c第659行的逻辑
    BOOL new_file = FALSE;  // 简化处理，假设文件已存在
    error = Imdisk_Mount(new_file || !net_installed);

    if (error && !new_file && net_installed) {
        OutputDebugStringA("\n🔄 ImDisk failed, trying DiscUtils...\n");
        device_number = -1;
        error = DiscUtils_Mount();
    }

    if (error) {
        OutputDebugStringA("\n💥 ═══ MOUNT FAILED ═══\n");
        return error;
    }

    OutputDebugStringA("\n✅ Mount successful, verifying drive access...\n");

    // 验证挂载结果（完全按照MountImg.c第684-703行）
    i = 0;
    do {
        if (GetVolumeInformation(drive, NULL, 0, NULL, NULL, NULL, NULL, 0)) {
            OutputDebugStringA("🎉 ╔═══════════════════════════════════════╗\n");
            OutputDebugStringA("   ║        MOUNT SUCCESS!                 ║\n");
            OutputDebugStringA("   ╚═══════════════════════════════════════╝\n");
            return 0;
        } else if (GetLastError() == ERROR_UNRECOGNIZED_VOLUME) {
            OutputDebugStringA("⚠️  Unrecognized volume detected\n");
            return 1;
        }
        Sleep(100);
    } while (++i < 100);

    OutputDebugStringA("⏰ Timeout waiting for drive to become accessible\n");
    return 1;
}

// ===== 导出的API函数 =====

// 初始化函数
BOOL InitializeVirtualDiskLib(void)
{
    OutputDebugStringA("🔧 Initializing VirtualDiskLib...\n");

    // 加载ImDisk API
    HMODULE hImDisk = LoadLibraryW(L"imdisk.cpl");
    if (hImDisk) {
        ImDisk_GetDeviceListEx = GetProcAddress(hImDisk, "ImDiskGetDeviceListEx");
        ImDisk_ForceRemoveDevice = GetProcAddress(hImDisk, "ImDiskForceRemoveDevice");
        ImDisk_NotifyShellDriveLetter = GetProcAddress(hImDisk, "ImDiskNotifyShellDriveLetter");

        if (ImDisk_GetDeviceListEx && ImDisk_ForceRemoveDevice && ImDisk_NotifyShellDriveLetter) {
            OutputDebugStringA("✅ ImDisk API loaded successfully\n");
        } else {
            OutputDebugStringA("⚠️  Some ImDisk APIs not available\n");
        }
    } else {
        OutputDebugStringA("⚠️  ImDisk library not found\n");
    }

    // 检查DiscUtils服务
    SC_HANDLE h_scman = OpenSCManager(NULL, NULL, SC_MANAGER_CONNECT);
    if (h_scman) {
        h_svc = OpenService(h_scman, L"ImDiskSvc", SERVICE_START);
        if (h_svc) {
            net_installed = TRUE;
            OutputDebugStringA("✅ DiscUtils service available\n");
        } else {
            OutputDebugStringA("⚠️  DiscUtils service not available\n");
        }
        CloseServiceHandle(h_scman);
    }

    OutputDebugStringA("🎯 VirtualDiskLib initialized\n");
    return TRUE;
}

// 挂载虚拟磁盘
MOUNT_RESULT MountVirtualDisk_Core(const char* imagePath, const char* driveLetter, int readonly, int partition)
{
    if (!imagePath || !driveLetter) {
        OutputDebugStringA("❌ Invalid parameters\n");
        return MOUNT_ERROR_INVALID_PARAMS;
    }

    // 转换为宽字符
    WCHAR wImagePath[MAX_PATH];
    WCHAR wDriveLetter[8];

    MultiByteToWideChar(CP_UTF8, 0, imagePath, -1, wImagePath, _countof(wImagePath));
    MultiByteToWideChar(CP_UTF8, 0, driveLetter, -1, wDriveLetter, _countof(wDriveLetter));

    // 检查文件是否存在
    if (GetFileAttributesW(wImagePath) == INVALID_FILE_ATTRIBUTES) {
        OutputDebugStringA("❌ Image file not found\n");
        return MOUNT_ERROR_FILE_NOT_FOUND;
    }

    // 执行挂载
    int result = Mount_Internal(wImagePath, wDriveLetter, readonly, partition);

    if (result == 0) {
        return MOUNT_SUCCESS;
    } else {
        return MOUNT_ERROR_MOUNT_FAILED;
    }
}

// 卸载虚拟磁盘
MOUNT_RESULT UnmountVirtualDisk_Core(const char* driveLetter)
{
    if (!driveLetter) {
        return MOUNT_ERROR_INVALID_PARAMS;
    }

    WCHAR wDriveLetter[8];
    MultiByteToWideChar(CP_UTF8, 0, driveLetter, -1, wDriveLetter, _countof(wDriveLetter));

    WCHAR cmdline[MAX_PATH];
    _snwprintf(cmdline, _countof(cmdline), L"imdisk -d -m \"%s\"", wDriveLetter);

    OutputDebugStringA("🗑️  Unmounting drive: ");
    OutputDebugStringA(driveLetter);
    OutputDebugStringA("\n");

    DWORD result = start_process(cmdline, 1);

    if (result == 0) {
        OutputDebugStringA("✅ Unmount successful\n");
        return MOUNT_SUCCESS;
    } else {
        OutputDebugStringA("❌ Unmount failed\n");
        return MOUNT_ERROR_UNMOUNT_FAILED;
    }
}

// 清理函数
void CleanupVirtualDiskLib(void)
{
    OutputDebugStringA("🧹 Cleaning up VirtualDiskLib...\n");

    if (h_svc) {
        CloseServiceHandle(h_svc);
        h_svc = NULL;
    }

    OutputDebugStringA("✅ VirtualDiskLib cleanup complete\n");
}

// ===== 兼容性函数（为VirtualDiskLib.cpp提供） =====

// 初始化挂载核心模块
int InitMountCore(void)
{
    OutputDebugStringA("🔧 InitMountCore called\n");
    return InitializeVirtualDiskLib() ? 0 : 1;
}

// 清理挂载核心模块
void CleanupMountCore(void)
{
    OutputDebugStringA("🧹 CleanupMountCore called\n");
    CleanupVirtualDiskLib();
}

// 获取磁盘信息
int GetDiskInfo(const char* filePath, DiskInfo* diskInfo)
{
    if (!filePath || !diskInfo) {
        return 1;
    }

    OutputDebugStringA("📊 GetDiskInfo called\n");

    // 清零结构体
    memset(diskInfo, 0, sizeof(DiskInfo));

    // 检查文件是否存在
    DWORD fileAttrib = GetFileAttributesA(filePath);
    if (fileAttrib == INVALID_FILE_ATTRIBUTES) {
        OutputDebugStringA("❌ File not found\n");
        return 1;
    }

    // 获取文件大小
    HANDLE hFile = CreateFileA(filePath, GENERIC_READ, FILE_SHARE_READ, NULL, OPEN_EXISTING, 0, NULL);
    if (hFile != INVALID_HANDLE_VALUE) {
        LARGE_INTEGER fileSize;
        if (GetFileSizeEx(hFile, &fileSize)) {
            diskInfo->size_bytes = fileSize.QuadPart;
            diskInfo->size_mb = (int)(fileSize.QuadPart / (1024 * 1024));
        }
        CloseHandle(hFile);
    }

    // 复制文件路径
    strncpy(diskInfo->file_path, filePath, sizeof(diskInfo->file_path) - 1);
    diskInfo->file_path[sizeof(diskInfo->file_path) - 1] = '\0';

    // 检测文件格式
    const char* ext = strrchr(filePath, '.');
    if (ext) {
        if (_stricmp(ext, ".vhd") == 0) {
            strcpy(diskInfo->format_type, "VHD");
        } else if (_stricmp(ext, ".vhdx") == 0) {
            strcpy(diskInfo->format_type, "VHDX");
        } else if (_stricmp(ext, ".vmdk") == 0) {
            strcpy(diskInfo->format_type, "VMDK");
        } else if (_stricmp(ext, ".iso") == 0) {
            strcpy(diskInfo->format_type, "ISO");
        } else if (_stricmp(ext, ".img") == 0) {
            strcpy(diskInfo->format_type, "IMG");
        } else {
            strcpy(diskInfo->format_type, "Unknown");
        }
    } else {
        strcpy(diskInfo->format_type, "Unknown");
    }

    // 默认值
    diskInfo->partition_count = 1;
    diskInfo->is_readonly = 0;
    strcpy(diskInfo->file_system, "Unknown");

    char debug_info[256];
    sprintf(debug_info, "✅ File: %s, Size: %lld bytes (%d MB), Format: %s\n",
            filePath, diskInfo->size_bytes, diskInfo->size_mb, diskInfo->format_type);
    OutputDebugStringA(debug_info);

    return 0;
}
