@echo off
rem ========================================
rem 磁盘挂载工具自动解压运行脚本
rem 兼容Windows XP及以上系统
rem ========================================

setlocal enabledelayedexpansion

echo 正在启动磁盘挂载工具...

rem 获取当前脚本所在目录
set "SCRIPT_DIR=%~dp0"

rem 设置ZIP文件路径
set "ZIP_FILE=%SCRIPT_DIR%files.zip"

rem 设置解压目标目录
set "EXTRACT_DIR=%TEMP%\DiskUp"

rem 设置config.exe路径
set "CONFIG_EXE=%EXTRACT_DIR%\config.exe"

rem 检查ZIP文件是否存在
if not exist "%ZIP_FILE%" (
    echo 错误: 找不到files.zip文件
    echo 文件路径: %ZIP_FILE%
    pause
    exit /b 1
)

echo 找到ZIP文件: %ZIP_FILE%

rem 清理旧的解压目录
if exist "%EXTRACT_DIR%" (
    echo 清理旧的临时文件...
    rmdir /s /q "%EXTRACT_DIR%" 2>nul
)

rem 创建解压目录
mkdir "%EXTRACT_DIR%" 2>nul
if not exist "%EXTRACT_DIR%" (
    echo 错误: 无法创建临时目录 %EXTRACT_DIR%
    pause
    exit /b 1
)

echo 临时目录: %EXTRACT_DIR%

rem 检测系统版本和可用的解压工具
echo 正在检测系统解压工具...

rem 方法1: 尝试使用PowerShell (Windows 7及以上)
powershell -Command "Get-Host" >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo 使用PowerShell解压...
    powershell -Command "try { Add-Type -AssemblyName System.IO.Compression.FileSystem; [System.IO.Compression.ZipFile]::ExtractToDirectory('%ZIP_FILE%', '%EXTRACT_DIR%'); Write-Host '解压成功'; exit 0 } catch { Write-Host '解压失败:' $_.Exception.Message; exit 1 }"
    
    if !ERRORLEVEL! EQU 0 (
        echo PowerShell解压成功
        goto :check_config
    ) else (
        echo PowerShell解压失败，尝试其他方法...
    )
)

rem 方法2: 尝试使用VBScript (兼容XP)
echo 使用VBScript解压...

rem 创建VBScript解压脚本
echo Set objShell = CreateObject("Shell.Application") > "%TEMP%\unzip.vbs"
echo Set objFolder = objShell.NameSpace("%EXTRACT_DIR%") >> "%TEMP%\unzip.vbs"
echo Set objZip = objShell.NameSpace("%ZIP_FILE%") >> "%TEMP%\unzip.vbs"
echo If Not objZip Is Nothing Then >> "%TEMP%\unzip.vbs"
echo     objFolder.CopyHere objZip.Items, 20 >> "%TEMP%\unzip.vbs"
echo     WScript.Echo "VBScript解压完成" >> "%TEMP%\unzip.vbs"
echo Else >> "%TEMP%\unzip.vbs"
echo     WScript.Echo "无法打开ZIP文件" >> "%TEMP%\unzip.vbs"
echo     WScript.Quit 1 >> "%TEMP%\unzip.vbs"
echo End If >> "%TEMP%\unzip.vbs"

rem 执行VBScript
cscript //nologo "%TEMP%\unzip.vbs"
set VBS_RESULT=%ERRORLEVEL%

rem 清理VBScript文件
del "%TEMP%\unzip.vbs" 2>nul

if %VBS_RESULT% EQU 0 (
    echo VBScript解压成功
    goto :check_config
) else (
    echo VBScript解压失败
)

rem 方法3: 尝试使用expand命令 (如果ZIP文件兼容)
echo 尝试使用expand命令...
expand "%ZIP_FILE%" -F:* "%EXTRACT_DIR%" >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo expand命令解压成功
    goto :check_config
) else (
    echo expand命令解压失败
)

rem 所有解压方法都失败
echo.
echo 错误: 所有解压方法都失败了
echo 请确保:
echo 1. files.zip文件完整且未损坏
echo 2. 系统支持ZIP文件解压
echo 3. 有足够的磁盘空间
echo.
pause
exit /b 1

:check_config
rem 检查解压结果
echo 正在验证解压结果...

rem 等待文件系统同步
timeout /t 2 /nobreak >nul 2>&1

rem 检查config.exe是否存在
if exist "%CONFIG_EXE%" (
    echo 找到config.exe: %CONFIG_EXE%
) else (
    rem 可能解压到了子目录，尝试查找
    echo 在子目录中查找config.exe...
    for /r "%EXTRACT_DIR%" %%f in (config.exe) do (
        if exist "%%f" (
            set "CONFIG_EXE=%%f"
            echo 找到config.exe: %%f
            goto :run_config
        )
    )
    
    echo 错误: 解压后找不到config.exe
    echo 解压目录内容:
    dir "%EXTRACT_DIR%" /b
    pause
    exit /b 1
)

:run_config
rem 运行config.exe
echo.
echo 正在启动配置程序...
echo 执行: "%CONFIG_EXE%"

rem 切换到config.exe所在目录
pushd "%~dp1"
where "!CONFIG_EXE!" >nul 2>&1
for %%i in ("!CONFIG_EXE!") do set "CONFIG_DIR=%%~dpi"
pushd "!CONFIG_DIR!"

rem 执行config.exe并传递所有参数
"%CONFIG_EXE%" %*
set RUN_RESULT=%ERRORLEVEL%

rem 恢复原目录
popd

rem 检查执行结果
if %RUN_RESULT% EQU 0 (
    echo 配置程序执行完成
) else (
    echo 配置程序执行返回代码: %RUN_RESULT%
)

rem 询问是否清理临时文件
echo.
set /p CLEANUP="是否清理临时文件? (Y/N): "
if /i "%CLEANUP%"=="Y" (
    echo 正在清理临时文件...
    rmdir /s /q "%EXTRACT_DIR%" 2>nul
    if not exist "%EXTRACT_DIR%" (
        echo 临时文件清理完成
    ) else (
        echo 警告: 部分临时文件可能未能清理
    )
) else (
    echo 临时文件保留在: %EXTRACT_DIR%
)

echo.
echo 脚本执行完成
exit /b %RUN_RESULT%
