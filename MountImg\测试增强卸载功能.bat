@echo off
echo.
echo ===============================================================
echo                    测试增强卸载功能
echo ===============================================================
echo.

cd /d "%~dp0"

echo 📋 测试基于MountImg.c UnmountDrive实现的增强卸载功能
echo    - Method 1: ImDisk-Dlg RM (完全兼容MountImg.c)
echo    - Method 2: imdisk -d (简化命令行)
echo    - Method 3: 直接start_process调用
echo.

echo 🔍 检查当前挂载的驱动器...
echo.

REM 检查各个驱动器是否存在
for %%d in (X Y Z W V) do (
    if exist %%d:\ (
        echo ✅ 发现驱动器 %%d: - 尝试卸载
        Debug\VirtualDiskTool32.exe unmount --drive %%d:
        echo.
    ) else (
        echo ⚪ 驱动器 %%d: 不存在
    )
)

echo.
echo 🧪 测试无效驱动器卸载（应该失败）...
echo.

REM 测试无效驱动器
echo === 测试无效驱动器 Q: ===
Debug\VirtualDiskTool32.exe unmount --drive Q:
echo.

echo === 测试无效驱动器 T: ===
Debug\VirtualDiskTool32.exe unmount --drive T:
echo.

echo ✅ 增强卸载功能测试完成！
echo.
echo 💡 查看调试信息：
echo    - 使用DebugView工具查看详细的卸载过程
echo    - 检查三种卸载方法的执行顺序和结果
echo    - 观察错误诊断和故障排除建议
echo.

pause
