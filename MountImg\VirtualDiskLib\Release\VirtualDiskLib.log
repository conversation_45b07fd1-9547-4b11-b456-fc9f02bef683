﻿  MountImg.c
..\MountImg.c : warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
..\MountImg.c(2): warning C4005: 'OEMRESOURCE' : macro redefinition
          command-line arguments :  see previous definition of 'OEMRESOURCE'
..\MountImg.c(114): warning C4133: 'initializing' : incompatible types - from 'char [1]' to 'WCHAR *'
..\MountImg.c(130): warning C4244: 'function' : conversion from 'LONGLONG' to 'SIZE_T', possible loss of data
..\MountImg.c(135): warning C4020: 'wcstok' : too many actual parameters
..\MountImg.c(137): warning C4020: 'wcstok' : too many actual parameters
..\MountImg.c(139): warning C4018: '<' : signed/unsigned mismatch
..\MountImg.c(207): warning C4018: '<=' : signed/unsigned mismatch
..\MountImg.c(320): warning C4244: '=' : conversion from '__int64' to 'double', possible loss of data
..\MountImg.c(854): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
..\MountImg.c(1625): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  VirtualDiskLib.cpp
VirtualDiskLib.cpp(13): warning C4005: '_WIN32_WINNT' : macro redefinition
          command-line arguments :  see previous definition of '_WIN32_WINNT'
VirtualDiskLib.cpp(14): warning C4005: 'OEMRESOURCE' : macro redefinition
          command-line arguments :  see previous definition of 'OEMRESOURCE'
VirtualDiskLib.cpp(15): warning C4005: '_CRT_SECURE_NO_WARNINGS' : macro redefinition
          command-line arguments :  see previous definition of '_CRT_SECURE_NO_WARNINGS'
VirtualDiskLib.cpp(652): warning C4566: character represented by universal-character-name '\u274C' cannot be represented in the current code page (936)
VirtualDiskLib.cpp(662): warning C4566: character represented by universal-character-name '\u274C' cannot be represented in the current code page (936)
VirtualDiskLib.cpp(667): warning C4566: character represented by universal-character-name '\u2705' cannot be represented in the current code page (936)
VirtualDiskLib.cpp(677): warning C4566: character represented by universal-character-name '\u274C' cannot be represented in the current code page (936)
VirtualDiskLib.cpp(683): warning C4566: character represented by universal-character-name '\u2705' cannot be represented in the current code page (936)
VirtualDiskLib.cpp(691): warning C4566: character represented by universal-character-name '\u274C' cannot be represented in the current code page (936)
VirtualDiskLib.cpp(697): warning C4566: character represented by universal-character-name '\u2705' cannot be represented in the current code page (936)
VirtualDiskLib.cpp(706): warning C4566: character represented by universal-character-name '\u2705' cannot be represented in the current code page (936)
VirtualDiskLib.cpp(712): warning C4566: character represented by universal-character-name '\u2705' cannot be represented in the current code page (936)
VirtualDiskLib.cpp(717): warning C4566: character represented by universal-character-name '\u26A0' cannot be represented in the current code page (936)
VirtualDiskLib.cpp(717): warning C4566: character represented by universal-character-name '\uFE0F' cannot be represented in the current code page (936)
VirtualDiskLib.cpp(719): warning C4566: character represented by universal-character-name '\u2705' cannot be represented in the current code page (936)
VirtualDiskLib.cpp(725): warning C4566: character represented by universal-character-name '\u2705' cannot be represented in the current code page (936)
VirtualDiskLib.cpp(737): warning C4566: character represented by universal-character-name '\u274C' cannot be represented in the current code page (936)
VirtualDiskLib.cpp(745): warning C4566: character represented by universal-character-name '\u2705' cannot be represented in the current code page (936)
VirtualDiskLib.cpp(752): warning C4566: character represented by universal-character-name '\u274C' cannot be represented in the current code page (936)
VirtualDiskLib.cpp(757): warning C4566: character represented by universal-character-name '\u2705' cannot be represented in the current code page (936)
VirtualDiskLib.cpp(761): warning C4566: character represented by universal-character-name '\u2705' cannot be represented in the current code page (936)
VirtualDiskLib.cpp(762): warning C4566: character represented by universal-character-name '\u2705' cannot be represented in the current code page (936)
VirtualDiskLib.cpp(1103): warning C4566: character represented by universal-character-name '\u2705' cannot be represented in the current code page (936)
VirtualDiskLib.cpp(1197): warning C4566: character represented by universal-character-name '\u2705' cannot be represented in the current code page (936)
VirtualDiskLib.cpp(1201): warning C4566: character represented by universal-character-name '\u274C' cannot be represented in the current code page (936)
  json_helper.cpp
json_helper.cpp(6): warning C4005: '_CRT_SECURE_NO_WARNINGS' : macro redefinition
          command-line arguments :  see previous definition of '_CRT_SECURE_NO_WARNINGS'
     Creating library E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\Release\VirtualDiskLib32.lib and object E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\Release\VirtualDiskLib32.exp
  Generating code
  Finished generating code
  VirtualDiskLib.vcxproj -> E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\Release\VirtualDiskLib32.dll
