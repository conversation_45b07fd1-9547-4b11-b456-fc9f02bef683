@echo off
chcp 65001 >nul
echo ========================================
echo Testing Formal JSON Parser Implementation
echo ========================================

echo.
echo 这个测试验证正式的 JSON 解析器功能
echo.
echo 正式 JSON 解析器特点:
echo 1. 使用结构化的 JSON_MOUNT_PARAMS 结构体
echo 2. 实现专用的 parseJsonValue Lambda 函数
echo 3. 支持字符串、布尔值、数字类型的正确解析
echo 4. 提供详细的解析状态反馈
echo 5. 包含解析结果验证和汇总
echo.

echo 启动 MountImg.exe 进行正式 JSON 解析测试...
echo ----------------------------------------

echo 2 | MountImg.exe

echo.
echo ----------------------------------------
echo 程序执行完毕，退出码: %ERRORLEVEL%
echo.

echo 检查挂载结果...
if exist X:\ (
    echo ✅ SUCCESS: X: 驱动器已挂载
    echo 目录列表:
    dir X: /w
) else (
    echo ❌ FAILED: X: 驱动器未挂载
)

echo.
echo ========================================
echo 正式 JSON 解析器技术说明:
echo ========================================
echo.
echo 1. JSON_MOUNT_PARAMS 结构体:
echo    typedef struct {
echo        WCHAR file_path[MAX_PATH];
echo        WCHAR drive[8];
echo        BOOL readonly;
echo        int partition;
echo    } JSON_MOUNT_PARAMS;
echo.
echo 2. parseJsonValue Lambda 函数特性:
echo    - 支持键名查找: "key": value
echo    - 字符串解析: "value" (带引号)
echo    - 布尔值解析: true/false
echo    - 数字解析: 123
echo    - 类型安全的参数传递
echo.
echo 3. 解析流程:
echo    Step 1: 查找键名 "key"
echo    Step 2: 定位冒号 :
echo    Step 3: 跳过空白字符
echo    Step 4: 根据首字符判断类型
echo    Step 5: 提取并转换值
echo.
echo 4. 支持的 JSON 格式:
echo    {
echo      "file_path": "E:\\004_VMDK\\666666.vmdk",
echo      "drive": "X:",
echo      "readonly": false,
echo      "partition": 1
echo    }
echo.
echo 5. 错误处理:
echo    - 键名不存在检测
echo    - 格式错误检测
echo    - 类型不匹配检测
echo    - 缓冲区溢出保护
echo.

pause
