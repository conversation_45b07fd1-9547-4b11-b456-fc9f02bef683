# MountImg - Visual Studio 2019 项目

## 项目概述

MountImg是ImDisk Toolkit的镜像文件挂载工具，支持挂载各种镜像文件格式（ISO、IMG、VHD等）。

## 项目文件结构

```
MountImg/
├── MountImg.sln              # Visual Studio 2019 解决方案文件
├── MountImg.vcxproj          # 项目文件
├── MountImg.vcxproj.filters  # 项目过滤器（文件分组）
├── MountImg.vcxproj.user     # 用户配置文件
├── MountImg.c                # 主源代码文件
├── resource.h                # 资源头文件
├── resource.rc               # 资源文件
├── manifest                  # 应用程序清单
├── lang.txt                  # 语言配置文件
├── comp32.bat               # 原始32位编译脚本
├── comp64.bat               # 原始64位编译脚本
└── README_VS2019.md         # 本说明文件
```

## 编译配置

### 支持的平台和配置
- **Win32 Debug**: 32位调试版本
- **Win32 Release**: 32位发布版本
- **x64 Debug**: 64位调试版本
- **x64 Release**: 64位发布版本

### 编译器设置
- **平台工具集**: v141_xp (支持Windows XP)
- **Windows SDK**: 7.0 (XP兼容)
- **字符集**: Unicode
- **C++标准**: C++14
- **运行时库**: 静态链接 (MT/MTd)

### 预处理器定义
- `_WIN32_WINNT=0x0501` (Windows XP兼容)
- `WINVER=0x0501` (Windows XP版本)
- `OEMRESOURCE` (OEM资源访问)
- `WIN32` (32位版本)
- `_WINDOWS` (Windows应用程序)

### 链接库
- kernel32.lib, user32.lib, gdi32.lib
- shell32.lib, shlwapi.lib, wtsapi32.lib
- comctl32.lib, advapi32.lib
- ole32.lib, oleaut32.lib, uuid.lib

### 包含目录
- `..\inc` (ImDisk头文件目录)

## 生成的文件

### Debug版本
- `Debug\MountImg32.exe` (32位调试版)
- `x64\Debug\MountImg64.exe` (64位调试版)

### Release版本
- `Release\MountImg32.exe` (32位发布版)
- `x64\Release\MountImg64.exe` (64位发布版)

## 使用说明

1. **打开项目**: 双击 `MountImg.sln` 在Visual Studio 2019中打开
2. **选择配置**: 在工具栏选择所需的配置（Debug/Release）和平台（Win32/x64）
3. **编译项目**: 按F7或选择"生成" -> "生成解决方案"
4. **运行调试**: 按F5开始调试，或Ctrl+F5开始执行（不调试）

## 依赖项

### 必需的头文件
- `imdisk.h` - ImDisk驱动接口
- `imdisktk.h` - ImDisk Toolkit定义
- `imdiskver.h` - 版本信息
- `build.h` - 构建信息（由编译脚本生成）

### 运行时依赖
- ImDisk虚拟磁盘驱动
- DiscUtils库（用于高级镜像格式支持）

## 特性

- 支持多种镜像文件格式
- 分区预览功能
- 图形用户界面
- 命令行参数支持
- Unicode字符集支持
- Windows XP SP3及以上版本兼容

## 注意事项

1. **编译前准备**: 确保 `..\inc\build.h` 文件存在（可运行 `comp_all.bat` 生成）
2. **资源文件**: 项目包含Windows资源文件，支持多语言界面
3. **清单文件**: 使用自定义清单文件以确保正确的权限和兼容性
4. **静态链接**: 使用静态运行时库，生成的可执行文件无需额外的运行时依赖

## 故障排除

### 编译错误
- 确保所有头文件路径正确
- 检查 `build.h` 文件是否存在
- 验证Windows SDK版本

### 链接错误
- 确保所有必需的库文件已包含
- 检查清单文件路径

### 运行时错误
- 确保ImDisk驱动已安装
- 检查DiscUtils库文件是否存在

---
*生成日期: 2025年7月11日*
*Visual Studio版本: 2019 (v142)*
