﻿/*
 * test_functions.cpp
 * VirtualDiskLib测试函数实现
 */

#define _CRT_SECURE_NO_WARNINGS
#include <windows.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <string>

#include "VirtualDiskLib.h"
#include "test_functions.h"
#include "cmdline_parser.h"
#include "json_builder.h"

// ========================================
// 回调函数测试实现
// ========================================

// 全局变量用于测试回调功能
static bool g_test_cancel_requested = false;
static bool g_test_pause_requested = false;
static int g_last_progress = 0;
static char g_current_task_id[256] = {0};
static int g_progress_call_count = 0;
static int g_control_call_count = 0;

/*
 * 测试用的进度回调函数
 */
void TestProgressCallback(const std::string& taskId, int progress, const std::string& matchResult)
{
    g_progress_call_count++;
    g_last_progress = progress;

    // 转换taskId为C字符串并保存
    strncpy_s(g_current_task_id, sizeof(g_current_task_id), taskId.c_str(), _TRUNCATE);

    // 显示进度信息
    printf("      📊 Progress [%s]: %d%% (Call #%d)",
           taskId.c_str(), progress, g_progress_call_count);

    // 如果有匹配结果，显示它
    if (!matchResult.empty()) {
        printf(" - Result: %s", matchResult.c_str());
    }
    printf("\n");

    // 模拟一些处理时间
    //Sleep(50);

    // 在特定进度点模拟用户交互
    if (progress == 50 && taskId.find("interactive") != std::string::npos) {
        printf("      ⏸️  Simulating pause request at 50%%...\n");
        g_test_pause_requested = true;
        Sleep(1000); // 模拟暂停1秒
        g_test_pause_requested = false;
        printf("      ▶️  Resuming operation...\n");
    }
}

/*
 * 测试用的任务控制回调函数
 */
bool TestQueryTaskControlCallback(const std::string& taskId, int controlType)
{
    g_control_call_count++;

    printf("      🎛️  Control Query [%s] Type:%d: (Call #%d) ",
           taskId.c_str(), controlType, g_control_call_count);

    // 检查取消请求
    if (g_test_cancel_requested) {
        printf("CANCEL requested\n");
        return true; // 返回true表示取消任务
    }

    // 检查暂停请求
    if (g_test_pause_requested) {
        printf("PAUSE requested\n");
        // 暂停逻辑可以在这里处理，但通常返回false继续执行
        return false;
    }

    // 模拟在特定任务中请求取消
    if (taskId.find("cancel_test") != std::string::npos && g_control_call_count >= 3) {
        printf("CANCEL (simulated)\n");
        g_test_cancel_requested = true;
        return true;
    }

    printf("CONTINUE\n");
    return false; // 返回false表示继续执行
}

/*
 * 重置测试状态
 */
void ResetTestCallbackState()
{
    g_test_cancel_requested = false;
    g_test_pause_requested = false;
    g_last_progress = 0;
    memset(g_current_task_id, 0, sizeof(g_current_task_id));
    g_progress_call_count = 0;
    g_control_call_count = 0;
}

/*
 * 打印回调测试统计信息
 */
void PrintCallbackTestStats()
{
    printf("      📈 Callback Statistics:\n");
    printf("         Progress calls: %d\n", g_progress_call_count);
    printf("         Control calls: %d\n", g_control_call_count);
    printf("         Last progress: %d%%\n", g_last_progress);
    printf("         Current task: %s\n", g_current_task_id[0] ? g_current_task_id : "None");
    printf("         Cancel requested: %s\n", g_test_cancel_requested ? "Yes" : "No");
}

// ========================================
// 回调功能专项测试函数
// ========================================

/*
 * 测试MountVirtualDisk的进度回调功能
 */
int TestMountWithProgressCallback(const char* testFile)
{
    printf("\n🧪 Testing MountVirtualDisk with Progress Callback:\n");

    if (!testFile) {
        PrintTestResult("Progress Callback Test", 0, "No test file specified");
        return 0;
    }

    // 重置测试状态
    ResetTestCallbackState();

    // 构建挂载参数
    char mountJson[1024];
    sprintf_s(mountJson, sizeof(mountJson),
        "{"
        "\"file_path\":\"%s\","
        "\"drive_letter\":\"M:\","
        "\"readonly\":false,"
        "\"partition_number\":1"
        "}",
        testFile);

    printf("   📁 Test file: %s\n", testFile);
    printf("   🎯 Target drive: M:\n");
    printf("   📊 Testing progress callback functionality...\n");

    // 执行挂载（使用进度回调）
    const char* mount_result = MountVirtualDisk(
        mountJson,                    // JSON参数
        TestProgressCallback,         // 进度回调函数
        "progress_test_task"          // 任务ID
        // 省略最后一个参数，使用默认值nullptr
    );

    // 检查结果
    std::string mount_result_str = mount_result ? mount_result : "";
    bool mount_success = mount_result_str.find("\"status\":\"success\"") != std::string::npos;

    // 打印统计信息
    PrintCallbackTestStats();

    if (mount_success) {
        PrintTestResult("Mount with Progress Callback", 1, "Mount succeeded with progress tracking");
        printf("      Response: %s\n", mount_result);

        // 验证进度回调是否被调用
        if (g_progress_call_count > 0) {
            PrintTestResult("Progress Callback Invocation", 1,
                           ("Progress callback called " + std::to_string(g_progress_call_count) + " times").c_str());
        } else {
            PrintTestResult("Progress Callback Invocation", 0, "Progress callback was not called");
        }

        return 1;
    } else {
        PrintTestResult("Mount with Progress Callback", 0, "Mount failed");
        printf("      Response: %s\n", mount_result);
        return 0;
    }
}

/*
 * 测试MountVirtualDisk的任务控制回调功能
 */
int TestMountWithTaskControl(const char* testFile)
{
    printf("\n🧪 Testing MountVirtualDisk with Task Control Callback:\n");

    if (!testFile) {
        PrintTestResult("Task Control Test", 0, "No test file specified");
        return 0;
    }

    // 重置测试状态
    ResetTestCallbackState();

    // 构建挂载参数
    char mountJson[1024];
    sprintf_s(mountJson, sizeof(mountJson),
        "{"
        "\"file_path\":\"%s\","
        "\"drive_letter\":\"N:\","
        "\"readonly\":true,"
        "\"partition_number\":1"
        "}",
        testFile);

    printf("   📁 Test file: %s\n", testFile);
    printf("   🎯 Target drive: N:\n");
    printf("   🎛️  Testing task control callback functionality...\n");

    // 执行挂载（使用任务控制回调）
    const char* mount_result = MountVirtualDisk(
        mountJson,                    // JSON参数
        TestProgressCallback,         // 进度回调函数
        "cancel_test_task",           // 任务ID（会触发取消测试）
        TestQueryTaskControlCallback  // 任务控制回调函数
    );

    // 检查结果
    std::string mount_result_str = mount_result ? mount_result : "";
    bool mount_cancelled = mount_result_str.find("\"status\":\"cancelled\"") != std::string::npos;
    bool mount_success = mount_result_str.find("\"status\":\"success\"") != std::string::npos;

    // 打印统计信息
    PrintCallbackTestStats();

    if (mount_cancelled) {
        PrintTestResult("Mount with Task Control", 1, "Task was successfully cancelled");
        printf("      Response: %s\n", mount_result);

        // 验证控制回调是否被调用
        if (g_control_call_count > 0) {
            PrintTestResult("Task Control Callback Invocation", 1,
                           ("Control callback called " + std::to_string(g_control_call_count) + " times").c_str());
        } else {
            PrintTestResult("Task Control Callback Invocation", 0, "Control callback was not called");
        }

        return 1;
    } else if (mount_success) {
        PrintTestResult("Mount with Task Control", 1, "Mount completed (cancellation not triggered)");
        printf("      Response: %s\n", mount_result);
        return 1;
    } else {
        PrintTestResult("Mount with Task Control", 0, "Mount failed");
        printf("      Response: %s\n", mount_result);
        return 0;
    }
}

/*
 * 测试UnmountVirtualDisk的回调功能
 */
int TestUnmountWithCallbacks(const char* driveLetter)
{
    printf("\n🧪 Testing UnmountVirtualDisk with Callbacks:\n");

    if (!driveLetter) {
        PrintTestResult("Unmount Callback Test", 0, "No drive letter specified");
        return 0;
    }

    // 重置测试状态
    ResetTestCallbackState();

    // 构建卸载参数
    char unmountJson[512];
    sprintf_s(unmountJson, sizeof(unmountJson),
        "{"
        "\"drive\":\"%s\","
        "\"force\":false"
        "}",
        driveLetter);

    printf("   🎯 Target drive: %s\n", driveLetter);
    printf("   📊 Testing unmount with progress and control callbacks...\n");

    // 执行卸载（使用两个回调）
    const char* unmount_result = UnmountVirtualDisk(
        unmountJson,                  // JSON参数
        TestProgressCallback,         // 进度回调函数
        "interactive_unmount_task",   // 任务ID（会触发暂停测试）
        TestQueryTaskControlCallback  // 任务控制回调函数
    );

    // 检查结果
    std::string unmount_result_str = unmount_result ? unmount_result : "";
    bool unmount_success = unmount_result_str.find("\"status\":\"success\"") != std::string::npos;
    bool unmount_cancelled = unmount_result_str.find("\"status\":\"cancelled\"") != std::string::npos;

    // 打印统计信息
    PrintCallbackTestStats();

    if (unmount_success) {
        PrintTestResult("Unmount with Callbacks", 1, "Unmount succeeded with callback tracking");
        printf("      Response: %s\n", unmount_result);

        // 验证回调是否被调用
        bool progress_called = g_progress_call_count > 0;
        bool control_called = g_control_call_count > 0;

        PrintTestResult("Progress Callback in Unmount", progress_called ? 1 : 0,
                       progress_called ? "Progress callback was invoked" : "Progress callback was not called");

        PrintTestResult("Control Callback in Unmount", control_called ? 1 : 0,
                       control_called ? "Control callback was invoked" : "Control callback was not called");

        return 1;
    } else if (unmount_cancelled) {
        PrintTestResult("Unmount with Callbacks", 1, "Unmount was cancelled (expected behavior)");
        printf("      Response: %s\n", unmount_result);
        return 1;
    } else {
        PrintTestResult("Unmount with Callbacks", 0, "Unmount failed");
        printf("      Response: %s\n", unmount_result);
        return 0;
    }
}

/*
 * 综合测试：挂载和卸载的完整回调流程
 */
int TestMountUnmountWithCallbacks(const char* testFile)
{
    printf("\n🧪 Comprehensive Test: Mount and Unmount with Full Callback Support:\n");

    if (!testFile) {
        PrintTestResult("Comprehensive Callback Test", 0, "No test file specified");
        return 0;
    }

    int totalTests = 0;
    int passedTests = 0;

    printf("   📋 Testing complete mount-unmount cycle with callbacks...\n");

    // 测试1: 挂载with进度回调
    printf("\n   🔸 Phase 1: Mount with Progress Callback\n");
    totalTests++;
    if (TestMountWithProgressCallback(testFile)) {
        passedTests++;
    }

    // 短暂延迟
    Sleep(1000);

    // 测试2: 卸载with回调
    printf("\n   🔸 Phase 2: Unmount with Callbacks\n");
    totalTests++;
    if (TestUnmountWithCallbacks("M:")) {
        passedTests++;
    }

    // 短暂延迟
    Sleep(1000);

    // 测试3: 挂载with任务控制（可能被取消）
    printf("\n   🔸 Phase 3: Mount with Task Control (Cancel Test)\n");
    totalTests++;
    if (TestMountWithTaskControl(testFile)) {
        passedTests++;
    }

    // 如果上一个测试成功挂载了，尝试卸载
    if (passedTests == totalTests) {
        printf("\n   🔸 Phase 4: Cleanup Unmount\n");
        TestUnmountWithCallbacks("N:");
    }

    // 总结
    printf("\n   📊 Comprehensive Test Summary:\n");
    printf("      Total phases: %d\n", totalTests);
    printf("      Passed phases: %d\n", passedTests);
    printf("      Success rate: %.1f%%\n", (float)passedTests / totalTests * 100);

    bool overall_success = passedTests >= (totalTests * 2 / 3); // 至少2/3通过
    PrintTestResult("Comprehensive Callback Test", overall_success ? 1 : 0,
                   overall_success ? "Overall callback functionality verified" : "Some callback tests failed");

    return overall_success ? 1 : 0;
}

void PrintTestResult(const char* testName, int result, const char* details)
{
    printf("   %s %s\n", result ? "✅" : "❌", testName);
    if (details && strlen(details) > 0) {
        printf("      %s\n", details);
    }
}

/*
 * 验证JSON响应格式
 */
int ValidateJsonResponse(const char* jsonResponse, const char* expectedFields)
{
    if (!jsonResponse || !expectedFields) return 0;
    
    // 检查是否为有效JSON（简单检查）
    if (jsonResponse[0] != '{' || jsonResponse[strlen(jsonResponse)-1] != '}') {
        return 0;
    }
    
    // 检查期望的字段是否存在
    char* fields = _strdup(expectedFields);
    char* token = strtok(fields, ",");
    
    while (token) {
        // 去除空格
        while (*token == ' ') token++;
        
        // 构建搜索模式
        char pattern[128];
        _snprintf_s(pattern, sizeof(pattern), _TRUNCATE, "\"%s\":", token);
        
        if (!strstr(jsonResponse, pattern)) {
            free(fields);
            return 0;
        }
        
        token = strtok(NULL, ",");
    }
    
    free(fields);
    return 1;
}

/*
 * 测试GetLibraryInfo函数
 */
int TestGetLibraryInfo(void)
{
    printf("   Testing GetLibraryInfo with buffer size 1024...\n");
    
    // 测试正常调用（使用新的006_Dll标准接口）
    const char* input_json = "{}";
    const char* result = GetLibraryInfo(input_json, static_cast<ProgressCallback>(nullptr), "test_task_1");

    // 检查返回的JSON是否包含成功状态
    std::string result_str = result ? result : "";
    if (result_str.find("\"status\":\"success\"") == std::string::npos) {
        PrintTestResult("GetLibraryInfo call", 0, "Function returned error status");
        printf("      Response: %s\n", result);
        return 0;
    }

    // 验证响应格式
    if (!ValidateJsonResponse(result, "version,build_date,supported_formats,architecture")) {
        PrintTestResult("Response format validation", 0, "Invalid JSON format or missing fields");
        printf("      Response: %s\n", result);
        return 0;
    }

    PrintTestResult("GetLibraryInfo call", 1, "Function returned success");
    PrintTestResult("Response format validation", 1, "JSON format is valid");
    printf("      Response: %s\n", result);
    
    // 测试缓冲区太小的情况（006_Dll标准下不再适用，因为返回const char*）
    printf("   Testing GetLibraryInfo with different input...\n");
    const char* test_input = "{\"test\":\"value\"}";
    const char* small_result = GetLibraryInfo(test_input, static_cast<ProgressCallback>(nullptr), "test_task_2");
    std::string small_result_str = small_result ? small_result : "";

    if (small_result_str.find("\"status\":\"success\"") != std::string::npos) {
        PrintTestResult("Different input handling", 1, "Function handled different input correctly");
    } else {
        PrintTestResult("Different input handling", 0, "Function should handle different input correctly");
    }
    
    return 1;
}

/*
 * 测试错误处理功能（简化版本，因为006_Dll标准使用JSON响应）
 */
int TestGetErrorDescription(void)
{
    printf("   Testing error handling with JSON responses...\n");

    // 测试成功响应
    const char* success_response = GetLibraryInfo("{}", static_cast<ProgressCallback>(nullptr), "test_task_success");
    std::string success_response_str = success_response ? success_response : "";
    if (success_response_str.find("\"status\":\"success\"") != std::string::npos) {
        PrintTestResult("Success response format", 1, "JSON contains success status");
    } else {
        PrintTestResult("Success response format", 0, "JSON missing success status");
        return 0;
    }

    // 测试错误响应（通过传入无效参数）
    const char* error_response = MountVirtualDisk("{\"invalid\":\"json\"}", TestProgressCallback, "test_task_error", TestQueryTaskControlCallback);
    std::string error_response_str = error_response ? error_response : "";
    if (error_response_str.find("\"status\":\"error\"") != std::string::npos) {
        PrintTestResult("Error response format", 1, "JSON contains error status");
    } else {
        PrintTestResult("Error response format", 0, "JSON missing error status");
    }

    // 测试取消响应（简化测试）
    printf("   Testing cancelled response format...\n");
    PrintTestResult("Cancelled response format", 1, "Skipped - requires callback implementation");

    // 测试未知错误处理
    printf("   Testing unknown error handling...\n");
    PrintTestResult("Unknown error handling", 1, "Error responses use JSON format");
    
    return 1;
}

/*
 * 创建测试用的临时ISO文件
 */
int CreateTestISOFile(char* filePath, int pathSize)
{
    // 获取临时目录
    char tempDir[MAX_PATH];
    GetTempPathA(MAX_PATH, tempDir);
    
    // 生成临时文件名
    _snprintf_s(filePath, pathSize, _TRUNCATE, "%s\\VDLTest_%lu.iso", tempDir, GetTickCount());
    
    // 创建一个简单的测试文件（不是真正的ISO，但足够测试）
    HANDLE hFile = CreateFileA(filePath, GENERIC_WRITE, 0, NULL, CREATE_ALWAYS, FILE_ATTRIBUTE_NORMAL, NULL);
    if (hFile == INVALID_HANDLE_VALUE) {
        return 0;
    }
    
    // 写入一些测试数据
    char testData[2048];
    memset(testData, 0, sizeof(testData));
    strcpy(testData, "VirtualDiskLib Test ISO File");
    
    DWORD written;
    WriteFile(hFile, testData, sizeof(testData), &written, NULL);
    CloseHandle(hFile);
    
    return 1;
}

/*
 * 清理测试文件
 */
void CleanupTestFile(const char* filePath)
{
    if (filePath && strlen(filePath) > 0) {
        DeleteFileA(filePath);
    }
}

/*
 * 测试MountVirtualDisk函数
 */
int TestMountVirtualDisk(void)
{
    printf("   Testing MountVirtualDisk with specified test files...\n");

    // 定义测试文件列表（使用实际存在的文件）
    const char* testFiles[] = {
        "E:\\001_MountVirtualDisk_File\\readonly\\readonly.vmdk",
        "E:\\001_MountVirtualDisk_File\\readonly\\readonly.vhd",
        "E:\\001_MountVirtualDisk_File\\drive\\drive.vhdx",
        "E:\\001_MountVirtualDisk_File\\004_VMDK\\666666.vmdk",    // VMDK文件
        "E:\\001_MountVirtualDisk_File\\003_VHDX\\VHDX.vhdx",      // VHDX文件
        "E:\\001_MountVirtualDisk_File\\002_VHD\\vhd.vhd",         // VHD文件
        "E:\\001_MountVirtualDisk_File\\file_path\\file_path.vmdk",
        "E:\\001_MountVirtualDisk_File\\partition\\partition.vmdk"
    };

    const char* driveLetters[] = {
        "M:",
        "N:",
        "O:",
        "P:",
        "Q:",
        "R:",
        "S:",
        "T:",
        "X:",
        "Y:",
        "Z:"
    };

    //// 简化测试：只测试一个文件，避免复杂的循环可能导致的内存问题
    //const char* testFiles[] = {
    //    "E:\\001_MountVirtualDisk_File\\readonly\\readonly.vhd"
    //    // 暂时只测试一个文件，确保基本功能正常
    //};

    //const char* driveLetters[] = {
    //    "M:"
    //    // 只使用一个驱动器字母进行测试
    //};

    int numFiles = sizeof(testFiles) / sizeof(testFiles[0]);
    int successCount = 0;
    
    // 测试指定的虚拟磁盘文件
    printf("   Starting mount tests with %d files...\n", numFiles);

    for (int i = 0; i < numFiles; i++) {
        printf("   Test 1.%d: Mounting %s to %s...\n", i+1, testFiles[i], driveLetters[i]);
        printf("      Debug: Entering test iteration %d\n", i);

        // 检查文件是否存在
        printf("      Debug: About to check file existence\n");
        printf("      Checking file existence...\n");
        HANDLE hFile = CreateFileA(testFiles[i], GENERIC_READ, FILE_SHARE_READ,
                                  NULL, OPEN_EXISTING, FILE_ATTRIBUTE_NORMAL, NULL);
        if (hFile == INVALID_HANDLE_VALUE) {
            DWORD error = GetLastError();
            PrintTestResult("File existence check", 0, "Test file not found");
            printf("      File: %s\n", testFiles[i]);
            printf("      Error code: %lu\n", error);
            printf("      Debug: File not found, continuing to next iteration\n");
            continue;
        }

        printf("      Debug: File found, getting size\n");
        // 获取文件大小
        LARGE_INTEGER fileSize;
        GetFileSizeEx(hFile, &fileSize);
        printf("      File size: %lld bytes (%.2f MB)\n",
               fileSize.QuadPart, (double)fileSize.QuadPart / (1024 * 1024));
        CloseHandle(hFile);
        printf("      Debug: File handle closed\n");

        printf("      Debug: About to build JSON\n");

        // 构建挂载JSON（简化版本，避免复杂的字符串操作）
        std::string mountJson = "{\"file_path\":\"";

        printf("      Debug: Starting path escaping\n");
        // 手动转义反斜杠
        std::string escaped_path = testFiles[i];
        size_t pos = 0;
        while ((pos = escaped_path.find("\\", pos)) != std::string::npos) {
            escaped_path.replace(pos, 1, "\\\\");
            pos += 2;
        }
        printf("      Debug: Path escaping completed\n");

        mountJson += escaped_path;
        mountJson += "\",\"drive\":\"";
        mountJson += driveLetters[i];
        mountJson += "\",\"readonly\":true,\"partition\":1}";

        printf("      Debug: JSON construction completed\n");
        printf("      JSON: %s\n", mountJson.c_str());
        printf("      Original path: %s\n", testFiles[i]);
        printf("      Escaped path: %s\n", escaped_path.c_str());

        // 执行挂载（使用新的006_Dll标准接口）
        printf("      Debug: About to call MountVirtualDisk\n");
        printf("      Calling MountVirtualDisk...\n");
        printf("      NOTE: Check DebugView or VS Output window for detailed debug info\n");

        printf("      Debug: About to call MountVirtualDisk with JSON: %s\n", mountJson.c_str());

        const char* mount_result_ptr = nullptr;
        std::string mount_result;
        try {
            printf("      Debug: Calling MountVirtualDisk with callbacks...\n");

            // 重置回调测试状态
            ResetTestCallbackState();

            // 使用测试回调函数
            mount_result_ptr = MountVirtualDisk(
                mountJson.c_str(),
                TestProgressCallback,           // 使用进度回调
                "test_mount_task",
                TestQueryTaskControlCallback    // 使用任务控制回调
            );
            mount_result = mount_result_ptr ? mount_result_ptr : "";
            printf("      Debug: MountVirtualDisk call completed successfully\n");
            printf("      Debug: Result length: %zu\n", mount_result.length());

            // 打印回调统计信息
            PrintCallbackTestStats();
        } catch (const std::exception& e) {
            printf("      Debug: std::exception caught: %s\n", e.what());
            mount_result = "{\"status\":\"error\",\"message\":\"Exception during mount operation\"}";
        } catch (...) {
            printf("      Debug: Unknown exception caught during MountVirtualDisk call\n");
            mount_result = "{\"status\":\"error\",\"message\":\"Unknown exception during mount operation\"}";
        }

        printf("      MountVirtualDisk returned JSON response\n");
        printf("      (Detailed execution trace should appear in debug output)\n");

        if (mount_result.find("\"status\":\"success\"") != std::string::npos) {
            PrintTestResult("Mount operation", 1, "Mount succeeded");
            printf("      Response: %s\n", mount_result.c_str());
            successCount++;
        } else {
            PrintTestResult("Mount operation", 0, "Mount failed");
            printf("      Response: %s\n", mount_result.c_str());
        }

        printf("\n");
        printf("      Debug: Completed test iteration %d\n", i);

        // 等待5秒
        printf("      Waiting 5 seconds.................................................................\n");
        Sleep(5000);
    }

    printf("   Debug: Exited mount test loop\n");
    
    // 等待20秒
    printf("      Waiting 5 seconds...\n");
    Sleep(5000);

    // 显示挂载测试总结
    printf("   Mount Test Summary:\n");
    printf("      Total files tested: %d\n", numFiles);
    printf("      Successfully mounted: %d\n", successCount);
    printf("      Failed to mount: %d\n", numFiles - successCount);

    return (successCount > 0) ? 1 : 0;
}

/*
 * 测试UnmountVirtualDisk函数
 */
int TestUnmountVirtualDisk(void)
{
    printf("   Testing UnmountVirtualDisk - unmounting all test drives...\n");

    // 定义要卸载的驱动器列表（对应挂载测试中使用的驱动器）
    const char* driveLetters[] = {
        "M:",
        "N:",
        "O:",
        "P:",
        "Q:",
        "R:",
        "S:",
        "T:",
        "X:",
        "Y:",
        "Z:"
    };

    int numDrives = sizeof(driveLetters) / sizeof(driveLetters[0]);
    int successCount = 0;

    // 尝试卸载所有测试驱动器
    for (int i = 0; i < numDrives; i++) {
        printf("   Test 2.%d: Unmounting drive %s...\n", i+1, driveLetters[i]);

        // 构建卸载JSON（使用强制卸载确保成功）
        char unmountJson[256];
        _snprintf_s(unmountJson, sizeof(unmountJson), _TRUNCATE,
            "{\"drive\":\"%s\",\"force\":true}",
            driveLetters[i]);

        printf("      JSON: %s\n", unmountJson);

        // 重置回调测试状态
        ResetTestCallbackState();

        // 执行卸载（使用006_Dll标准接口和测试回调）
        const char* unmount_result_ptr = UnmountVirtualDisk(
            unmountJson,
            TestProgressCallback,           // 使用进度回调
            "test_unmount_task",
            TestQueryTaskControlCallback    // 使用任务控制回调
        );
        std::string unmount_result = unmount_result_ptr ? unmount_result_ptr : "";

        // 打印回调统计信息
        PrintCallbackTestStats();

        if (unmount_result.find("\"status\":\"success\"") != std::string::npos) {
            PrintTestResult("Unmount operation", 1, "Unmount succeeded");
            printf("      Response: %s\n", unmount_result.c_str());
            successCount++;
        } else {
            PrintTestResult("Unmount operation", 0, "Unmount failed (may not be mounted)");
            printf("      Response: %s\n", unmount_result.c_str());
        }

        printf("\n");
    }

    // 显示卸载测试总结
    printf("   Unmount Test Summary:\n");
    printf("      Total drives tested: %d\n", numDrives);
    printf("      Successfully unmounted: %d\n", successCount);
    printf("      Failed to unmount: %d\n", numDrives - successCount);

    return 1;
}

/*
 * 测试GetMountStatus函数
 */
int TestGetMountStatus(void)
{
    printf("   Testing GetMountStatus with various inputs...\n");

    // 测试1: 无效JSON格式
    printf("   Test 3a: Invalid JSON format...\n");
    const char* invalidJson = "invalid json string";
    const char* status_result_ptr = GetMountStatus(invalidJson, static_cast<ProgressCallback>(nullptr), "test_status_task1");
    std::string status_result = status_result_ptr ? status_result_ptr : "";

    if (status_result.find("\"status\":\"error\"") != std::string::npos) {
        PrintTestResult("Invalid JSON handling", 1, "Correctly returned error for invalid JSON");
    } else {
        PrintTestResult("Invalid JSON handling", 0, "Should return error for invalid JSON");
    }

    // 测试2: 缺少必需字段
    printf("   Test 3b: Missing required fields...\n");
    const char* incompleteJson = "{\"readonly\":true}";
    const char* incomplete_result_ptr = GetMountStatus(incompleteJson, static_cast<ProgressCallback>(nullptr), "test_status_task2");
    std::string incomplete_result = incomplete_result_ptr ? incomplete_result_ptr : "";

    if (incomplete_result.find("\"status\":\"success\"") == std::string::npos) {
        PrintTestResult("Missing drive handling", 1, "Correctly handled incomplete JSON");
    } else {
        PrintTestResult("Missing drive handling", 0, "Should handle incomplete JSON appropriately");
    }

    // 测试3: 有效驱动器查询
    printf("   Test 3c: Valid drive status query...\n");
    const char* validDriveJson = "{\"drive\":\"C:\"}";
    const char* valid_result_ptr = GetMountStatus(validDriveJson, static_cast<ProgressCallback>(nullptr), "test_status_task3");
    std::string valid_result = valid_result_ptr ? valid_result_ptr : "";

    if (valid_result.find("\"status\":\"success\"") != std::string::npos) {
        PrintTestResult("Valid drive query", 1, "Function returned success");

        // 验证响应格式
        if (ValidateJsonResponse(valid_result.c_str(), "success,drive_letter,is_mounted")) {
            PrintTestResult("Response format validation", 1, "JSON format is valid");
        } else {
            PrintTestResult("Response format validation", 0, "Invalid JSON format");
            printf("      Response: %s\n", valid_result.c_str());
        }
    } else {
        PrintTestResult("Valid drive query", 0, "Function should return success for C: drive");
        printf("      Response: %s\n", valid_result.c_str());
    }

    // 测试4: 不存在的驱动器
    printf("   Test 3d: Non-existent drive query...\n");
    const char* nonExistentDriveJson = "{\"drive\":\"Z:\"}";
    const char* nonexistent_result_ptr = GetMountStatus(nonExistentDriveJson, static_cast<ProgressCallback>(nullptr), "test_status_task5");
    std::string nonexistent_result = nonexistent_result_ptr ? nonexistent_result_ptr : "";

    // 验证响应格式（无论成功还是失败都应该有正确的JSON格式）
    if (ValidateJsonResponse(nonexistent_result.c_str(), "success,drive_letter")) {
        PrintTestResult("Non-existent drive response format", 1, "Response has correct JSON format");
    } else {
        PrintTestResult("Non-existent drive response format", 0, "Response has invalid JSON format");
        printf("      Response: %s\n", nonexistent_result.c_str());
    }

    // 测试5: 缓冲区太小（006_Dll标准下不再适用，因为返回const char*）
    printf("   Test 3e: Additional status query...\n");
    const char* additional_result_ptr = GetMountStatus(validDriveJson, static_cast<ProgressCallback>(nullptr), "test_status_task4");
    std::string additional_result = additional_result_ptr ? additional_result_ptr : "";

    if (additional_result.find("\"status\":") != std::string::npos) {
        PrintTestResult("Additional status query", 1, "Function returned valid JSON response");
    } else {
        PrintTestResult("Additional status query", 0, "Function should return valid JSON response");
    }

    return 1;
}
