/*
 * VirtualDiskLib.cpp
 * 虚拟磁盘挂载库 - 符合006_Dll要求的现代化实现
 *
 * 特性：
 * - 符合006_Dll标准的接口设计
 * - 支持进度回调和任务控制
 * - 使用C++11现代特性
 * - 保持Windows XP兼容性
 * - 直接集成MountImg.c实现
 */

#define _WIN32_WINNT 0x0501  // Windows XP兼容
#define OEMRESOURCE
#define _CRT_SECURE_NO_WARNINGS

// C++11标准库
#include <string>
#include <memory>
#include <functional>
#include <algorithm>
#include <chrono>
#include <thread>
#include <atomic>
#include <mutex>
#include <map>

// Windows API
#include <windows.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <dbt.h>               // 用于设备广播消息
#include <dbt.h>
#include <shlwapi.h>
#include <winternl.h>

// 项目头文件
#include "VirtualDiskLib.h"
#include "../MountImg.h"       // MountImg.c的头文件
#include "../../inc/imdisk.h"  // ImDisk API 定义
#include "../MountImg.h"       // MountImg.c的头文件
#include "../../inc/imdisk.h"  // ImDisk API 定义
#include "../../inc/imdisktk.h" // ImDisk 工具包定义

// 简化的JSON处理实现 (兼容C++11和XP)
#ifdef _MSC_VER
#pragma warning(push)
#pragma warning(disable: 4996) // 禁用不安全函数警告
#endif

// 简化的JSON响应生成函数 (兼容VS2013)
std::string create_success_response(const std::string& message) {
    char buffer[1024];
    sprintf_s(buffer, sizeof(buffer),
        "{\"status\":\"success\",\"message\":\"%s\"}",
        message.c_str());
    return std::string(buffer);
}

std::string create_error_response(const std::string& message) {
    char buffer[1024];
    sprintf_s(buffer, sizeof(buffer),
        "{\"status\":\"error\",\"message\":\"%s\"}",
        message.c_str());
    return std::string(buffer);
}

std::string create_cancelled_response(const std::string& message) {
    char buffer[1024];
    sprintf_s(buffer, sizeof(buffer),
        "{\"status\":\"cancelled\",\"message\":\"%s\"}",
        message.c_str());
    return std::string(buffer);
}

std::string create_mount_success_response(const std::string& drive, const std::string& file_path) {
    char buffer[2048];
    sprintf_s(buffer, sizeof(buffer),
        "{"
        "\"status\":\"success\","
        "\"message\":\"Mount operation completed successfully\","
        "\"mounted_drive\":\"%s\","
        "\"mounted_file\":\"%s\","
        "\"mount_type\":\"virtual_disk\","
        "\"readonly\":false"
        "}",
        drive.c_str(), file_path.c_str());
    return std::string(buffer);
}

// 简化的JSON解析函数
std::string get_json_string_value(const std::string& json, const std::string& key, const std::string& default_value) {
    // 简单的字符串查找，适用于基本JSON解析
    std::string search_key = "\"" + key + "\":\"";
    size_t pos = json.find(search_key);
    if (pos == std::string::npos) {
        return default_value;
    }

    pos += search_key.length();
    size_t end_pos = json.find("\"", pos);
    if (end_pos == std::string::npos) {
        return default_value;
    }

    return json.substr(pos, end_pos - pos);
}

bool get_json_bool_value(const std::string& json, const std::string& key, bool default_value) {
    std::string search_key = "\"" + key + "\":";
    size_t pos = json.find(search_key);
    if (pos == std::string::npos) {
        return default_value;
    }

    pos += search_key.length();
    if (json.substr(pos, 4) == "true") {
        return true;
    } else if (json.substr(pos, 5) == "false") {
        return false;
    }

    return default_value;
}

int get_json_int_value(const std::string& json, const std::string& key, int default_value) {
    std::string search_key = "\"" + key + "\":";
    size_t pos = json.find(search_key);
    if (pos == std::string::npos) {
        return default_value;
    }

    pos += search_key.length();
    size_t end_pos = json.find_first_of(",}", pos);
    if (end_pos == std::string::npos) {
        return default_value;
    }

    std::string value_str = json.substr(pos, end_pos - pos);
    return atoi(value_str.c_str());
}

// 简化的JSON类实现
class SimpleJson {
public:
    std::map<std::string, std::string> string_values;
    std::map<std::string, bool> bool_values;
    std::map<std::string, int> int_values;
    bool is_valid;

    SimpleJson() : is_valid(false) {}

    static SimpleJson parse(const std::string& json_str) {
        SimpleJson result;
        result.is_valid = !json_str.empty() && json_str.find("{") != std::string::npos;
        return result;
    }

    bool is_null() const {
        return !is_valid;
    }

    std::string value(const std::string& key, const std::string& default_val) const {
        return get_json_string_value("", key, default_val);
    }

    bool value(const std::string& key, bool default_val) const {
        return get_json_bool_value("", key, default_val);
    }

    int value(const std::string& key, int default_val) const {
        return get_json_int_value("", key, default_val);
    }

    std::string dump() const {
        std::string result = "{";
        bool first = true;

        for (const auto& pair : string_values) {
            if (!first) result += ",";
            result += "\"" + pair.first + "\":\"" + pair.second + "\"";
            first = false;
        }

        for (const auto& pair : bool_values) {
            if (!first) result += ",";
            result += "\"" + pair.first + "\":" + (pair.second ? "true" : "false");
            first = false;
        }

        for (const auto& pair : int_values) {
            if (!first) result += ",";
            result += "\"" + pair.first + "\":" + std::to_string(pair.second);
            first = false;
        }

        result += "}";
        return result;
    }

    SimpleJson& operator[](const std::string& key) {
        return *this;
    }

    void operator=(const std::string& value) {
        // 简化实现
    }

    void operator=(bool value) {
        // 简化实现
    }

    void operator=(int value) {
        // 简化实现
    }

    static SimpleJson create_success_response(const std::string& message) {
        SimpleJson result;
        result.is_valid = true;
        result.string_values["status"] = "success";
        result.string_values["message"] = message;
        return result;
    }

    static SimpleJson create_error_response(const std::string& message) {
        SimpleJson result;
        result.is_valid = true;
        result.string_values["status"] = "error";
        result.string_values["message"] = message;
        return result;
    }

    static SimpleJson create_cancelled_response(const std::string& message) {
        SimpleJson result;
        result.is_valid = true;
        result.string_values["status"] = "cancelled";
        result.string_values["message"] = message;
        return result;
    }
};

// 类型别名
typedef SimpleJson json;

#ifdef _MSC_VER
#pragma warning(pop)
#endif

// ========================================
// 全局状态管理 (C++11现代化)
// ========================================

namespace {
    // 使用C++11的原子类型和互斥锁
    std::atomic<bool> g_initialized{false};
    std::mutex g_init_mutex;

    // 任务管理 - 使用普通bool，通过mutex保护
    std::map<std::string, bool> g_cancel_flags;
    std::map<std::string, bool> g_pause_flags;
    std::mutex g_task_mutex;

    // 静态缓冲区用于返回字符串，避免跨DLL边界的std::string问题
    static char g_response_buffer[8192];
    static std::mutex g_response_mutex;

    // 前向声明卸载相关函数
    int execute_command_and_wait(const wchar_t* cmd_line, DWORD timeout_ms);
    int try_imdisk_dlg_unmount(const std::wstring& drive_letter);
    int try_imdisk_direct_unmount(const std::wstring& drive_letter, bool force_unmount);
    int try_force_unmount(const std::wstring& drive_letter);
    void terminate_discutils_processes();
    int execute_unmount_with_imdisk_api(const WCHAR* mount_point, bool force_unmount);
}

// ========================================
// 辅助函数 (C++11现代化实现)
// ========================================

namespace {
    // 任务控制辅助函数
    void register_task(const std::string& task_id) {
        std::lock_guard<std::mutex> lock(g_task_mutex);
        g_cancel_flags[task_id] = false;
        g_pause_flags[task_id] = false;
    }

    void unregister_task(const std::string& task_id) {
        std::lock_guard<std::mutex> lock(g_task_mutex);
        g_cancel_flags.erase(task_id);
        g_pause_flags.erase(task_id);
    }

    bool is_task_cancelled(const std::string& task_id) {
        std::lock_guard<std::mutex> lock(g_task_mutex);
        auto it = g_cancel_flags.find(task_id);
        return it != g_cancel_flags.end() && it->second;
    }

    bool is_task_paused(const std::string& task_id) {
        std::lock_guard<std::mutex> lock(g_task_mutex);
        auto it = g_pause_flags.find(task_id);
        return it != g_pause_flags.end() && it->second;
    }

    void set_task_cancelled(const std::string& task_id, bool cancelled) {
        std::lock_guard<std::mutex> lock(g_task_mutex);
        auto it = g_cancel_flags.find(task_id);
        if (it != g_cancel_flags.end()) {
            it->second = cancelled;
        }
    }

    void set_task_paused(const std::string& task_id, bool paused) {
        std::lock_guard<std::mutex> lock(g_task_mutex);
        auto it = g_pause_flags.find(task_id);
        if (it != g_pause_flags.end()) {
            it->second = paused;
        }
    }

    // 任务控制检查函数
    bool check_task_control(const std::string& task_id,
                           QueryTaskControlCallback callback,
                           ProgressCallback progress_callback) {
        if (!callback) return false;

        // 检查取消
        if (callback(task_id, 0)) {
            set_task_cancelled(task_id, true);
            return true;
        }

        // 检查暂停
        while (callback(task_id, 1)) {
            set_task_paused(task_id, true);
            std::this_thread::sleep_for(std::chrono::milliseconds(100));

            // 暂停期间也要检查取消
            if (callback(task_id, 0)) {
                set_task_cancelled(task_id, true);
                return true;
            }
        }

        set_task_paused(task_id, false);
        return false;
    }

    // 进度报告辅助函数
    void report_progress(const std::string& task_id, int progress,
                        ProgressCallback callback,
                        const std::string& match_result = "") {
        // 简化调试输出，避免复杂字符串操作
        OutputDebugStringA("DEBUG: report_progress called\n");

        if (callback) {
            try {
                OutputDebugStringA("DEBUG: About to call progress callback\n");
                callback(task_id, progress, match_result);
                OutputDebugStringA("DEBUG: Progress callback completed successfully\n");
            } catch (...) {
                OutputDebugStringA("DEBUG: Exception in progress callback\n");
                // 回调异常不应影响主任务
            }
        } else {
            OutputDebugStringA("DEBUG: Progress callback is null, skipping\n");
        }
    }

    // 安全地将响应复制到静态缓冲区
    const char* copy_response_to_buffer(const std::string& response) {
        std::lock_guard<std::mutex> lock(g_response_mutex);
        strncpy_s(g_response_buffer, sizeof(g_response_buffer), response.c_str(), _TRUNCATE);
        return g_response_buffer;
    }

    // 执行ImDisk挂载命令
    int execute_mount_command(const std::wstring& cmdline) {
        STARTUPINFOW si = { sizeof(si) };
        PROCESS_INFORMATION pi = { 0 };

        OutputDebugStringA("DEBUG: Attempting ImDisk direct mount\n");

        if (CreateProcessW(NULL, const_cast<LPWSTR>(cmdline.c_str()), NULL, NULL, FALSE,
                          CREATE_NO_WINDOW, NULL, NULL, &si, &pi)) {
            WaitForSingleObject(pi.hProcess, INFINITE);

            DWORD exit_code;
            GetExitCodeProcess(pi.hProcess, &exit_code);

            CloseHandle(pi.hProcess);
            CloseHandle(pi.hThread);

            OutputDebugStringA(exit_code == 0 ? "DEBUG: ImDisk mount succeeded\n" : "DEBUG: ImDisk mount failed\n");
            return exit_code;
        }

        OutputDebugStringA("DEBUG: Failed to create ImDisk process\n");
        return -1;
    }

    // 执行DiscUtils挂载
    int execute_discutils_mount(const std::wstring& discutils_cmd, const std::wstring& drive_letter, __int64 pipe_id, bool readonly) {
        STARTUPINFOW si = { sizeof(si) };
        PROCESS_INFORMATION pi_discutils = { 0 };
        PROCESS_INFORMATION pi_imdisk = { 0 };

        OutputDebugStringA("DEBUG: Attempting DiscUtils mount\n");

        // 输出DiscUtils命令用于调试
        char debug_cmd[2048];
        WideCharToMultiByte(CP_UTF8, 0, discutils_cmd.c_str(), -1, debug_cmd, sizeof(debug_cmd), NULL, NULL);
        OutputDebugStringA("DEBUG: DiscUtils command: ");
        OutputDebugStringA(debug_cmd);
        OutputDebugStringA("\n");

        // 启动DiscUtilsDevio服务
        if (!CreateProcessW(NULL, const_cast<LPWSTR>(discutils_cmd.c_str()), NULL, NULL, FALSE,
                           CREATE_NO_WINDOW, NULL, NULL, &si, &pi_discutils)) {
            DWORD error = GetLastError();
            char error_msg[256];
            sprintf_s(error_msg, sizeof(error_msg), "DEBUG: Failed to start DiscUtilsDevio, error: %d\n", error);
            OutputDebugStringA(error_msg);
            return -1;
        }

        // 等待DiscUtilsDevio启动
        Sleep(1000);

        // 构建ImDisk代理挂载命令（使用与MountImg.c相同的格式）
        wchar_t imdisk_cmd[1024];
        _snwprintf(imdisk_cmd, _countof(imdisk_cmd),
            L"imdisk -a -t proxy -m \"%s\" -o shm,hd,r%c,rem -f ImDisk%I64x",
            drive_letter.c_str(),
            readonly ? L'o' : L'w',
            pipe_id);

        OutputDebugStringA("DEBUG: Starting ImDisk proxy mount\n");

        // 输出ImDisk命令用于调试
        char debug_imdisk_cmd[2048];
        WideCharToMultiByte(CP_UTF8, 0, imdisk_cmd, -1, debug_imdisk_cmd, sizeof(debug_imdisk_cmd), NULL, NULL);
        OutputDebugStringA("DEBUG: ImDisk command: ");
        OutputDebugStringA(debug_imdisk_cmd);
        OutputDebugStringA("\n");

        // 启动ImDisk代理挂载
        if (CreateProcessW(NULL, imdisk_cmd, NULL, NULL, FALSE,
                          CREATE_NO_WINDOW, NULL, NULL, &si, &pi_imdisk)) {
            WaitForSingleObject(pi_imdisk.hProcess, INFINITE);

            DWORD exit_code;
            GetExitCodeProcess(pi_imdisk.hProcess, &exit_code);

            CloseHandle(pi_imdisk.hProcess);
            CloseHandle(pi_imdisk.hThread);

            if (exit_code == 0) {
                OutputDebugStringA("DEBUG: DiscUtils mount succeeded\n");
                // 保持DiscUtilsDevio运行
                CloseHandle(pi_discutils.hThread);
                CloseHandle(pi_discutils.hProcess);
                return 0;
            } else {
                char error_msg[256];
                sprintf_s(error_msg, sizeof(error_msg), "DEBUG: ImDisk proxy mount failed with exit code: %d\n", exit_code);
                OutputDebugStringA(error_msg);
            }
        } else {
            DWORD error = GetLastError();
            char error_msg[256];
            sprintf_s(error_msg, sizeof(error_msg), "DEBUG: Failed to create ImDisk proxy process, error: %d\n", error);
            OutputDebugStringA(error_msg);
        }

        // 如果失败，终止DiscUtilsDevio
        TerminateProcess(pi_discutils.hProcess, 1);
        CloseHandle(pi_discutils.hProcess);
        CloseHandle(pi_discutils.hThread);

        return -1;
    }

    // 执行卸载操作 - 支持多种卸载方式
    int execute_unmount_operation(const std::wstring& drive_letter, bool force_unmount) {
        OutputDebugStringA("DEBUG: Starting unmount operation\n");

        // 输出驱动器信息用于调试
        char debug_drive[256];
        WideCharToMultiByte(CP_UTF8, 0, drive_letter.c_str(), -1, debug_drive, sizeof(debug_drive), NULL, NULL);
        OutputDebugStringA("DEBUG: Unmounting drive: ");
        OutputDebugStringA(debug_drive);
        OutputDebugStringA("\n");

        int result = -1;

        // 方法1: 尝试使用ImDisk-Dlg RM命令（与原始MountImg.c一致）
        result = try_imdisk_dlg_unmount(drive_letter);
        if (result == 0) {
            OutputDebugStringA("DEBUG: ImDisk-Dlg RM unmount succeeded\n");
            return 0;
        }

        // 方法2: 尝试使用imdisk -d命令
        result = try_imdisk_direct_unmount(drive_letter, force_unmount);
        if (result == 0) {
            OutputDebugStringA("DEBUG: imdisk -d unmount succeeded\n");
            return 0;
        }

        // 方法3: 如果是强制卸载，尝试终止相关进程
        if (force_unmount) {
            result = try_force_unmount(drive_letter);
            if (result == 0) {
                OutputDebugStringA("DEBUG: Force unmount succeeded\n");
                return 0;
            }
        }

        OutputDebugStringA("DEBUG: All unmount methods failed\n");
        return -1;
    }

    // 尝试使用ImDisk-Dlg RM命令卸载
    int try_imdisk_dlg_unmount(const std::wstring& drive_letter) {
        OutputDebugStringA("DEBUG: Trying ImDisk-Dlg RM unmount\n");

        wchar_t cmd_line[1024];
        _snwprintf(cmd_line, _countof(cmd_line), L"ImDisk-Dlg RM \"%s\"", drive_letter.c_str());

        return execute_command_and_wait(cmd_line, 30000);
    }

    // 尝试使用imdisk -d命令卸载
    int try_imdisk_direct_unmount(const std::wstring& drive_letter, bool force_unmount) {
        OutputDebugStringA("DEBUG: Trying imdisk -d unmount\n");

        wchar_t cmd_line[1024];
        if (force_unmount) {
            _snwprintf(cmd_line, _countof(cmd_line), L"imdisk -d -f -m \"%s\"", drive_letter.c_str());
        } else {
            _snwprintf(cmd_line, _countof(cmd_line), L"imdisk -d -m \"%s\"", drive_letter.c_str());
        }

        return execute_command_and_wait(cmd_line, 30000);
    }

    // 尝试强制卸载（终止相关进程）
    int try_force_unmount(const std::wstring& drive_letter) {
        OutputDebugStringA("DEBUG: Trying force unmount\n");

        // 首先尝试终止可能的DiscUtilsDevio进程
        terminate_discutils_processes();

        // 然后再次尝试普通卸载
        return try_imdisk_direct_unmount(drive_letter, true);
    }

    // 终止DiscUtilsDevio相关进程
    void terminate_discutils_processes() {
        OutputDebugStringA("DEBUG: Terminating DiscUtilsDevio processes\n");

        // 使用taskkill命令终止DiscUtilsDevio进程
        wchar_t cmd_line[] = L"taskkill /f /im DiscUtilsDevio.exe";
        execute_command_and_wait(cmd_line, 10000);
    }

    // 执行命令并等待完成
    int execute_command_and_wait(const wchar_t* cmd_line, DWORD timeout_ms) {
        // 输出命令用于调试
        char debug_cmd[2048];
        WideCharToMultiByte(CP_UTF8, 0, cmd_line, -1, debug_cmd, sizeof(debug_cmd), NULL, NULL);
        OutputDebugStringA("DEBUG: Executing command: ");
        OutputDebugStringA(debug_cmd);
        OutputDebugStringA("\n");

        STARTUPINFOW si = {0};
        PROCESS_INFORMATION pi = {0};
        si.cb = sizeof(si);
        si.dwFlags = STARTF_USESHOWWINDOW;
        si.wShowWindow = SW_HIDE;

        BOOL process_created = CreateProcessW(
            nullptr,
            const_cast<LPWSTR>(cmd_line),
            nullptr,
            nullptr,
            FALSE,
            0,
            nullptr,
            nullptr,
            &si,
            &pi
        );

        if (!process_created) {
            DWORD error = GetLastError();
            char error_msg[256];
            sprintf_s(error_msg, sizeof(error_msg), "DEBUG: Failed to create process, error: %d\n", error);
            OutputDebugStringA(error_msg);
            return -1;
        }

        // 等待进程完成
        DWORD wait_result = WaitForSingleObject(pi.hProcess, timeout_ms);
        int result = -1;

        if (wait_result == WAIT_OBJECT_0) {
            DWORD exit_code;
            GetExitCodeProcess(pi.hProcess, &exit_code);
            result = (exit_code == 0) ? 0 : -1;

            char exit_msg[256];
            sprintf_s(exit_msg, sizeof(exit_msg), "DEBUG: Process completed with exit code: %d\n", exit_code);
            OutputDebugStringA(exit_msg);
        } else {
            OutputDebugStringA("DEBUG: Process timeout or error, terminating\n");
            TerminateProcess(pi.hProcess, 1);
            result = -1;
        }

        CloseHandle(pi.hProcess);
        CloseHandle(pi.hThread);

        return result;
    }

    // 使用ImDisk API执行卸载操作 - 参照VirtualDiskLib_Old.cpp实现
    int execute_unmount_with_imdisk_api(const WCHAR* mount_point, bool force_unmount) {
        OutputDebugStringA("=== Starting ImDisk API Unmount ===\n");

        // 声明所有变量
        HANDLE h = INVALID_HANDLE_VALUE;
        DWORD access_list[] = { GENERIC_READ | GENERIC_WRITE, GENERIC_READ, GENERIC_WRITE };
        int n_access;
        DWORD dw;
        DEV_BROADCAST_VOLUME dbv;
        DWORD_PTR dwp;
        HMODULE h_cpl = NULL;

        // 初始化 dbv 结构
        memset(&dbv, 0, sizeof(dbv));
        dbv.dbcv_size = sizeof(dbv);
        dbv.dbcv_devicetype = DBT_DEVTYP_VOLUME;

        // 步骤0: 加载ImDisk函数
        OutputDebugStringA("Step 0: Loading ImDisk functions...\n");
        h_cpl = LoadLibraryA("imdisk.cpl");
        if (!h_cpl) {
            OutputDebugStringA("❌ Cannot find imdisk.cpl\n");
            return -1;
        }

        // 获取 ImDiskOpenDeviceByMountPoint 函数指针
        typedef HANDLE (WINAPI *ImDiskOpenDeviceByMountPointProc)(LPCWSTR, DWORD);
        ImDiskOpenDeviceByMountPointProc ImDiskOpenDeviceByMountPoint =
            (ImDiskOpenDeviceByMountPointProc)GetProcAddress(h_cpl, "ImDiskOpenDeviceByMountPoint");

        if (!ImDiskOpenDeviceByMountPoint) {
            OutputDebugStringA("❌ Cannot find ImDiskOpenDeviceByMountPoint function\n");
            FreeLibrary(h_cpl);
            return -1;
        }

        OutputDebugStringA("✅ ImDisk functions loaded successfully\n");

        // 步骤1: 打开设备
        OutputDebugStringA("Step 1: Opening ImDisk device...\n");
        for (n_access = 0; n_access < _countof(access_list); n_access++) {
            h = (HANDLE)ImDiskOpenDeviceByMountPoint(mount_point, access_list[n_access]);
            if (h != INVALID_HANDLE_VALUE) break;
        }

        if (h == INVALID_HANDLE_VALUE) {
            OutputDebugStringA("❌ Cannot open ImDisk device\n");
            FreeLibrary(h_cpl);
            return -1;
        }

        char debug_info[256];
        sprintf_s(debug_info, sizeof(debug_info), "✅ Device opened with access mode %d\n", n_access);
        OutputDebugStringA(debug_info);

        // 步骤2: 查询设备信息
        OutputDebugStringA("Step 2: Querying device info...\n");
        struct { IMDISK_CREATE_DATA icd; WCHAR buff[MAX_PATH + 15]; } create_data = {};

        if (!DeviceIoControl(h, IOCTL_IMDISK_QUERY_DEVICE, NULL, 0, &create_data, sizeof(create_data), &dw, NULL)) {
            OutputDebugStringA("❌ Device is not an ImDisk virtual disk\n");
            CloseHandle(h);
            FreeLibrary(h_cpl);
            return -1;
        }

        OutputDebugStringA("✅ Device info queried successfully\n");

        // 步骤3: 发送设备移除通知
        if (mount_point[1] == L':' && !mount_point[2]) {
            dbv.dbcv_unitmask = 1 << (mount_point[0] - L'A');

            OutputDebugStringA("Step 3: Sending device remove notification...\n");
            SendMessageTimeout(HWND_BROADCAST, WM_DEVICECHANGE, DBT_DEVICEREMOVEPENDING,
                              (LPARAM)&dbv, SMTO_BLOCK | SMTO_ABORTIFHUNG, 4000, &dwp);
            OutputDebugStringA("✅ Device remove notification sent\n");
        }

        // 步骤4: 刷新文件缓冲区
        OutputDebugStringA("Step 4: Flushing file buffers...\n");
        FlushFileBuffers(h);
        OutputDebugStringA("✅ File buffers flushed\n");

        // 步骤5: 锁定卷
        OutputDebugStringA("Step 5: Locking volume...\n");
        if (!DeviceIoControl(h, FSCTL_LOCK_VOLUME, NULL, 0, NULL, 0, &dw, NULL)) {
            OutputDebugStringA("⚠️ Warning: Cannot lock volume, continuing...\n");
        } else {
            OutputDebugStringA("✅ Volume locked\n");
        }

        // 步骤6: 卸载卷
        OutputDebugStringA("Step 6: Dismounting volume...\n");
        DeviceIoControl(h, FSCTL_DISMOUNT_VOLUME, NULL, 0, NULL, 0, &dw, NULL);
        OutputDebugStringA("✅ Volume dismounted\n");

        // 再次锁定卷
        DeviceIoControl(h, FSCTL_LOCK_VOLUME, NULL, 0, NULL, 0, &dw, NULL);

        // 步骤7: 弹出媒体
        OutputDebugStringA("Step 7: Ejecting media...\n");
        if (!DeviceIoControl(h, IOCTL_STORAGE_EJECT_MEDIA, NULL, 0, NULL, 0, &dw, NULL)) {
            // 尝试强制移除设备
            typedef BOOL (WINAPI *ImDiskForceRemoveDeviceProc)(HANDLE, DWORD);
            ImDiskForceRemoveDeviceProc ForceRemove = (ImDiskForceRemoveDeviceProc)GetProcAddress(h_cpl, "ImDiskForceRemoveDevice");
            if (ForceRemove && !ForceRemove(h, 0)) {
                OutputDebugStringA("❌ Cannot remove device\n");
                CloseHandle(h);
                FreeLibrary(h_cpl);
                return -1;
            }
        }

        CloseHandle(h);
        OutputDebugStringA("✅ Media ejected\n");

        // 步骤8: 移除挂载点
        OutputDebugStringA("Step 8: Removing mount point...\n");
        typedef BOOL (WINAPI *ImDiskRemoveMountPointProc)(LPCWSTR);
        ImDiskRemoveMountPointProc RemoveMountPoint = (ImDiskRemoveMountPointProc)GetProcAddress(h_cpl, "ImDiskRemoveMountPoint");
        if (RemoveMountPoint && !RemoveMountPoint(mount_point)) {
            OutputDebugStringA("❌ Cannot remove mount point\n");
            FreeLibrary(h_cpl);
            return -1;
        }

        OutputDebugStringA("✅ Mount point removed\n");

        // 释放 imdisk.cpl 库
        FreeLibrary(h_cpl);
        OutputDebugStringA("✅ imdisk.cpl library released\n");
        OutputDebugStringA("✅ Unmount operation completed successfully\n");

        return 0; // 成功
    }

    // 字符串转换辅助函数
    std::wstring utf8_to_wstring(const std::string& utf8_str) {
        if (utf8_str.empty()) return std::wstring();

        int size_needed = MultiByteToWideChar(CP_UTF8, 0, utf8_str.c_str(), -1, nullptr, 0);
        if (size_needed <= 0) return std::wstring();

        std::wstring result(size_needed - 1, 0);
        MultiByteToWideChar(CP_UTF8, 0, utf8_str.c_str(), -1, &result[0], size_needed);
        return result;
    }

    std::string wstring_to_utf8(const std::wstring& wide_str) {
        if (wide_str.empty()) return std::string();

        int size_needed = WideCharToMultiByte(CP_UTF8, 0, wide_str.c_str(), -1, nullptr, 0, nullptr, nullptr);
        if (size_needed <= 0) return std::string();

        std::string result(size_needed - 1, 0);
        WideCharToMultiByte(CP_UTF8, 0, wide_str.c_str(), -1, &result[0], size_needed, nullptr, nullptr);
        return result;
    }
}

// ========================================
// 006_Dll标准接口实现
// ========================================

/*
 * 挂载虚拟磁盘 - 符合006_Dll标准
 */
const char* MountVirtualDisk(
    const char* params,
    ProgressCallback progressCallback,
    const char* taskId,
    QueryTaskControlCallback queryTaskControlCb)
{
#if 0
    // 最早的调试输出 - 使用printf确保能看到
    printf("DEBUG: MountVirtualDisk - Function entry\n");
    fflush(stdout);
    OutputDebugStringA("DEBUG: MountVirtualDisk - Function entry\n");

    try {
        // 转换参数为std::string
        std::string params_str = params ? params : "";
        std::string taskId_str = taskId ? taskId : "";

        // 简化调试输出
        OutputDebugStringA("DEBUG: MountVirtualDisk called\n");

        OutputDebugStringA("DEBUG: About to register task\n");
        // 注册任务
        register_task(taskId_str);
        OutputDebugStringA("DEBUG: Task registered successfully\n");

        // 内层try块开始
        try {
            // 解析输入参数
            if (params_str.empty() || params_str.find("{") == std::string::npos) {
                unregister_task(taskId_str);
                return copy_response_to_buffer(create_error_response("Invalid JSON input"));
            }

            // 报告开始进度
            report_progress(taskId_str, 0, progressCallback, "Starting mount operation");

            // 检查任务控制状态
            if (check_task_control(taskId_str, queryTaskControlCb, progressCallback)) {
                unregister_task(taskId_str);
                return copy_response_to_buffer(create_cancelled_response("Task cancelled by user"));
            }

            // 提取参数（使用全局函数）
            std::string file_path = get_json_string_value(params_str, "file_path", "");
            std::string drive = get_json_string_value(params_str, "drive", "");
            bool readonly = get_json_bool_value(params_str, "readonly", false);
            int partition_num = get_json_int_value(params_str, "partition", 1);
            bool auto_assign = get_json_bool_value(params_str, "auto_assign", false);

            // 参数验证
            if (file_path.empty()) {
                unregister_task(taskId_str);
                return copy_response_to_buffer(create_error_response("Missing required parameter: file_path"));
            }

            // 报告参数解析完成
            report_progress(taskId_str, 20, progressCallback, "Parameters parsed successfully");

            // 检查文件是否存在
            std::wstring wide_file_path = utf8_to_wstring(file_path);
            if (GetFileAttributesW(wide_file_path.c_str()) == INVALID_FILE_ATTRIBUTES) {
                unregister_task(taskId_str);
                return copy_response_to_buffer(create_error_response("Image file not found: " + file_path));
            }

            // 报告文件验证完成
            report_progress(taskId_str, 40, progressCallback, "File validation completed");

            // 检查任务控制状态
            if (check_task_control(taskId_str, queryTaskControlCb, progressCallback)) {
                unregister_task(taskId_str);
                return copy_response_to_buffer(create_cancelled_response("Task cancelled by user"));
            }

            // 实现双重挂载策略：先尝试ImDisk直接挂载，失败则使用DiscUtils
            std::wstring wide_drive_letter;
            if (!drive.empty()) {
                wide_drive_letter = utf8_to_wstring(drive);
            }
            else {
                // 自动分配驱动器号
                wide_drive_letter = L"Z:";
            }

            // 检测文件格式
            std::string file_ext = file_path.substr(file_path.find_last_of(".") + 1);
            std::transform(file_ext.begin(), file_ext.end(), file_ext.begin(), ::tolower);

            bool need_discutils = (file_ext == "vmdk" || file_ext == "vhdx" ||
                file_ext == "vdi" || file_ext == "dmg" || file_ext == "xva");

            int mount_result = -1;
            std::wstring cmdline;

            // 第一次尝试：ImDisk直接挂载（适用于VHD、ISO、IMG等）
            if (!need_discutils) {
                cmdline = L"imdisk -a -t file -f \"" + wide_file_path + L"\" -m \"" + wide_drive_letter + L"\"";
                if (readonly) {
                    cmdline += L" -o ro";
                }
                if (partition_num > 1) {
                    cmdline += L" -p " + std::to_wstring(partition_num);
                }

                // 尝试ImDisk直接挂载
                mount_result = execute_mount_command(cmdline);
            }

            // 第二次尝试：如果ImDisk失败或需要DiscUtils，使用DiscUtils挂载
            if (mount_result != 0) {
                // 构建DiscUtils挂载命令（使用与MountImg.c相同的格式）
                __int64 pipe_id = ((__int64)GetTickCount() << 16) | (GetCurrentProcessId() & 0xFFFF);
                std::wstring partition_param = (partition_num > 1) ?
                    (L" /partition=" + std::to_wstring(partition_num)) : L"";
                std::wstring readonly_param = readonly ? L" /readonly" : L"";

                // 启动DiscUtilsDevio服务（使用正确的格式）
                wchar_t discutils_cmd[1024];
                _snwprintf(discutils_cmd, _countof(discutils_cmd),
                    L"DiscUtilsDevio /name=ImDisk%I64x%s /filename=\"%s\"%s",
                    pipe_id, partition_param.c_str(), wide_file_path.c_str(), readonly_param.c_str());

                mount_result = execute_discutils_mount(std::wstring(discutils_cmd), wide_drive_letter, pipe_id, readonly);
            }

            // 检查挂载结果并生成响应
            std::string response;
            if (mount_result == 0) {
                // 挂载成功，生成成功响应
                OutputDebugStringA("DEBUG: About to generate response\n");
                response = create_mount_success_response(drive, file_path);
                OutputDebugStringA("DEBUG: Success response created\n");
            }
            else {
                // 挂载失败，生成错误响应
                std::string error_msg = "Mount operation failed. ";
                if (need_discutils) {
                    error_msg += "Both ImDisk and DiscUtils mount attempts failed. ";
                    error_msg += "Please ensure DiscUtilsDevio.exe is available and the file format is supported.";
                }
                else {
                    error_msg += "ImDisk mount failed. The file may be corrupted or in an unsupported format.";
                }
                response = create_error_response(error_msg);
            }

            // 报告挂载操作完成
            report_progress(taskId_str, 90, progressCallback, "Mount operation completed");

            OutputDebugStringA("DEBUG: About to report task completion\n");
            // 报告任务完成
            report_progress(taskId_str, 100, progressCallback, "Task completed successfully");
            OutputDebugStringA("DEBUG: Task completion reported\n");

            OutputDebugStringA("DEBUG: About to unregister task\n");
            unregister_task(taskId_str);
            OutputDebugStringA("DEBUG: Task unregistered\n");

            OutputDebugStringA("DEBUG: About to return response\n");
            return copy_response_to_buffer(response);

        }
        catch (const std::exception& e) {
            OutputDebugStringA("DEBUG: Inner exception caught\n");
            unregister_task(taskId_str);
            return copy_response_to_buffer(create_error_response(std::string("Exception: ") + e.what()));
        }
        catch (...) {
            OutputDebugStringA("DEBUG: Inner unknown exception caught\n");
            unregister_task(taskId_str);
            return copy_response_to_buffer(create_error_response("Unknown exception occurred"));
        }

    }
    catch (const std::exception& e) {
        OutputDebugStringA("DEBUG: Outer exception caught\n");
        return copy_response_to_buffer(create_error_response(std::string("Critical exception: ") + e.what()));
    }
    catch (...) {
        OutputDebugStringA("DEBUG: Outer unknown exception caught\n");
        return copy_response_to_buffer(create_error_response("Critical unknown exception occurred"));
    }
#else

    // 输出API调用调试信息
    OutputDebugStringA("========================================\n");
    OutputDebugStringA("VirtualDiskLib: MountVirtualDisk called (006_Dll Standard with Old Implementation)\n");
    OutputDebugStringA("========================================\n");

    char debug_info[512];
    sprintf_s(debug_info, sizeof(debug_info), "JSON Input: %s\n", params ? params : "NULL");
    OutputDebugStringA(debug_info);
    sprintf_s(debug_info, sizeof(debug_info), "Task ID: %s\n", taskId ? taskId : "NULL");
    OutputDebugStringA(debug_info);

    try {
        // 转换参数为std::string
        std::string params_str = params ? params : "";
        std::string taskId_str = taskId ? taskId : "";

        // 注册任务
        register_task(taskId_str);

        // === 第1步: 参数验证和JSON解析 ===
        if (params_str.empty() || params_str.find("{") == std::string::npos) {
            unregister_task(taskId_str);
            return copy_response_to_buffer(create_error_response("Invalid JSON input"));
        }

        // 报告开始进度
        report_progress(taskId_str, 0, progressCallback, "Starting mount operation");

        // 检查任务控制状态
        if (check_task_control(taskId_str, queryTaskControlCb, progressCallback)) {
            unregister_task(taskId_str);
            return copy_response_to_buffer(create_cancelled_response("Task cancelled by user"));
        }

        OutputDebugStringA("=== Step 1: Parsing JSON Input ===\n");

        // 提取参数（使用全局函数）
        std::string file_path = get_json_string_value(params_str, "file_path", "");
        std::string drive = get_json_string_value(params_str, "drive", "");
        bool readonly = get_json_bool_value(params_str, "readonly", false);
        int partition_num = get_json_int_value(params_str, "partition", 1);

        // 输出解析结果
        sprintf_s(debug_info, sizeof(debug_info), "Parsed: file_path=%s, drive=%s, readonly=%d, partition=%d\n",
                file_path.c_str(), drive.c_str(), readonly, partition_num);
        OutputDebugStringA(debug_info);

        // 验证必需参数
        if (file_path.empty()) {
            OutputDebugStringA("ERROR: file_path is required\n");
            unregister_task(taskId_str);
            return copy_response_to_buffer(create_error_response("file_path is required"));
        }

        if (drive.empty()) {
            OutputDebugStringA("ERROR: drive is required\n");
            unregister_task(taskId_str);
            return copy_response_to_buffer(create_error_response("drive is required"));
        }

        // 报告参数解析完成
        report_progress(taskId_str, 20, progressCallback, "Parameters parsed successfully");

        // === 第2步: 文件验证 ===
        OutputDebugStringA("=== Step 2: File Validation ===\n");

        // 检查文件是否存在
        std::wstring wide_file_path = utf8_to_wstring(file_path);
        if (GetFileAttributesW(wide_file_path.c_str()) == INVALID_FILE_ATTRIBUTES) {
            OutputDebugStringA("ERROR: Image file not found\n");
            unregister_task(taskId_str);
            return copy_response_to_buffer(create_error_response("Image file not found: " + file_path));
        }

        // 报告文件验证完成
        report_progress(taskId_str, 40, progressCallback, "File validation completed");

        // 检查任务控制状态
        if (check_task_control(taskId_str, queryTaskControlCb, progressCallback)) {
            unregister_task(taskId_str);
            return copy_response_to_buffer(create_cancelled_response("Task cancelled by user"));
        }

        // === 第2步: 设置MountImg.c全局变量（完全参照VirtualDiskLib_Old.cpp）===
        OutputDebugStringA("=== Step 2: Setting MountImg.c Global Variables ===\n");

        // 将JSON参数映射到MountImg.c全局变量（参照SetMountImgParameters函数）
        // 文件路径转换（使用已定义的wide_file_path变量）
        wcscpy_s(filename, MAX_PATH, wide_file_path.c_str());

        // 驱动器号转换
        std::wstring wide_drive = utf8_to_wstring(drive);
        wcscpy_s(::drive, MAX_PATH + 2, wide_drive.c_str());

        // 挂载选项设置
        ::readonly = readonly;
        ::partition = partition_num;

        sprintf_s(debug_info, sizeof(debug_info), "Set: filename=%S, drive=%S, readonly=%d, partition=%d\n",
                filename, ::drive, ::readonly, ::partition);
        OutputDebugStringA(debug_info);

        // 报告参数设置完成
        report_progress(taskId_str, 60, progressCallback, "MountImg parameters set");

        // === 第3步: 执行挂载操作（完全参照ExecuteMountOperation函数）===
        OutputDebugStringA("=== Step 3: Executing Mount Operation ===\n");

        sprintf_s(debug_info, sizeof(debug_info), "Mounting: %S -> %S (readonly=%d, partition=%d)\n",
                filename, ::drive, ::readonly, ::partition);
        OutputDebugStringA(debug_info);

        // 直接调用MountImg.c的挂载函数（完全参照ExecuteMountOperation）
        int mount_error = 0;

        // === 第0步: 初始化 ImDisk ===
        OutputDebugStringA("Step 0: Initializing ImDisk...\n");
        if (InitializeImDisk() != 0) {
            OutputDebugStringA("ERROR: Failed to initialize ImDisk\n");
            mount_error = 1;
        } else {
            OutputDebugStringA("✅ ImDisk initialization successful\n");
        }

        if (mount_error == 0) {
            // === 第1步: 等待挂载互斥锁（参考Mount函数第739行） ===
            OutputDebugStringA("Step 1: Waiting for mount mutex...\n");
            if (mount_mutex) {
                WaitForSingleObject(mount_mutex, INFINITE);
            }

            // === 第2步: 设置挂载目标（参考Mount函数第742行） ===
            if (mount_point) {
                wcscpy_s(::drive, MAX_PATH + 2, mountdir);
            }

            // === 第3步: 尝试ImDisk挂载（参考Mount函数第744行） ===
            OutputDebugStringA("Step 3: Attempting ImDisk mount...\n");

            BYTE no_check_fs = new_file || !net_installed;
            sprintf_s(debug_info, sizeof(debug_info), "ImDisk mount parameters: no_check_fs=%s (new_file=%s, net_installed=%s)\n",
                    no_check_fs ? "TRUE" : "FALSE",
                    new_file ? "TRUE" : "FALSE",
                    net_installed ? "TRUE" : "FALSE");
            OutputDebugStringA(debug_info);

            mount_error = Imdisk_Mount(no_check_fs);

            sprintf_s(debug_info, sizeof(debug_info), "ImDisk mount result: %d\n", mount_error);
            OutputDebugStringA(debug_info);

            // === 第4步: 如果ImDisk失败，尝试DiscUtils（参考Mount函数第745-748行） ===
            if (mount_error && !new_file && net_installed) {
                OutputDebugStringA("Step 4: ImDisk failed, trying DiscUtils mount...\n");
                sprintf_s(debug_info, sizeof(debug_info), "DiscUtils conditions: error=%d, new_file=%s, net_installed=%s\n",
                        mount_error, new_file ? "TRUE" : "FALSE", net_installed ? "TRUE" : "FALSE");
                OutputDebugStringA(debug_info);

                device_number = -1;
                mount_error = DiscUtils_Mount();

                sprintf_s(debug_info, sizeof(debug_info), "DiscUtils mount result: %d\n", mount_error);
                OutputDebugStringA(debug_info);
            } else {
                sprintf_s(debug_info, sizeof(debug_info), "Skipping DiscUtils: error=%d, new_file=%s, net_installed=%s\n",
                        mount_error, new_file ? "TRUE" : "FALSE", net_installed ? "TRUE" : "FALSE");
                OutputDebugStringA(debug_info);
            }

            // === 第5步: 保存启动配置（参考Mount函数第767行） ===
            if (win_boot) {
                OutputDebugStringA("Step 5: Saving boot configuration...\n");
                reg_save();
            }

            // === 第6步: 释放互斥锁 ===
            if (mount_mutex) {
                ReleaseMutex(mount_mutex);
            }
        }

        // 报告挂载操作完成
        report_progress(taskId_str, 80, progressCallback, "Mount operation completed");

        // === 第4步: 验证挂载结果（完全参照VerifyMountResult函数）===
        OutputDebugStringA("=== Step 4: Verifying Mount Result ===\n");

        int verify_result = -1;
        if (mount_error == 0) {
            // 验证挂载结果（参照VerifyMountResult函数）
            WCHAR temp_drive[MAX_PATH + 2];
            wcscpy_s(temp_drive, MAX_PATH + 2, ::drive);

            // 如果是挂载点，添加反斜杠
            if (mount_point) {
                PathAddBackslashW(temp_drive);
            }

            // 循环验证挂载结果（参考VirtualDiskLib_Old.cpp的验证逻辑）
            for (int i = 0; i < 50; i++) {  // 最多等待5秒
                DWORD attr = GetFileAttributesW(temp_drive);
                if (attr != INVALID_FILE_ATTRIBUTES) {
                    verify_result = 0;  // 验证成功
                    break;
                }
                Sleep(100);  // 等待100ms
            }

            sprintf_s(debug_info, sizeof(debug_info), "Mount verification result: %d\n", verify_result);
            OutputDebugStringA(debug_info);
        }

        std::string response;
        if (mount_error == 0 && verify_result == 0) {
            // 挂载成功
            OutputDebugStringA("✅ Mount verification successful\n");
            response = create_mount_success_response(drive, file_path);
        } else {
            // 挂载失败
            OutputDebugStringA("❌ Mount operation or verification failed\n");
            std::string error_msg = "Mount operation failed. ";
            if (mount_error != 0) {
                error_msg += "Mount function returned error code " + std::to_string(mount_error) + ". ";
            }
            if (verify_result != 0) {
                if (verify_result == -1) {
                    error_msg += "Unrecognized volume. ";
                } else {
                    error_msg += "Mount verification timeout. ";
                }
            }
            response = create_error_response(error_msg);
        }

        // 报告任务完成
        report_progress(taskId_str, 100, progressCallback, "Task completed successfully");
        unregister_task(taskId_str);

        OutputDebugStringA("========================================\n");
        OutputDebugStringA("VirtualDiskLib: MountVirtualDisk completed (006_Dll Standard with Old Implementation)\n");
        sprintf_s(debug_info, sizeof(debug_info), "Final return result: %s\n", mount_error == 0 ? "SUCCESS" : "FAILED");
        OutputDebugStringA(debug_info);
        OutputDebugStringA("========================================\n");

        return copy_response_to_buffer(response);

    } catch (const std::exception& e) {
        OutputDebugStringA("ERROR: Exception caught in MountVirtualDisk\n");
        sprintf_s(debug_info, sizeof(debug_info), "Exception: %s\n", e.what());
        OutputDebugStringA(debug_info);
        unregister_task(taskId ? taskId : "");
        return copy_response_to_buffer(create_error_response(std::string("Exception: ") + e.what()));
    } catch (...) {
        OutputDebugStringA("ERROR: Unknown exception caught in MountVirtualDisk\n");
        unregister_task(taskId ? taskId : "");
        return copy_response_to_buffer(create_error_response("Unknown exception occurred"));
    }

#endif
}

///*
// * 卸载虚拟磁盘
// */
//VIRTUALDISKLIB_API int UnmountVirtualDisk_Old(const char* jsonInput, char* jsonOutput, int bufferSize)
//{
//#if 0
//    // 参数验证
//    if (!jsonInput || !jsonOutput || bufferSize <= 0) {
//        return VDL_ERROR_INVALID_PARAMETER;
//    }
//
//    // 清空输出缓冲区
//    memset(jsonOutput, 0, bufferSize);
//
//    //// 检查初始化状态
//    //if (!g_bInitialized) {
//    //    return GenerateErrorResponse(VDL_ERROR_UNMOUNT_FAILED, 
//    //        "Library not initialized", jsonOutput, bufferSize);
//    //}
//
//    EnterCriticalSection(&g_cs);
//
//    MountRequest request;
//    MountResponse response;
//    int result = VDL_SUCCESS;
//
//    // 初始化结构
//    memset(&request, 0, sizeof(request));
//    memset(&response, 0, sizeof(response));
//
//    // 解析JSON请求
//    if (ParseMountRequest(jsonInput, &request) != 0) {
//        result = VDL_ERROR_INVALID_JSON;
//        strcpy_s(response.error_message, sizeof(response.error_message),
//            "Invalid JSON format");
//        goto cleanup;
//    }
//
//    // 验证驱动器号
//    if (strlen(request.drive) == 0) {
//        result = VDL_ERROR_INVALID_PARAMETER;
//        strcpy_s(response.error_message, sizeof(response.error_message),
//            "Drive letter is required");
//        goto cleanup;
//    }
//
//    // 执行卸载操作 - 使用MountImg.c中的直接函数调用
//    OutputDebugStringA("=== Starting Unmount Operation ===\n");
//    char debug_info[256];
//    sprintf(debug_info, "Unmounting drive: %s\n", request.drive);
//    OutputDebugStringA(debug_info);
//
//    // 转换驱动器号为宽字符
//    WCHAR wDrive[8];
//    MultiByteToWideChar(CP_UTF8, 0, request.drive, -1, wDrive, 8);
//
//    // 直接实现卸载逻辑 (基于MountImg.c第424-438行的UnmountDrive函数)
//    WCHAR cmdLine[MAX_PATH + 20];
//    _snwprintf(cmdLine, _countof(cmdLine), L"ImDisk-Dlg RM \"%s\"", wDrive);
//
//    sprintf(debug_info, "Executing command: ImDisk-Dlg RM \"%S\"\n", wDrive);
//    OutputDebugStringA(debug_info);
//
//    // 调用MountImg.c中的start_process函数
//    int unmountResult = start_process(cmdLine, TRUE);
//
//    sprintf(debug_info, "Unmount result: %d\n", unmountResult);
//    OutputDebugStringA(debug_info);
//
//    if (unmountResult == 0) {
//        // 卸载成功
//        response.success = 1;
//        response.error_code = 0;
//        strcpy_s(response.drive_letter, sizeof(response.drive_letter), request.drive);
//        strcpy_s(response.message, sizeof(response.message), "Unmount successful");
//        OutputDebugStringA("✅ Unmount operation successful\n");
//    }
//    else {
//        // 卸载失败
//        result = VDL_ERROR_UNMOUNT_FAILED;
//        _snprintf_s(response.error_message, sizeof(response.error_message), _TRUNCATE,
//            "Unmount operation failed with code %d", unmountResult);
//        OutputDebugStringA("❌ Unmount operation failed\n");
//    }
//
//cleanup:
//    response.error_code = result;
//
//    // 生成响应JSON
//    int jsonResult = GenerateMountResponse(&response, jsonOutput, bufferSize);
//    if (jsonResult != 0) {
//        result = VDL_ERROR_BUFFER_TOO_SMALL;
//    }
//
//    LeaveCriticalSection(&g_cs);
//    return result;
//#else
//    // 参数验证
//    if (!jsonInput || !jsonOutput || bufferSize <= 0) {
//        return VDL_ERROR_INVALID_PARAMETER;
//    }
//
//    // 清空输出缓冲区
//    memset(jsonOutput, 0, bufferSize);
//
//    //// 检查初始化状态
//    //if (!g_bInitialized) {
//    //    return GenerateErrorResponse(VDL_ERROR_UNMOUNT_FAILED, 
//    //        "Library not initialized", jsonOutput, bufferSize);
//    //}
//
//    EnterCriticalSection(&g_cs);
//
//    MountRequest request;
//    MountResponse response;
//    int result = VDL_SUCCESS;
//
//    struct { IMDISK_CREATE_DATA icd; WCHAR buff[MAX_PATH + 15]; } create_data = {};
//    HMODULE h_cpl = NULL;
//
//    // 初始化结构
//    memset(&request, 0, sizeof(request));
//    memset(&response, 0, sizeof(response));
//
//    // 解析JSON请求
//    if (ParseMountRequest(jsonInput, &request) != 0) {
//        result = VDL_ERROR_INVALID_JSON;
//        strcpy_s(response.error_message, sizeof(response.error_message),
//            "Invalid JSON format");
//        goto cleanup;
//    }
//
//    // 验证驱动器号
//    if (strlen(request.drive) == 0) {
//        result = VDL_ERROR_INVALID_PARAMETER;
//        strcpy_s(response.error_message, sizeof(response.error_message),
//            "Drive letter is required");
//        goto cleanup;
//    }
//
//    // 执行卸载操作 - 直接实现 ImDisk-Dlg.c RM 代码块逻辑
//    OutputDebugStringA("=== Starting Unmount Operation (Direct RM Logic) ===\n");
//    char debug_info[256];
//    sprintf(debug_info, "Unmounting drive: %s\n", request.drive);
//    OutputDebugStringA(debug_info);
//
//    // 转换驱动器号为宽字符
//    WCHAR mount_point[8];
//    MultiByteToWideChar(CP_UTF8, 0, request.drive, -1, mount_point, 8);
//
//    // 声明所有变量 (避免 goto 跳过初始化的问题)
//    HANDLE h = INVALID_HANDLE_VALUE;
//    DWORD access_list[] = { GENERIC_READ | GENERIC_WRITE, GENERIC_READ, GENERIC_WRITE };
//    int n_access;
//    //struct { IMDISK_CREATE_DATA icd; WCHAR buff[MAX_PATH + 15]; } create_data = {};
//    DWORD dw;
//    DEV_BROADCAST_VOLUME dbv;  // 简单声明，避免复杂初始化
//    DWORD_PTR dwp;
//    //HMODULE h_cpl = NULL;
//
//    // 初始化 dbv 结构
//    memset(&dbv, 0, sizeof(dbv));
//    dbv.dbcv_size = sizeof(dbv);
//    dbv.dbcv_devicetype = DBT_DEVTYP_VOLUME;
//
//    // 参考 ImDisk-Dlg.c 第425-429行: 动态加载 ImDisk 函数
//    OutputDebugStringA("Step 0: Loading ImDisk functions...\n");
//    h_cpl = LoadLibraryA("imdisk.cpl");
//    if (!h_cpl) {
//        result = VDL_ERROR_UNMOUNT_FAILED;
//        strcpy_s(response.error_message, sizeof(response.error_message), "Cannot find imdisk.cpl");
//        OutputDebugStringA("❌ Cannot find imdisk.cpl\n");
//        goto cleanup;
//    }
//
//    // 获取 ImDiskOpenDeviceByMountPoint 函数指针
//    typedef HANDLE(WINAPI* ImDiskOpenDeviceByMountPointProc)(LPCWSTR, DWORD);
//    ImDiskOpenDeviceByMountPointProc ImDiskOpenDeviceByMountPoint =
//        (ImDiskOpenDeviceByMountPointProc)GetProcAddress(h_cpl, "ImDiskOpenDeviceByMountPoint");
//
//    if (!ImDiskOpenDeviceByMountPoint) {
//        result = VDL_ERROR_UNMOUNT_FAILED;
//        strcpy_s(response.error_message, sizeof(response.error_message), "Cannot find ImDiskOpenDeviceByMountPoint function");
//        OutputDebugStringA("❌ Cannot find ImDiskOpenDeviceByMountPoint function\n");
//        FreeLibrary(h_cpl);
//        goto cleanup;
//    }
//
//    OutputDebugStringA("✅ ImDisk functions loaded successfully\n");
//
//    // 参考 ImDisk-Dlg.c 第449-454行: 打开设备
//    OutputDebugStringA("Step 1: Opening ImDisk device...\n");
//    for (n_access = 0; n_access < _countof(access_list); n_access++) {
//        h = (HANDLE)ImDiskOpenDeviceByMountPoint(mount_point, access_list[n_access]);
//        if (h != INVALID_HANDLE_VALUE) break;
//    }
//
//    if (h == INVALID_HANDLE_VALUE) {
//        result = VDL_ERROR_UNMOUNT_FAILED;
//        strcpy_s(response.error_message, sizeof(response.error_message), "Cannot open ImDisk device");
//        OutputDebugStringA("❌ Cannot open ImDisk device\n");
//        goto cleanup;
//    }
//
//    sprintf(debug_info, "✅ Device opened with access mode %d\n", n_access);
//    OutputDebugStringA(debug_info);
//
//    // 参考 ImDisk-Dlg.c 第455-460行: 查询设备信息
//
//    OutputDebugStringA("Step 2: Querying device info...\n");
//    if (!DeviceIoControl(h, IOCTL_IMDISK_QUERY_DEVICE, NULL, 0, &create_data, sizeof(create_data), &dw, NULL)) {
//        result = VDL_ERROR_UNMOUNT_FAILED;
//        _snprintf_s(response.error_message, sizeof(response.error_message), _TRUNCATE,
//            "%s is not an ImDisk virtual disk", request.drive);
//        OutputDebugStringA("❌ Device is not an ImDisk virtual disk\n");
//        CloseHandle(h);
//        goto cleanup;
//    }
//
//    OutputDebugStringA("✅ Device info queried successfully\n");
//
//    // 参考 ImDisk-Dlg.c 第469-472行: 发送设备移除通知
//    if (mount_point[1] == L':' && !mount_point[2]) {
//        dbv.dbcv_unitmask = 1 << (mount_point[0] - L'A');
//
//        OutputDebugStringA("Step 3: Sending device remove notification...\n");
//        SendMessageTimeout(HWND_BROADCAST, WM_DEVICECHANGE, DBT_DEVICEREMOVEPENDING,
//            (LPARAM)&dbv, SMTO_BLOCK | SMTO_ABORTIFHUNG, 4000, &dwp);
//        OutputDebugStringA("✅ Device remove notification sent\n");
//    }
//
//    // 参考 ImDisk-Dlg.c 第474-475行: 刷新文件缓冲区
//    OutputDebugStringA("Step 4: Flushing file buffers...\n");
//    FlushFileBuffers(h);
//    OutputDebugStringA("✅ File buffers flushed\n");
//
//    // 参考 ImDisk-Dlg.c 第477-481行: 锁定卷
//    OutputDebugStringA("Step 5: Locking volume...\n");
//    if (!DeviceIoControl(h, FSCTL_LOCK_VOLUME, NULL, 0, NULL, 0, &dw, NULL)) {
//        OutputDebugStringA("⚠️ Warning: Cannot lock volume, continuing...\n");
//    }
//    else {
//        OutputDebugStringA("✅ Volume locked\n");
//    }
//
//    // 参考 ImDisk-Dlg.c 第483-484行: 卸载卷
//    OutputDebugStringA("Step 6: Dismounting volume...\n");
//    DeviceIoControl(h, FSCTL_DISMOUNT_VOLUME, NULL, 0, NULL, 0, &dw, NULL);
//    OutputDebugStringA("✅ Volume dismounted\n");
//
//    // 再次锁定卷
//    DeviceIoControl(h, FSCTL_LOCK_VOLUME, NULL, 0, NULL, 0, &dw, NULL);
//
//    // 参考 ImDisk-Dlg.c 第497-501行: 弹出媒体
//    OutputDebugStringA("Step 7: Ejecting media...\n");
//    if (!DeviceIoControl(h, IOCTL_STORAGE_EJECT_MEDIA, NULL, 0, NULL, 0, &dw, NULL)) {
//        // 尝试强制移除设备 (重用已加载的 h_cpl)
//        typedef BOOL(WINAPI* ImDiskForceRemoveDeviceProc)(HANDLE, DWORD);
//        ImDiskForceRemoveDeviceProc ForceRemove = (ImDiskForceRemoveDeviceProc)GetProcAddress(h_cpl, "ImDiskForceRemoveDevice");
//        if (ForceRemove && !ForceRemove(h, 0)) {
//            result = VDL_ERROR_UNMOUNT_FAILED;
//            strcpy_s(response.error_message, sizeof(response.error_message), "Cannot remove device");
//            OutputDebugStringA("❌ Cannot remove device\n");
//            CloseHandle(h);
//            goto cleanup;
//        }
//    }
//
//    CloseHandle(h);
//    OutputDebugStringA("✅ Media ejected\n");
//
//    // 参考 ImDisk-Dlg.c 第504-508行: 移除挂载点
//    OutputDebugStringA("Step 8: Removing mount point...\n");
//    // 重用已加载的 h_cpl
//    typedef BOOL(WINAPI* ImDiskRemoveMountPointProc)(LPCWSTR);
//    ImDiskRemoveMountPointProc RemoveMountPoint = (ImDiskRemoveMountPointProc)GetProcAddress(h_cpl, "ImDiskRemoveMountPoint");
//    if (RemoveMountPoint && !RemoveMountPoint(mount_point)) {
//        result = VDL_ERROR_UNMOUNT_FAILED;
//        strcpy_s(response.error_message, sizeof(response.error_message), "Cannot remove mount point");
//        OutputDebugStringA("❌ Cannot remove mount point\n");
//        goto cleanup;
//    }
//
//    OutputDebugStringA("✅ Mount point removed\n");
//
//    // 卸载成功
//    response.success = 1;
//    response.error_code = 0;
//    strcpy_s(response.drive_letter, sizeof(response.drive_letter), request.drive);
//    strcpy_s(response.message, sizeof(response.message), "Unmount successful");
//    OutputDebugStringA("✅ Unmount operation completed successfully\n");
//
//cleanup:
//    // 释放 imdisk.cpl 库
//    if (h_cpl) {
//        FreeLibrary(h_cpl);
//        OutputDebugStringA("✅ imdisk.cpl library released\n");
//    }
//
//    response.error_code = result;
//
//    // 生成响应JSON
//    int jsonResult = GenerateMountResponse(&response, jsonOutput, bufferSize);
//    if (jsonResult != 0) {
//        result = VDL_ERROR_BUFFER_TOO_SMALL;
//    }
//
//    LeaveCriticalSection(&g_cs);
//    return result;
//#endif
//}

/*
 * 卸载虚拟磁盘 - 符合006_Dll标准
 */
const char* UnmountVirtualDisk(
    const char* params,
    ProgressCallback progressCallback,
    const char* taskId,
    QueryTaskControlCallback queryTaskControlCb)
{
    // 转换参数为std::string
    std::string params_str = params ? params : "";
    std::string taskId_str = taskId ? taskId : "";

    // 注册任务
    register_task(taskId_str);

    OutputDebugStringA("=== Starting Unmount Operation (006_Dll Standard) ===\n");

    try {
        // 解析输入参数
        if (params_str.empty() || params_str.find("{") == std::string::npos) {
            unregister_task(taskId_str);
            return copy_response_to_buffer(create_error_response("Invalid JSON input"));
        }

        // 报告开始进度
        report_progress(taskId_str, 0, progressCallback, "Starting unmount operation");

        // 检查任务控制状态
        if (check_task_control(taskId_str, queryTaskControlCb, progressCallback)) {
            unregister_task(taskId_str);
            return copy_response_to_buffer(create_cancelled_response("Task cancelled by user"));
        }

        // 提取参数
        std::string drive_letter = get_json_string_value(params_str, "drive", "");
        bool force_unmount = get_json_bool_value(params_str, "force", false);

        // 参数验证
        if (drive_letter.empty()) {
            unregister_task(taskId_str);
            return copy_response_to_buffer(create_error_response("Missing required parameter: drive"));
        }

        // 报告参数解析完成
        report_progress(taskId_str, 20, progressCallback, "Parameters parsed successfully");

        // 检查驱动器是否存在
        std::wstring wide_drive = utf8_to_wstring(drive_letter);
        UINT drive_type = GetDriveTypeW(wide_drive.c_str());
        if (drive_type == DRIVE_NO_ROOT_DIR) {
            unregister_task(taskId_str);
            return copy_response_to_buffer(create_error_response("Drive not found: " + drive_letter));
        }

        // 报告驱动器验证完成
        report_progress(taskId_str, 40, progressCallback, "Drive validation completed");

        // 检查任务控制状态
        if (check_task_control(taskId_str, queryTaskControlCb, progressCallback)) {
            unregister_task(taskId_str);
            return copy_response_to_buffer(create_cancelled_response("Task cancelled by user"));
        }

        char debug_info[256];
        sprintf_s(debug_info, sizeof(debug_info), "Unmounting drive: %s\n", drive_letter.c_str());
        OutputDebugStringA(debug_info);

        // 转换驱动器号为宽字符
        WCHAR mount_point[8];
        MultiByteToWideChar(CP_UTF8, 0, drive_letter.c_str(), -1, mount_point, 8);

        // 报告开始卸载
        report_progress(taskId_str, 60, progressCallback, "Starting unmount process");

        // 执行卸载操作 - 使用VirtualDiskLib_Old.cpp的实现
        int unmount_result = execute_unmount_with_imdisk_api(mount_point, force_unmount);

        // 报告卸载完成
        report_progress(taskId_str, 100, progressCallback, "Unmount operation completed successfully");

        // 生成响应
        std::string response;
        if (unmount_result == 0) {
            char buffer[1024];
            sprintf_s(buffer, sizeof(buffer),
                "{\"status\":\"success\",\"message\":\"Unmount operation completed successfully\","
                "\"unmounted_drive\":\"%s\",\"cleanup_completed\":true}",
                drive_letter.c_str());
            response = buffer;
        } else {
            char buffer[1024];
            sprintf_s(buffer, sizeof(buffer),
                "{\"status\":\"error\",\"message\":\"Unmount operation failed\",\"error_code\":%d}",
                unmount_result);
            response = buffer;
        }

        unregister_task(taskId_str);
        return copy_response_to_buffer(response);

    } catch (const std::exception& e) {
        unregister_task(taskId_str);
        return copy_response_to_buffer(create_error_response(std::string("Exception: ") + e.what()));
    } catch (...) {
        unregister_task(taskId_str);
        return copy_response_to_buffer(create_error_response("Unknown exception occurred"));
    }
}

/*
 * 获取挂载状态 - 符合006_Dll标准
 */
const char* GetMountStatus(
    const char* params,
    ProgressCallback progressCallback,
    const char* taskId,
    QueryTaskControlCallback queryTaskControlCb)
{
    // 转换参数为std::string
    std::string params_str = params ? params : "";
    std::string taskId_str = taskId ? taskId : "";

    // 注册任务
    register_task(taskId_str);

    try {
        // 解析输入参数
        if (params_str.empty() || params_str.find("{") == std::string::npos) {
            unregister_task(taskId_str);
            return copy_response_to_buffer(create_error_response("Invalid JSON input"));
        }

        // 报告开始进度
        report_progress(taskId_str, 0, progressCallback, "Starting status query");

        // 检查任务控制状态
        if (check_task_control(taskId_str, queryTaskControlCb, progressCallback)) {
            unregister_task(taskId_str);
            return copy_response_to_buffer(create_cancelled_response("Task cancelled by user"));
        }

        // 提取参数
        std::string query_drive = get_json_string_value(params_str, "drive", "");

        // 报告参数解析完成
        report_progress(taskId_str, 20, progressCallback, "Parameters parsed for status query");

        // 获取系统中的所有驱动器
        DWORD drives_mask = GetLogicalDrives();
        std::string mounted_drives_json = "[";

        // 简化实现：遍历所有可能的驱动器号
        bool first_drive = true;
        for (int i = 0; i < 26; i++) {
            if (!(drives_mask & (1 << i))) continue;

            char drive_letter = 'A' + i;
            std::string drive_str = std::string(1, drive_letter) + ":";

            // 如果指定了特定驱动器，只检查该驱动器
            if (!query_drive.empty() && query_drive != drive_str) {
                continue;
            }

            // 简单检查驱动器类型
            UINT drive_type = GetDriveTypeA(drive_str.c_str());
            if (drive_type == DRIVE_FIXED || drive_type == DRIVE_REMOVABLE) {
                // 获取文件系统信息
                char fs_name[MAX_PATH] = "Unknown";
                char drive_root[4] = {drive_letter, ':', '\\', '\0'};
                GetVolumeInformationA(drive_root, nullptr, 0, nullptr, nullptr, nullptr, fs_name, MAX_PATH);

                // 获取磁盘大小
                ULARGE_INTEGER free_bytes, total_bytes;
                int size_mb = 0;
                if (GetDiskFreeSpaceExA(drive_root, &free_bytes, &total_bytes, nullptr)) {
                    size_mb = static_cast<int>(total_bytes.QuadPart / (1024 * 1024));
                }

                // 添加到结果中
                if (!first_drive) {
                    mounted_drives_json += ",";
                }

                char drive_info[1024];
                sprintf_s(drive_info, sizeof(drive_info),
                    "{\"drive_letter\":\"%s\",\"image_path\":\"Unknown\",\"file_system\":\"%s\",\"size_mb\":%d,\"readonly\":false}",
                    drive_str.c_str(), fs_name, size_mb);
                mounted_drives_json += drive_info;
                first_drive = false;
            }

            // 检查任务控制状态
            if (check_task_control(taskId_str, queryTaskControlCb, progressCallback)) {
                unregister_task(taskId_str);
                return copy_response_to_buffer(create_cancelled_response("Task cancelled by user"));
            }
        }

        // 报告扫描完成
        report_progress(taskId_str, 100, progressCallback, "Status query completed successfully");

        // 生成响应
        mounted_drives_json += "]";
        char response_buffer[4096];
        sprintf_s(response_buffer, sizeof(response_buffer),
            "{\"status\":\"success\",\"message\":\"Status retrieved successfully\",\"mounted_drives\":%s}",
            mounted_drives_json.c_str());

        unregister_task(taskId_str);
        return copy_response_to_buffer(std::string(response_buffer));

    } catch (const std::exception& e) {
        unregister_task(taskId_str);
        return copy_response_to_buffer(create_error_response(std::string("Exception: ") + e.what()));
    } catch (...) {
        unregister_task(taskId_str);
        return copy_response_to_buffer(create_error_response("Unknown exception occurred"));
    }
}

/*
 * 获取库版本信息 - 符合006_Dll标准
 */
const char* GetLibraryInfo(
    const char* params,
    ProgressCallback progressCallback,
    const char* taskId,
    QueryTaskControlCallback queryTaskControlCb)
{
    // 转换参数为std::string
    std::string params_str = params ? params : "";
    std::string taskId_str = taskId ? taskId : "";

    // 注册任务
    register_task(taskId_str);

    try {
        // 报告开始进度
        report_progress(taskId_str, 0, progressCallback, "Starting library info retrieval");

        // 检查任务控制状态
        if (check_task_control(taskId_str, queryTaskControlCb, progressCallback)) {
            unregister_task(taskId_str);
            return copy_response_to_buffer(create_cancelled_response("Task cancelled by user"));
        }

        // 报告信息收集完成
        report_progress(taskId_str, 100, progressCallback, "Library info retrieved successfully");

        // 生成库信息响应
        const char* response_template =
            "{"
            "\"status\":\"success\","
            "\"message\":\"Library info retrieved successfully\","
            "\"version\":\"2.0.0\","
            "\"build_date\":\"2025-01-16\","
            "\"supported_formats\":[\"VMDK\",\"VHDX\",\"VHD\",\"ISO\",\"IMG\"],"
            "\"min_windows_version\":\"Windows XP SP3\","
            "\"architecture\":\"x86/x64\","
            "\"cpp_standard\":\"C++11\","
            "\"dll_standard\":\"006_Dll_v1.0\","
            "\"compiler_toolset\":\"v120_xp\""
            "}";

        unregister_task(taskId_str);
        return copy_response_to_buffer(std::string(response_template));

    } catch (const std::exception& e) {
        unregister_task(taskId_str);
        return copy_response_to_buffer(create_error_response(std::string("Exception: ") + e.what()));
    } catch (...) {
        unregister_task(taskId_str);
        return copy_response_to_buffer(create_error_response("Unknown exception occurred"));
    }
}

/*
 * 初始化VirtualDiskLib - 符合006_Dll标准
 */
const char* InitializeVirtualDiskLib(
    const char* params,
    ProgressCallback progressCallback,
    const char* taskId,
    QueryTaskControlCallback queryTaskControlCb)
{
    // 转换参数为std::string
    std::string params_str = params ? params : "";
    std::string taskId_str = taskId ? taskId : "";

    // 注册任务
    register_task(taskId_str);

    try {
        // 解析输入参数（简化处理）
        bool check_dependencies = get_json_bool_value(params_str, "check_dependencies", true);
        bool enable_debug = get_json_bool_value(params_str, "enable_debug", false);

        // 报告开始进度
        report_progress(taskId_str, 0, progressCallback, "Starting library initialization");

        // 检查任务控制状态
        if (check_task_control(taskId_str, queryTaskControlCb, progressCallback)) {
            unregister_task(taskId_str);
            return copy_response_to_buffer(create_cancelled_response("Task cancelled by user"));
        }

        // 报告参数解析完成
        report_progress(taskId_str, 20, progressCallback, "Initialization parameters parsed");

        // 执行初始化
        std::lock_guard<std::mutex> lock(g_init_mutex);
        bool already_initialized = g_initialized.load();

        if (!already_initialized) {
            // 简化初始化（不依赖可能不存在的InitializeImDisk函数）
            // 这里可以添加实际的初始化逻辑

            // 报告依赖检查完成
            report_progress(taskId_str, 80, progressCallback, "Dependencies checked successfully");

            g_initialized = true;
        }

        // 报告初始化完成
        report_progress(taskId_str, 100, progressCallback, "Library initialization completed");

        // 生成响应
        char response_buffer[1024];
        sprintf_s(response_buffer, sizeof(response_buffer),
            "{\"status\":\"success\",\"message\":\"VirtualDiskLib initialized successfully\","
            "\"dependencies_checked\":%s,\"debug_enabled\":%s,\"already_initialized\":%s}",
            check_dependencies ? "true" : "false",
            enable_debug ? "true" : "false",
            already_initialized ? "true" : "false");

        unregister_task(taskId_str);
        return copy_response_to_buffer(std::string(response_buffer));

    } catch (const std::exception& e) {
        unregister_task(taskId_str);
        return copy_response_to_buffer(create_error_response(std::string("Exception: ") + e.what()));
    } catch (...) {
        unregister_task(taskId_str);
        return copy_response_to_buffer(create_error_response("Unknown exception occurred"));
    }
}

/*
 * 清理VirtualDiskLib - 符合006_Dll标准
 */
const char* CleanupVirtualDiskLib(
    const char* params,
    ProgressCallback progressCallback,
    const char* taskId,
    QueryTaskControlCallback queryTaskControlCb)
{
    // 转换参数为std::string
    std::string params_str = params ? params : "";
    std::string taskId_str = taskId ? taskId : "";

    // 注册任务
    register_task(taskId_str);

    try {
        // 解析输入参数（简化处理）
        bool force_cleanup = get_json_bool_value(params_str, "force_cleanup", false);

        // 报告开始进度
        report_progress(taskId_str, 0, progressCallback, "Starting library cleanup");

        // 检查任务控制状态
        if (check_task_control(taskId_str, queryTaskControlCb, progressCallback)) {
            unregister_task(taskId_str);
            return copy_response_to_buffer(create_cancelled_response("Task cancelled by user"));
        }

        // 报告参数解析完成
        report_progress(taskId_str, 20, progressCallback, "Cleanup parameters parsed");

        // 执行清理
        std::lock_guard<std::mutex> lock(g_init_mutex);

        if (g_initialized.load()) {
            // 清理所有活动任务
            {
                std::lock_guard<std::mutex> task_lock(g_task_mutex);
                if (!force_cleanup && !g_cancel_flags.empty()) {
                    unregister_task(taskId_str);
                    return copy_response_to_buffer(create_error_response("Active tasks exist, use force_cleanup to override"));
                }

                // 取消所有任务
                for (auto& pair : g_cancel_flags) {
                    pair.second = true;
                }

                // 清理任务映射
                g_cancel_flags.clear();
                g_pause_flags.clear();
            }

            // 报告任务清理完成
            report_progress(taskId_str, 60, progressCallback, "Active tasks cleaned up");

            // 这里可以添加更多的清理逻辑
            // 例如：关闭服务句柄、释放资源等

            g_initialized = false;
        }

        // 报告清理完成
        report_progress(taskId_str, 100, progressCallback, "Library cleanup completed successfully");

        // 生成响应
        char response_buffer[1024];
        sprintf_s(response_buffer, sizeof(response_buffer),
            "{\"status\":\"success\",\"message\":\"VirtualDiskLib cleanup completed\","
            "\"resources_freed\":true,\"force_cleanup_used\":%s}",
            force_cleanup ? "true" : "false");

        unregister_task(taskId_str);
        return copy_response_to_buffer(std::string(response_buffer));

    } catch (const std::exception& e) {
        unregister_task(taskId_str);
        return copy_response_to_buffer(create_error_response(std::string("Exception: ") + e.what()));
    } catch (...) {
        unregister_task(taskId_str);
        return copy_response_to_buffer(create_error_response("Unknown exception occurred"));
    }
}

/*
 * 虚拟磁盘一体化管理接口 - 符合006_Dll标准
 */
const char* Init_Mountdisk(
    const char* params,
    ProgressCallback progressCallback,
    const char* taskId,
    QueryTaskControlCallback queryTaskControlCb)
{
    // 转换参数为std::string
    std::string params_str = params ? params : "";
    std::string taskId_str = taskId ? taskId : "";

    // 注册任务
    register_task(taskId_str);

    OutputDebugStringA("=== Starting Init_Mountdisk Operation (006_Dll Standard) ===\n");

    try {
        // 报告开始进度
        report_progress(taskId_str, 0, progressCallback, "Starting integrated disk management operation");

        // 检查任务控制状态
        if (check_task_control(taskId_str, queryTaskControlCb, progressCallback)) {
            unregister_task(taskId_str);
            return copy_response_to_buffer(create_cancelled_response("Task cancelled by user"));
        }

        // 解析输入参数
        std::string operation = "mount";  // 默认操作
        std::string file_path;
        std::string drive;
        bool readonly = false;
        int partition = 1;
        bool auto_cleanup = true;
        bool force_unmount = false;
        bool check_dependencies = true;

        // 简单的JSON解析（这里可以使用nlohmann/json库进行更完善的解析）
        if (params_str.find("\"operation\"") != std::string::npos) {
            if (params_str.find("\"mount\"") != std::string::npos) operation = "mount";
            else if (params_str.find("\"unmount\"") != std::string::npos) operation = "unmount";
            else if (params_str.find("\"status\"") != std::string::npos) operation = "status";
            else if (params_str.find("\"full_cycle\"") != std::string::npos) operation = "full_cycle";
        }

        // 提取文件路径
        size_t file_path_start = params_str.find("\"file_path\":\"");
        if (file_path_start != std::string::npos) {
            file_path_start += 13; // 跳过 "file_path":"
            size_t file_path_end = params_str.find("\"", file_path_start);
            if (file_path_end != std::string::npos) {
                file_path = params_str.substr(file_path_start, file_path_end - file_path_start);
            }
        }

        // 提取驱动器号
        size_t drive_start = params_str.find("\"drive\":\"");
        if (drive_start != std::string::npos) {
            drive_start += 9; // 跳过 "drive":"
            size_t drive_end = params_str.find("\"", drive_start);
            if (drive_end != std::string::npos) {
                drive = params_str.substr(drive_start, drive_end - drive_start);
            }
        }

        // 提取只读标志
        if (params_str.find("\"readonly\":true") != std::string::npos) {
            readonly = true;
        }

        // 提取分区号
        size_t partition_start = params_str.find("\"partition\":");
        if (partition_start != std::string::npos) {
            partition_start += 12; // 跳过 "partition":
            size_t partition_end = params_str.find_first_of(",}", partition_start);
            if (partition_end != std::string::npos) {
                std::string partition_str = params_str.substr(partition_start, partition_end - partition_start);
                partition = atoi(partition_str.c_str());
            }
        }

        // 提取自动清理标志
        if (params_str.find("\"auto_cleanup\":false") != std::string::npos) {
            auto_cleanup = false;
        }

        // 提取强制卸载标志
        if (params_str.find("\"force_unmount\":true") != std::string::npos) {
            force_unmount = true;
        }

        // 提取依赖检查标志
        if (params_str.find("\"check_dependencies\":false") != std::string::npos) {
            check_dependencies = false;
        }

        char debug_info[512];
        sprintf_s(debug_info, sizeof(debug_info),
            "Operation: %s, File: %s, Drive: %s, ReadOnly: %s, Partition: %d\n",
            operation.c_str(), file_path.c_str(), drive.c_str(),
            readonly ? "true" : "false", partition);
        OutputDebugStringA(debug_info);

        // 构建结果JSON
        std::string result_json = "{\"status\":\"success\",\"message\":\"Operation completed successfully\",\"operation\":\"" + operation + "\",\"results\":{";

        // 步骤1: 初始化库（10%进度）
        report_progress(taskId_str, 10, progressCallback, "Initializing VirtualDiskLib");

        std::string init_params = check_dependencies ?
            "{\"check_dependencies\":true,\"enable_debug\":false}" :
            "{\"check_dependencies\":false,\"enable_debug\":false}";

        const char* init_result = InitializeVirtualDiskLib(init_params.c_str(), nullptr,
            (taskId_str + "_init").c_str(), nullptr);

        bool init_success = (init_result && strstr(init_result, "\"status\":\"success\"") != nullptr);

        result_json += "\"initialization\":{\"success\":" + std::string(init_success ? "true" : "false") +
                      ",\"dependencies_checked\":" + std::string(check_dependencies ? "true" : "false") + "}";

        if (!init_success) {
            unregister_task(taskId_str);
            return copy_response_to_buffer(create_error_response("Initialization failed: " + std::string(init_result ? init_result : "Unknown error")));
        }

        // 根据操作类型执行相应的步骤
        if (operation == "mount" || operation == "full_cycle") {
            // 步骤2: 挂载操作（30%进度）
            report_progress(taskId_str, 30, progressCallback, "Mounting virtual disk");

            if (file_path.empty() || drive.empty()) {
                unregister_task(taskId_str);
                return copy_response_to_buffer(create_error_response("Mount operation requires file_path and drive parameters"));
            }

            char mount_params[1024];
            sprintf_s(mount_params, sizeof(mount_params),
                "{\"file_path\":\"%s\",\"drive\":\"%s\",\"readonly\":%s,\"partition\":%d}",
                file_path.c_str(), drive.c_str(), readonly ? "true" : "false", partition);

            const char* mount_result = MountVirtualDisk(mount_params, nullptr,
                (taskId_str + "_mount").c_str(), nullptr);

            bool mount_success = (mount_result && strstr(mount_result, "\"status\":\"success\"") != nullptr);

            result_json += ",\"mount\":{\"success\":" + std::string(mount_success ? "true" : "false");
            if (mount_success) {
                result_json += ",\"drive_letter\":\"" + drive + "\",\"readonly\":" +
                              std::string(readonly ? "true" : "false") + ",\"partition\":" + std::to_string(partition);
            }
            result_json += "}";

            if (!mount_success && operation == "mount") {
                // 如果挂载失败且不是full_cycle，进行清理并返回错误
                if (auto_cleanup) {
                    CleanupVirtualDiskLib("{\"force_cleanup\":false}", nullptr,
                        (taskId_str + "_cleanup").c_str(), nullptr);
                }
                unregister_task(taskId_str);
                return copy_response_to_buffer(create_error_response("Mount operation failed: " + std::string(mount_result ? mount_result : "Unknown error")));
            }
        }

        if (operation == "unmount" || operation == "full_cycle") {
            // 步骤3: 卸载操作（50%进度）
            report_progress(taskId_str, 50, progressCallback, "Unmounting virtual disk");

            if (drive.empty()) {
                unregister_task(taskId_str);
                return copy_response_to_buffer(create_error_response("Unmount operation requires drive parameter"));
            }

            char unmount_params[512];
            sprintf_s(unmount_params, sizeof(unmount_params),
                "{\"drive\":\"%s\",\"force\":%s}",
                drive.c_str(), force_unmount ? "true" : "false");

            const char* unmount_result = UnmountVirtualDisk(unmount_params, nullptr,
                (taskId_str + "_unmount").c_str(), nullptr);

            bool unmount_success = (unmount_result && strstr(unmount_result, "\"status\":\"success\"") != nullptr);

            result_json += ",\"unmount\":{\"success\":" + std::string(unmount_success ? "true" : "false");
            if (unmount_success) {
                result_json += ",\"unmounted_drive\":\"" + drive + "\",\"force_used\":" +
                              std::string(force_unmount ? "true" : "false");
            }
            result_json += "}";
        }

        if (operation == "status") {
            // 步骤4: 状态查询（70%进度）
            report_progress(taskId_str, 70, progressCallback, "Querying mount status");

            std::string status_params = drive.empty() ? "{}" : "{\"drive\":\"" + drive + "\"}";

            const char* status_result = GetMountStatus(status_params.c_str(), nullptr,
                (taskId_str + "_status").c_str(), nullptr);

            bool status_success = (status_result && strstr(status_result, "\"status\":\"success\"") != nullptr);

            result_json += ",\"status\":{\"success\":" + std::string(status_success ? "true" : "false");
            if (status_success && status_result) {
                // 提取mounted_drives部分
                const char* drives_start = strstr(status_result, "\"mounted_drives\":");
                if (drives_start) {
                    const char* drives_end = strstr(drives_start, "]");
                    if (drives_end) {
                        std::string drives_info(drives_start, drives_end - drives_start + 1);
                        result_json += "," + drives_info;
                    }
                }
            }
            result_json += "}";
        }

        // 步骤5: 清理操作（90%进度）
        if (auto_cleanup) {
            report_progress(taskId_str, 90, progressCallback, "Cleaning up resources");

            const char* cleanup_result = CleanupVirtualDiskLib("{\"force_cleanup\":false}", nullptr,
                (taskId_str + "_cleanup").c_str(), nullptr);

            bool cleanup_success = (cleanup_result && strstr(cleanup_result, "\"status\":\"success\"") != nullptr);

            result_json += ",\"cleanup\":{\"success\":" + std::string(cleanup_success ? "true" : "false") +
                          ",\"resources_freed\":" + std::string(cleanup_success ? "true" : "false") + "}";
        }

        result_json += "}}";

        // 报告完成
        report_progress(taskId_str, 100, progressCallback, "Integrated disk management operation completed");

        unregister_task(taskId_str);
        return copy_response_to_buffer(result_json);

    } catch (const std::exception& e) {
        unregister_task(taskId_str);
        return copy_response_to_buffer(create_error_response(std::string("Exception: ") + e.what()));
    } catch (...) {
        unregister_task(taskId_str);
        return copy_response_to_buffer(create_error_response("Unknown exception occurred"));
    }
}

// ========================================
// DLL入口点 (C++11现代化)
// ========================================

/*
 * DLL入口点
 */
BOOL APIENTRY DllMain(HMODULE hModule, DWORD ul_reason_for_call, LPVOID lpReserved)
{
    switch (ul_reason_for_call)
    {
    case DLL_PROCESS_ATTACH:
        // C++11全局对象会自动初始化
        // 初始化临界区
        //InitializeCriticalSection(&g_cs);
        break;

    case DLL_PROCESS_DETACH:
        // C++11全局对象会自动析构
        //DeleteCriticalSection(&g_cs);
        break;

    case DLL_THREAD_ATTACH:
    case DLL_THREAD_DETACH:
        break;
    }
    return TRUE;
}