/*
 * test_wrapper_interface.cpp
 * 测试VirtualDiskLib统一封装接口
 * 
 * 功能：演示如何使用封装后的统一C++接口
 * 编译：cl test_wrapper_interface.cpp /I. VirtualDiskLib32.lib /Fe:test_wrapper.exe
 */

#include <iostream>
#include <string>
#include <windows.h>
#include "VirtualDiskLib.h"

// ========================================
// 测试用回调函数
// ========================================

/*
 * 简化的进度回调函数
 */
void TestProgressCallback(const std::string& taskId, int progress) {
    std::cout << "[PROGRESS] Task: " << taskId << ", Progress: " << progress << "%" << std::endl;
}

/*
 * 简化的任务控制回调函数
 */
bool TestQueryTaskControlCallback(const std::string& taskId, int controlType) {
    // controlType: 1=取消, 2=暂停
    std::cout << "[CONTROL] Task: " << taskId << ", Control Type: " << controlType << std::endl;
    
    // 这里可以根据需要返回true来取消或暂停任务
    // 为了测试，我们不取消任何任务
    return false;
}

// ========================================
// 测试函数
// ========================================

/*
 * 测试获取库信息
 */
void TestGetLibraryInfo() {
    std::cout << "\n=== 测试获取库信息 ===" << std::endl;
    
    std::string result = GetLibraryInfoWrapper(
        "{}",                           // 空参数
        TestProgressCallback,           // 进度回调
        "test_library_info",           // 任务ID
        TestQueryTaskControlCallback   // 任务控制回调
    );
    
    std::cout << "结果: " << result << std::endl;
}

/*
 * 测试初始化库
 */
void TestInitializeLibrary() {
    std::cout << "\n=== 测试初始化库 ===" << std::endl;
    
    std::string result = InitializeVirtualDiskLibWrapper(
        R"({"check_dependencies": true, "enable_debug": true})",
        TestProgressCallback,
        "test_initialize",
        TestQueryTaskControlCallback
    );
    
    std::cout << "结果: " << result << std::endl;
}

/*
 * 测试挂载虚拟磁盘
 */
void TestMountVirtualDisk() {
    std::cout << "\n=== 测试挂载虚拟磁盘 ===" << std::endl;
    
    // 使用JSON参数方式
    std::string mountParams = R"({
        "image_path": "C:\\test\\disk.vmdk",
        "drive_letter": "Z:",
        "readonly": false,
        "partition": 1
    })";
    
    std::string result = MountVirtualDiskWrapper(
        mountParams,
        TestProgressCallback,
        "test_mount",
        TestQueryTaskControlCallback
    );
    
    std::cout << "挂载结果: " << result << std::endl;
    
    // 使用简化接口方式
    std::cout << "\n--- 使用简化接口挂载 ---" << std::endl;
    std::string simpleResult = MountDiskSimple(
        "C:\\test\\disk2.vmdk",  // 镜像路径
        "Y:",                    // 驱动器号
        false,                   // 不是只读
        1,                       // 分区1
        TestProgressCallback     // 进度回调
    );
    
    std::cout << "简化挂载结果: " << simpleResult << std::endl;
}

/*
 * 测试获取挂载状态
 */
void TestGetMountStatus() {
    std::cout << "\n=== 测试获取挂载状态 ===" << std::endl;
    
    // 查询所有挂载的驱动器
    std::string result = GetMountStatusWrapper(
        R"({"drive": ""})",      // 空驱动器号表示查询所有
        TestProgressCallback,
        "test_status",
        TestQueryTaskControlCallback
    );
    
    std::cout << "挂载状态: " << result << std::endl;
    
    // 查询特定驱动器
    std::cout << "\n--- 查询特定驱动器 ---" << std::endl;
    std::string specificResult = GetMountStatusWrapper(
        R"({"drive": "Z:"})",    // 查询Z盘
        TestProgressCallback,
        "test_status_z"
    );
    
    std::cout << "Z盘状态: " << specificResult << std::endl;
}

/*
 * 测试卸载虚拟磁盘
 */
void TestUnmountVirtualDisk() {
    std::cout << "\n=== 测试卸载虚拟磁盘 ===" << std::endl;
    
    // 使用JSON参数方式
    std::string unmountParams = R"({
        "drive": "Z:",
        "force": false
    })";
    
    std::string result = UnmountVirtualDiskWrapper(
        unmountParams,
        TestProgressCallback,
        "test_unmount",
        TestQueryTaskControlCallback
    );
    
    std::cout << "卸载结果: " << result << std::endl;
    
    // 使用简化接口方式
    std::cout << "\n--- 使用简化接口卸载 ---" << std::endl;
    std::string simpleResult = UnmountDiskSimple(
        "Y:",                    // 驱动器号
        false,                   // 不强制卸载
        TestProgressCallback     // 进度回调
    );
    
    std::cout << "简化卸载结果: " << simpleResult << std::endl;
}

/*
 * 测试清理库
 */
void TestCleanupLibrary() {
    std::cout << "\n=== 测试清理库 ===" << std::endl;
    
    std::string result = CleanupVirtualDiskLibWrapper(
        R"({"force_cleanup": false})",
        TestProgressCallback,
        "test_cleanup",
        TestQueryTaskControlCallback
    );
    
    std::cout << "清理结果: " << result << std::endl;
}

// ========================================
// 主函数
// ========================================

int main() {
    std::cout << "VirtualDiskLib 统一封装接口测试程序" << std::endl;
    std::cout << "========================================" << std::endl;
    
    try {
        // 1. 获取库信息
        TestGetLibraryInfo();
        
        // 2. 初始化库
        TestInitializeLibrary();
        
        // 3. 测试挂载
        TestMountVirtualDisk();
        
        // 4. 获取挂载状态
        TestGetMountStatus();
        
        // 5. 测试卸载
        TestUnmountVirtualDisk();
        
        // 6. 清理库
        TestCleanupLibrary();
        
        std::cout << "\n========================================" << std::endl;
        std::cout << "所有测试完成！" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "测试过程中发生异常: " << e.what() << std::endl;
        return 1;
    } catch (...) {
        std::cerr << "测试过程中发生未知异常" << std::endl;
        return 1;
    }
    
    return 0;
}
