﻿/*
 * VirtualDiskLib.h
 * 虚拟磁盘挂载库 - 符合006_Dll要求的现代化接口
 *
 * 功能：提供虚拟磁盘挂载、卸载和状态查询功能
 * 支持格式：VMDK, VHDX, VHD, ISO, IMG等
 * 兼容性：Windows XP及以上版本
 * C++标准：C++11 (v120_xp工具集)
 *
 * 架构特性：
 * - 符合006_Dll要求的标准化接口
 * - 支持进度回调和任务控制
 * - 异步任务执行和状态管理
 * - JSON格式的参数和返回值
 * - 现代C++11特性应用
 */

#ifndef VIRTUALDISKLIB_H
#define VIRTUALDISKLIB_H

#include <string>
#include <memory>
#include <functional>

#ifdef __cplusplus
extern "C" {
#endif

// DLL导出宏定义
#ifdef VIRTUALDISKLIB_EXPORTS
#define VIRTUALDISKLIB_API __declspec(dllexport)
#else
#define VIRTUALDISKLIB_API __declspec(dllimport)
#endif

// ========================================
// 006_Dll标准回调函数类型定义
// ========================================

/*
 * 进度回调函数类型
 *
 * 参数：
 *   taskId: 任务唯一标识符
 *   progress: 进度值
 *     - 0-100: 进度百分比
 *     - -1: 匹配结果回调（matchResult包含结果JSON）
 *   matchResult: 匹配结果JSON字符串（仅当progress=-1时有效）
 */
typedef void (*ProgressCallback)(const std::string& taskId, int progress, const std::string& matchResult);

/*
 * 任务控制回调函数类型
 *
 * 参数：
 *   taskId: 任务唯一标识符
 *   controlType: 控制类型
 *     - 0: 查询是否取消任务
 *     - 1: 查询是否暂停任务
 *
 * 返回值：
 *   true: 相应的控制状态激活
 *   false: 相应的控制状态未激活
 */
typedef bool (*QueryTaskControlCallback)(const std::string& taskId, int controlType);

// ========================================
// 006_Dll标准化接口函数
// ========================================

/*
 * 挂载虚拟磁盘
 * 
 * 参数：
 *   jsonInput: 输入的JSON字符串，包含挂载参数
 *   jsonOutput: 输出缓冲区，用于接收响应JSON
 *   bufferSize: 输出缓冲区大小
 * 
 * 输入JSON格式：
 * {
 *   "file_path": "C:\\path\\to\\disk.vmdk",  // 镜像文件路径
 *   "drive": "Z:",                           // 目标驱动器号（可选）
 *   "readonly": true,                        // 是否只读挂载
 *   "partition": 1,                          // 分区号（默认1）
 *   "auto_assign": false                     // 是否自动分配驱动器号
 * }
 * 
 * 输出JSON格式：
 * {
 *   "success": true,                         // 操作是否成功
 *   "error_code": 0,                         // 错误代码
 *   "drive_letter": "Z:",                    // 实际分配的驱动器号
 *   "file_system": "NTFS",                   // 文件系统类型
 *   "size_mb": 1024,                         // 磁盘大小（MB）
 *   "readonly": true,                        // 是否只读
 *   "message": "Mount successful"            // 操作消息
 * }
 * 
 * 返回值：
 *   VDL_SUCCESS: 操作成功
 *   其他值: 错误代码
 */
VIRTUALDISKLIB_API const char* MountVirtualDisk(
    const char* params,
    ProgressCallback progressCallback = nullptr,
    const char* taskId = "",
    QueryTaskControlCallback queryTaskControlCb = nullptr
);

/*
 * 卸载虚拟磁盘 - 符合006_Dll标准
 *
 * 参数：
 *   params: JSON格式的输入参数
 *   progressCallback: 进度回调函数（可选）
 *   taskId: 任务唯一标识符
 *   queryTaskControlCb: 任务控制回调函数（可选）
 *
 * 输入JSON格式：
 * {
 *   "drive": "Z:",                           // 要卸载的驱动器号
 *   "force": false                           // 是否强制卸载
 * }
 *
 * 返回JSON格式：
 * {
 *   "status": "success",                     // 状态: success/error/cancelled
 *   "message": "Unmount successful",         // 操作消息
 *   "unmounted_drive": "Z:",                 // 已卸载的驱动器号
 *   "cleanup_completed": true                // 清理是否完成
 * }
 */
VIRTUALDISKLIB_API const char* UnmountVirtualDisk(
    const char* params,
    ProgressCallback progressCallback = nullptr,
    const char* taskId = "",
    QueryTaskControlCallback queryTaskControlCb = nullptr
);

/*
 * 获取挂载状态 - 符合006_Dll标准
 *
 * 参数：
 *   params: JSON格式的输入参数
 *   progressCallback: 进度回调函数（可选）
 *   taskId: 任务唯一标识符
 *   queryTaskControlCb: 任务控制回调函数（可选）
 *
 * 输入JSON格式：
 * {
 *   "drive": "Z:"                            // 要查询的驱动器号（可选，为空则查询所有）
 * }
 *
 * 返回JSON格式：
 * {
 *   "status": "success",
 *   "message": "Status retrieved successfully",
 *   "mounted_drives": [
 *     {
 *       "drive_letter": "Z:",
 *       "image_path": "C:\\path\\to\\disk.vmdk",
 *       "file_system": "NTFS",
 *       "size_mb": 1024,
 *       "readonly": true
 *     }
 *   ]
 * }
 */
VIRTUALDISKLIB_API const char* GetMountStatus(
    const char* params,
    ProgressCallback progressCallback = nullptr,
    const char* taskId = "",
    QueryTaskControlCallback queryTaskControlCb = nullptr
);

/*
 * 获取库版本信息 - 符合006_Dll标准
 *
 * 参数：
 *   params: JSON格式的输入参数（可为空JSON对象）
 *   progressCallback: 进度回调函数（可选）
 *   taskId: 任务唯一标识符
 *   queryTaskControlCb: 任务控制回调函数（可选）
 *
 * 返回JSON格式：
 * {
 *   "status": "success",
 *   "message": "Library info retrieved successfully",
 *   "version": "2.0.0",
 *   "build_date": "2025-01-16",
 *   "supported_formats": ["VMDK", "VHDX", "VHD", "ISO", "IMG"],
 *   "min_windows_version": "Windows XP SP3",
 *   "architecture": "x86/x64",
 *   "cpp_standard": "C++11",
 *   "dll_standard": "006_Dll_v1.0"
 * }
 */
VIRTUALDISKLIB_API const char* GetLibraryInfo(
    const char* params,
    ProgressCallback progressCallback = nullptr,
    const char* taskId = "",
    QueryTaskControlCallback queryTaskControlCb = nullptr
);

/*
 * 初始化VirtualDiskLib - 符合006_Dll标准
 *
 * 参数：
 *   params: JSON格式的输入参数
 *   progressCallback: 进度回调函数（可选）
 *   taskId: 任务唯一标识符
 *   queryTaskControlCb: 任务控制回调函数（可选）
 *
 * 输入JSON格式：
 * {
 *   "check_dependencies": true,                  // 是否检查依赖项
 *   "enable_debug": false                        // 是否启用调试模式
 * }
 *
 * 返回JSON格式：
 * {
 *   "status": "success",
 *   "message": "VirtualDiskLib initialized successfully",
 *   "dependencies_checked": true,
 *   "debug_enabled": false
 * }
 */
VIRTUALDISKLIB_API const char* InitializeVirtualDiskLib(
    const char* params,
    ProgressCallback progressCallback = nullptr,
    const char* taskId = "",
    QueryTaskControlCallback queryTaskControlCb = nullptr
);

/*
 * 清理VirtualDiskLib - 符合006_Dll标准
 *
 * 参数：
 *   params: JSON格式的输入参数
 *   progressCallback: 进度回调函数（可选）
 *   taskId: 任务唯一标识符
 *   queryTaskControlCb: 任务控制回调函数（可选）
 *
 * 输入JSON格式：
 * {
 *   "force_cleanup": false                       // 是否强制清理
 * }
 *
 * 返回JSON格式：
 * {
 *   "status": "success",
 *   "message": "VirtualDiskLib cleanup completed",
 *   "resources_freed": true
 * }
 */
VIRTUALDISKLIB_API const char* CleanupVirtualDiskLib(
    const char* params,
    ProgressCallback progressCallback = nullptr,
    const char* taskId = "",
    QueryTaskControlCallback queryTaskControlCb = nullptr
);

// ========================================
// 兼容性和实现说明
// ========================================
//
// 本库已完全符合006_Dll要求的架构标准：
//
// 1. 标准化接口：所有函数使用统一的签名模式
// 2. 回调机制：支持进度回调和任务控制
// 3. JSON格式：统一的输入输出数据格式
// 4. 异步支持：支持任务取消和暂停
// 5. C++11特性：在XP兼容前提下使用现代C++
// 6. 错误处理：标准化的错误状态和消息
//
// 实现特点：
// - 直接集成MountImg.c，零重复实现
// - 高性能，直接函数调用无进程开销
// - 强兼容性，与MountImg_Simple使用相同逻辑
// - Windows XP支持，使用v120_xp工具集
// - C++11标准，现代化但保持兼容性

// ========================================
// 统一封装接口 - C++接口
// ========================================

#ifdef __cplusplus

/*
 * 简化的进度回调函数类型
 * 参数：
 *   taskId: 任务唯一标识符
 *   progress: 进度值 (0-100)
 */
typedef void (*SimpleProgressCallback)(const std::string& taskId, int progress);

/*
 * 简化的任务控制回调函数类型
 * 参数：
 *   taskId: 任务唯一标识符
 *   controlType: 控制类型 (1=取消, 2=暂停)
 * 返回值：
 *   true: 执行相应控制操作
 *   false: 继续执行
 */
typedef bool (*SimpleQueryTaskControlCallback)(const std::string& taskId, int controlType);

/*
 * 统一的虚拟磁盘操作封装接口
 *
 * 功能：将所有VirtualDiskLib函数封装成统一的C++接口
 * 特点：
 *   - 自动任务ID生成
 *   - 简化的回调函数接口
 *   - 统一的异常处理
 *   - 线程安全的操作
 *
 * 参数：
 *   params: JSON格式的输入参数
 *   progressCallback: 简化的进度回调函数（可选）
 *   taskId: 任务唯一标识符（可选，自动生成）
 *   queryTaskControlCb: 简化的任务控制回调函数（可选）
 *
 * 返回值：
 *   JSON格式的操作结果字符串
 */
VIRTUALDISKLIB_API std::string MountVirtualDiskWrapper(
    const std::string& params,
    SimpleProgressCallback progressCallback = nullptr,
    const std::string& taskId = "",
    SimpleQueryTaskControlCallback queryTaskControlCb = nullptr
);

VIRTUALDISKLIB_API std::string UnmountVirtualDiskWrapper(
    const std::string& params,
    SimpleProgressCallback progressCallback = nullptr,
    const std::string& taskId = "",
    SimpleQueryTaskControlCallback queryTaskControlCb = nullptr
);

VIRTUALDISKLIB_API std::string GetMountStatusWrapper(
    const std::string& params = R"({"drive": ""})",
    SimpleProgressCallback progressCallback = nullptr,
    const std::string& taskId = "",
    SimpleQueryTaskControlCallback queryTaskControlCb = nullptr
);

VIRTUALDISKLIB_API std::string GetLibraryInfoWrapper(
    const std::string& params = "{}",
    SimpleProgressCallback progressCallback = nullptr,
    const std::string& taskId = "",
    SimpleQueryTaskControlCallback queryTaskControlCb = nullptr
);

VIRTUALDISKLIB_API std::string InitializeVirtualDiskLibWrapper(
    const std::string& params = R"({"check_dependencies": true, "enable_debug": false})",
    SimpleProgressCallback progressCallback = nullptr,
    const std::string& taskId = "",
    SimpleQueryTaskControlCallback queryTaskControlCb = nullptr
);

VIRTUALDISKLIB_API std::string CleanupVirtualDiskLibWrapper(
    const std::string& params = R"({"force_cleanup": false})",
    SimpleProgressCallback progressCallback = nullptr,
    const std::string& taskId = "",
    SimpleQueryTaskControlCallback queryTaskControlCb = nullptr
);

// ========================================
// 便捷接口函数
// ========================================

/*
 * 简化的挂载接口
 *
 * 参数：
 *   imagePath: 虚拟磁盘镜像文件路径
 *   driveLetter: 目标驱动器号 (如 "Z:")
 *   readonly: 是否只读挂载
 *   partition: 分区号 (默认为1)
 *   progressCallback: 进度回调函数（可选）
 *
 * 返回值：
 *   JSON格式的挂载结果字符串
 */
VIRTUALDISKLIB_API std::string MountDiskSimple(
    const std::string& imagePath,
    const std::string& driveLetter,
    bool readonly = false,
    int partition = 1,
    SimpleProgressCallback progressCallback = nullptr
);

/*
 * 简化的卸载接口
 *
 * 参数：
 *   driveLetter: 要卸载的驱动器号 (如 "Z:")
 *   force: 是否强制卸载
 *   progressCallback: 进度回调函数（可选）
 *
 * 返回值：
 *   JSON格式的卸载结果字符串
 */
VIRTUALDISKLIB_API std::string UnmountDiskSimple(
    const std::string& driveLetter,
    bool force = false,
    SimpleProgressCallback progressCallback = nullptr
);

#endif // __cplusplus

#ifdef __cplusplus
}
#endif

#endif // VIRTUALDISKLIB_H
