﻿  config.c
config.c : warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
config.c(1): warning C4005: '_WIN32_WINNT' : macro redefinition
          command-line arguments :  see previous definition of '_WIN32_WINNT'
config.c(106): warning C4244: 'function' : conversion from 'LONGLONG' to 'SIZE_T', possible loss of data
config.c(165): warning C4018: '<' : signed/unsigned mismatch
config.c(319): warning C4101: 'j' : unreferenced local variable
config.c(746): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
config.c(843): warning C4133: 'function' : incompatible types - from 'char [9]' to 'const wchar_t *'
config.c(1154): warning C4018: '<=' : signed/unsigned mismatch
config.c(1467): warning C4018: '<' : signed/unsigned mismatch
config.c(1502): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  Generating code
  Finished generating code
  ImDiskInstaller.vcxproj -> E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\Release\config32.exe
