@echo off
REM build_test_init_mountdisk.bat
REM 编译 Init_Mountdisk 接口测试程序
REM 
REM 要求：Visual Studio 2013 (v120_xp工具集)
REM 目标：Windows XP兼容的32位程序

echo ========================================
echo    编译 Init_Mountdisk 测试程序
echo ========================================
echo.

REM 设置编译环境
set VCVARS_PATH="C:\Program Files (x86)\Microsoft Visual Studio 12.0\VC\vcvarsall.bat"
if exist %VCVARS_PATH% (
    echo 正在设置Visual Studio 2013编译环境...
    call %VCVARS_PATH% x86
) else (
    echo 警告: 未找到Visual Studio 2013，尝试使用当前环境
)

echo.
echo 正在编译测试程序...

REM 编译参数说明：
REM /EHsc - 启用C++异常处理
REM /std:c++11 - 使用C++11标准（如果支持）
REM /D_WIN32_WINNT=0x0501 - 目标Windows XP
REM /DWINVER=0x0501 - Windows版本定义
REM /D_CRT_SECURE_NO_WARNINGS - 禁用安全警告
REM /I. - 包含当前目录头文件

cl.exe /EHsc ^
       /D_WIN32_WINNT=0x0501 ^
       /DWINVER=0x0501 ^
       /D_CRT_SECURE_NO_WARNINGS ^
       /I. ^
       test_init_mountdisk.cpp ^
       /Fe:test_init_mountdisk.exe ^
       /link kernel32.lib user32.lib

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ 编译成功！
    echo 生成文件: test_init_mountdisk.exe
    echo.
    echo 使用说明:
    echo 1. 确保 VirtualDiskLib32.dll 在同一目录或系统PATH中
    echo 2. 运行 test_init_mountdisk.exe 进行测试
    echo 3. 根据需要修改测试参数（文件路径、驱动器号等）
    echo.
) else (
    echo.
    echo ❌ 编译失败！
    echo 错误代码: %ERRORLEVEL%
    echo.
    echo 可能的解决方案:
    echo 1. 检查Visual Studio 2013是否正确安装
    echo 2. 确认vcvarsall.bat路径是否正确
    echo 3. 检查源代码是否有语法错误
    echo.
)

echo 按任意键退出...
pause >nul
