.OPTION EXPLICIT
.Set CabinetNameTemplate=files.cab
.Set DiskDirectoryTemplate=.
.Set CompressionType=MSZIP
.Set UniqueFiles=OFF
.Set Cabinet=ON
.Set Compress=ON
.Set SourceDir=temp_cab_dir

"config.exe"
"config32.exe"
"config32_uninstall.exe"
"cp-admin.lnk"
"cp.lnk"
"devio.exe"
"DevioNet.dll"
"DiscUtils.Core.dll"
"DiscUtils.Dmg.dll"
"DiscUtils.Streams.dll"
"DiscUtils.Vdi.dll"
"DiscUtils.Vhd.dll"
"DiscUtils.Vhdx.dll"
"DiscUtils.Vmdk.dll"
"DiscUtils.Xva.dll"
"DiscUtilsDevio.exe"
"ImDisk-Dlg.exe"
"ImDiskNet.dll"
"ImDiskTk-svc.exe"
"ImDiskTk-svc32.exe"
"ImDiskTk-svc64.exe"
"lang.txt"
"MountImg.exe"
"MountImg32.exe"
"RamDiskUI.exe"
"RamDyn.exe"
"RamDyn32.exe"
"RamDyn64.exe"
"driver\gpl.txt"
"driver\imdisk.inf"
"driver\install.cmd"
"driver\msgboxw.exe"
"driver\readme.txt"
"driver\runwaitw.exe"
"driver\uninstall_imdisk.cmd"
"driver\awealloc\amd64\awealloc.cer"
"driver\awealloc\amd64\awealloc.sys"
"driver\awealloc\i386\awealloc.cer"
"driver\awealloc\i386\awealloc.sys"
"driver\cli\amd64\imdisk.exe"
"driver\cli\i386\imdisk.exe"
"driver\cpl\amd64\imdisk.cpl"
"driver\cpl\amd64\imdisk.lib"
"driver\cpl\i386\imdisk.cpl"
"driver\cpl\i386\imdisk.lib"
"driver\svc\amd64\imdsksvc.exe"
"driver\svc\i386\imdsksvc.exe"
"driver\sys\amd64\imdisk.cer"
"driver\sys\amd64\imdisk.sys"
"driver\sys\i386\imdisk.cer"
"driver\sys\i386\imdisk.sys"
"lang\brazilian-portuguese.txt"
"lang\english.txt"
"lang\french.txt"
"lang\german.txt"
"lang\italian.txt"
"lang\russian.txt"
"lang\schinese.txt"
"lang\spanish.txt"
"lang\swedish.txt"
