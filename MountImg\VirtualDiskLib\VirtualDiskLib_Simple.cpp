/*
 * VirtualDiskLib_Simple.cpp
 * 简化版本的VirtualDiskLib实现，兼容VS2013
 * 使用C风格JSON生成，避免C++11兼容性问题
 */

// Windows头文件
#define _WIN32_WINNT 0x0501
#define OEMRESOURCE
#define _CRT_SECURE_NO_WARNINGS

#include <windows.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <dbt.h>
#include <shlwapi.h>
#include <winternl.h>

// 项目头文件
#include "VirtualDiskLib.h"
#include "../MountImg.h"
#include "json_helper.h"

// C++标准库
#include <string>
#include <map>
#include <vector>
#include <mutex>
#include <atomic>
#include <memory>

// ========================================
// 全局变量和状态管理 (C++11现代化)
// ========================================

// 初始化状态
static std::atomic<bool> g_initialized(false);
static std::mutex g_init_mutex;

// 任务管理
static std::mutex g_task_mutex;
static std::map<std::string, bool> g_cancel_flags;
static std::map<std::string, bool> g_pause_flags;

// ========================================
// 辅助函数
// ========================================

// 简化的JSON响应生成函数
std::string create_success_response(const std::string& message) {
    char buffer[1024];
    sprintf_s(buffer, sizeof(buffer), 
        "{\"status\":\"success\",\"message\":\"%s\"}", 
        message.c_str());
    return std::string(buffer);
}

std::string create_error_response(const std::string& message) {
    char buffer[1024];
    sprintf_s(buffer, sizeof(buffer), 
        "{\"status\":\"error\",\"message\":\"%s\"}", 
        message.c_str());
    return std::string(buffer);
}

std::string create_cancelled_response(const std::string& message) {
    char buffer[1024];
    sprintf_s(buffer, sizeof(buffer), 
        "{\"status\":\"cancelled\",\"message\":\"%s\"}", 
        message.c_str());
    return std::string(buffer);
}

// 简化的JSON解析函数
std::string get_json_string_value(const std::string& json, const std::string& key, const std::string& default_value) {
    std::string search_key = "\"" + key + "\":\"";
    size_t pos = json.find(search_key);
    if (pos == std::string::npos) {
        return default_value;
    }
    
    pos += search_key.length();
    size_t end_pos = json.find("\"", pos);
    if (end_pos == std::string::npos) {
        return default_value;
    }
    
    return json.substr(pos, end_pos - pos);
}

bool get_json_bool_value(const std::string& json, const std::string& key, bool default_value) {
    std::string search_key = "\"" + key + "\":";
    size_t pos = json.find(search_key);
    if (pos == std::string::npos) {
        return default_value;
    }
    
    pos += search_key.length();
    if (json.substr(pos, 4) == "true") {
        return true;
    } else if (json.substr(pos, 5) == "false") {
        return false;
    }
    
    return default_value;
}

// 字符串转换函数
std::wstring utf8_to_wstring(const std::string& utf8) {
    if (utf8.empty()) return std::wstring();
    
    int size_needed = MultiByteToWideChar(CP_UTF8, 0, utf8.c_str(), -1, NULL, 0);
    if (size_needed <= 0) return std::wstring();
    
    std::wstring result(size_needed - 1, 0);
    MultiByteToWideChar(CP_UTF8, 0, utf8.c_str(), -1, &result[0], size_needed);
    return result;
}

std::string wstring_to_utf8(const std::wstring& wide) {
    if (wide.empty()) return std::string();
    
    int size_needed = WideCharToMultiByte(CP_UTF8, 0, wide.c_str(), -1, NULL, 0, NULL, NULL);
    if (size_needed <= 0) return std::string();
    
    std::string result(size_needed - 1, 0);
    WideCharToMultiByte(CP_UTF8, 0, wide.c_str(), -1, &result[0], size_needed, NULL, NULL);
    return result;
}

// 任务管理函数
void register_task(const std::string& taskId) {
    std::lock_guard<std::mutex> lock(g_task_mutex);
    g_cancel_flags[taskId] = false;
    g_pause_flags[taskId] = false;
}

void unregister_task(const std::string& taskId) {
    std::lock_guard<std::mutex> lock(g_task_mutex);
    g_cancel_flags.erase(taskId);
    g_pause_flags.erase(taskId);
}

bool check_task_control(const std::string& taskId, QueryTaskControlCallback queryTaskControlCb, ProgressCallback progressCallback) {
    if (!queryTaskControlCb) return false;
    
    // 检查取消
    if (queryTaskControlCb(taskId, 1)) { // 1 = cancel
        std::lock_guard<std::mutex> lock(g_task_mutex);
        g_cancel_flags[taskId] = true;
        return true;
    }
    
    // 检查暂停
    if (queryTaskControlCb(taskId, 2)) { // 2 = pause
        std::lock_guard<std::mutex> lock(g_task_mutex);
        g_pause_flags[taskId] = true;
        // 暂停处理逻辑可以在这里添加
    }
    
    return false;
}

void report_progress(const std::string& taskId, int progress, ProgressCallback progressCallback) {
    if (progressCallback) {
        progressCallback(taskId, progress, "");
    }
}

// ========================================
// 006_Dll标准接口实现
// ========================================

/*
 * 获取库版本信息 - 符合006_Dll标准
 */
std::string GetLibraryInfo(
    const std::string& params,
    ProgressCallback progressCallback,
    const std::string& taskId,
    QueryTaskControlCallback queryTaskControlCb)
{
    register_task(taskId);
    
    try {
        report_progress(taskId, 0, progressCallback);
        
        if (check_task_control(taskId, queryTaskControlCb, progressCallback)) {
            unregister_task(taskId);
            return create_cancelled_response("Task cancelled by user");
        }
        
        report_progress(taskId, 100, progressCallback);
        
        // 生成库信息响应
        char response[2048];
        sprintf_s(response, sizeof(response),
            "{"
            "\"status\":\"success\","
            "\"message\":\"Library info retrieved successfully\","
            "\"version\":\"2.0.0\","
            "\"build_date\":\"2025-01-16\","
            "\"supported_formats\":[\"VMDK\",\"VHDX\",\"VHD\",\"ISO\",\"IMG\"],"
            "\"min_windows_version\":\"Windows XP SP3\","
            "\"architecture\":\"x86\","
            "\"cpp_standard\":\"C++11\","
            "\"dll_standard\":\"006_Dll_v1.0\","
            "\"compiler_toolset\":\"v120_xp\""
            "}");
        
        unregister_task(taskId);
        return std::string(response);
        
    } catch (const std::exception& e) {
        unregister_task(taskId);
        return create_error_response(std::string("Exception: ") + e.what());
    } catch (...) {
        unregister_task(taskId);
        return create_error_response("Unknown exception occurred");
    }
}

/*
 * 初始化VirtualDiskLib - 符合006_Dll标准
 */
std::string InitializeVirtualDiskLib(
    const std::string& params,
    ProgressCallback progressCallback,
    const std::string& taskId,
    QueryTaskControlCallback queryTaskControlCb)
{
    register_task(taskId);
    
    try {
        report_progress(taskId, 0, progressCallback);
        
        if (check_task_control(taskId, queryTaskControlCb, progressCallback)) {
            unregister_task(taskId);
            return create_cancelled_response("Task cancelled by user");
        }
        
        // 提取参数
        bool check_dependencies = get_json_bool_value(params, "check_dependencies", true);
        bool enable_debug = get_json_bool_value(params, "enable_debug", false);
        
        report_progress(taskId, 20, progressCallback);
        
        // 执行初始化
        std::lock_guard<std::mutex> lock(g_init_mutex);
        
        if (!g_initialized.load()) {
            // 初始化MountImg.c
            int init_result = InitializeImDisk();
            if (init_result != 0 && check_dependencies) {
                unregister_task(taskId);
                char error_response[512];
                sprintf_s(error_response, sizeof(error_response),
                    "{\"status\":\"error\",\"message\":\"Failed to initialize ImDisk dependencies\",\"error_code\":%d}",
                    init_result);
                return std::string(error_response);
            }
            
            report_progress(taskId, 80, progressCallback);
            g_initialized = true;
        }
        
        report_progress(taskId, 100, progressCallback);
        
        // 生成响应
        char response[1024];
        sprintf_s(response, sizeof(response),
            "{"
            "\"status\":\"success\","
            "\"message\":\"VirtualDiskLib initialized successfully\","
            "\"dependencies_checked\":%s,"
            "\"debug_enabled\":%s,"
            "\"already_initialized\":%s"
            "}",
            check_dependencies ? "true" : "false",
            enable_debug ? "true" : "false",
            g_initialized.load() ? "true" : "false");
        
        unregister_task(taskId);
        return std::string(response);
        
    } catch (const std::exception& e) {
        unregister_task(taskId);
        return create_error_response(std::string("Exception: ") + e.what());
    } catch (...) {
        unregister_task(taskId);
        return create_error_response("Unknown exception occurred");
    }
}

/*
 * 清理VirtualDiskLib - 符合006_Dll标准
 */
std::string CleanupVirtualDiskLib(
    const std::string& params,
    ProgressCallback progressCallback,
    const std::string& taskId,
    QueryTaskControlCallback queryTaskControlCb)
{
    register_task(taskId);

    try {
        report_progress(taskId, 0, progressCallback);

        if (check_task_control(taskId, queryTaskControlCb, progressCallback)) {
            unregister_task(taskId);
            return create_cancelled_response("Task cancelled by user");
        }

        // 提取参数
        bool force_cleanup = get_json_bool_value(params, "force_cleanup", false);

        report_progress(taskId, 20, progressCallback);

        // 执行清理
        std::lock_guard<std::mutex> lock(g_init_mutex);

        if (g_initialized.load()) {
            // 清理所有活动任务
            {
                std::lock_guard<std::mutex> task_lock(g_task_mutex);
                if (!force_cleanup && !g_cancel_flags.empty()) {
                    unregister_task(taskId);
                    return create_error_response("Active tasks exist, use force_cleanup to override");
                }

                // 取消所有任务
                for (auto& pair : g_cancel_flags) {
                    pair.second = true;
                }

                // 清理任务映射
                g_cancel_flags.clear();
                g_pause_flags.clear();
            }

            report_progress(taskId, 60, progressCallback);
            g_initialized = false;
        }

        report_progress(taskId, 100, progressCallback);

        // 生成响应
        char response[512];
        sprintf_s(response, sizeof(response),
            "{"
            "\"status\":\"success\","
            "\"message\":\"VirtualDiskLib cleanup completed\","
            "\"resources_freed\":true,"
            "\"force_cleanup_used\":%s"
            "}",
            force_cleanup ? "true" : "false");

        unregister_task(taskId);
        return std::string(response);

    } catch (const std::exception& e) {
        unregister_task(taskId);
        return create_error_response(std::string("Exception: ") + e.what());
    } catch (...) {
        unregister_task(taskId);
        return create_error_response("Unknown exception occurred");
    }
}

/*
 * 挂载虚拟磁盘 - 符合006_Dll标准
 */
std::string MountVirtualDisk(
    const std::string& params,
    ProgressCallback progressCallback,
    const std::string& taskId,
    QueryTaskControlCallback queryTaskControlCb)
{
    register_task(taskId);

    try {
        report_progress(taskId, 0, progressCallback);

        if (check_task_control(taskId, queryTaskControlCb, progressCallback)) {
            unregister_task(taskId);
            return create_cancelled_response("Task cancelled by user");
        }

        // 提取参数
        std::string file_path = get_json_string_value(params, "file_path", "");
        std::string drive = get_json_string_value(params, "drive", "");
        bool readonly = get_json_bool_value(params, "readonly", false);

        // 参数验证
        if (file_path.empty()) {
            unregister_task(taskId);
            return create_error_response("Missing required parameter: file_path");
        }

        if (drive.empty()) {
            unregister_task(taskId);
            return create_error_response("Missing required parameter: drive");
        }

        report_progress(taskId, 20, progressCallback);

        // 文件存在性检查
        std::wstring wide_file_path = utf8_to_wstring(file_path);
        if (GetFileAttributesW(wide_file_path.c_str()) == INVALID_FILE_ATTRIBUTES) {
            unregister_task(taskId);
            return create_error_response("Image file not found: " + file_path);
        }

        report_progress(taskId, 40, progressCallback);

        if (check_task_control(taskId, queryTaskControlCb, progressCallback)) {
            unregister_task(taskId);
            return create_cancelled_response("Task cancelled by user");
        }

        // 设置MountImg.c全局变量
        std::wstring wide_drive = utf8_to_wstring(drive);
        wcscpy_s(filename, MAX_PATH, wide_file_path.c_str());
        wcscpy_s(::drive, 8, wide_drive.c_str());
        ::readonly = readonly ? 1 : 0;
        partition = 1; // 默认分区

        report_progress(taskId, 60, progressCallback);

        // 初始化ImDisk
        if (InitializeImDisk() != 0) {
            unregister_task(taskId);
            return create_error_response("Failed to initialize ImDisk");
        }

        report_progress(taskId, 80, progressCallback);

        // 执行挂载操作
        int mount_result = Imdisk_Mount();

        report_progress(taskId, 100, progressCallback);

        // 生成响应
        if (mount_result == 0) {
            char response[1024];
            sprintf_s(response, sizeof(response),
                "{"
                "\"status\":\"success\","
                "\"message\":\"Mount operation completed successfully\","
                "\"drive_letter\":\"%s\","
                "\"file_system\":\"Unknown\","
                "\"readonly\":%s,"
                "\"size_mb\":0"
                "}",
                drive.c_str(),
                readonly ? "true" : "false");

            unregister_task(taskId);
            return std::string(response);
        } else {
            char response[512];
            sprintf_s(response, sizeof(response),
                "{"
                "\"status\":\"error\","
                "\"message\":\"Mount operation failed\","
                "\"error_code\":%d"
                "}",
                mount_result);

            unregister_task(taskId);
            return std::string(response);
        }

    } catch (const std::exception& e) {
        unregister_task(taskId);
        return create_error_response(std::string("Exception: ") + e.what());
    } catch (...) {
        unregister_task(taskId);
        return create_error_response("Unknown exception occurred");
    }
}

/*
 * 卸载虚拟磁盘 - 符合006_Dll标准
 */
std::string UnmountVirtualDisk(
    const std::string& params,
    ProgressCallback progressCallback,
    const std::string& taskId,
    QueryTaskControlCallback queryTaskControlCb)
{
    register_task(taskId);

    try {
        report_progress(taskId, 0, progressCallback);

        if (check_task_control(taskId, queryTaskControlCb, progressCallback)) {
            unregister_task(taskId);
            return create_cancelled_response("Task cancelled by user");
        }

        // 提取参数
        std::string drive_letter = get_json_string_value(params, "drive", "");

        // 参数验证
        if (drive_letter.empty()) {
            unregister_task(taskId);
            return create_error_response("Missing required parameter: drive");
        }

        report_progress(taskId, 20, progressCallback);

        // 驱动器验证
        std::wstring wide_drive = utf8_to_wstring(drive_letter);
        UINT drive_type = GetDriveTypeW(wide_drive.c_str());
        if (drive_type == DRIVE_NO_ROOT_DIR) {
            unregister_task(taskId);
            return create_error_response("Drive not found: " + drive_letter);
        }

        report_progress(taskId, 40, progressCallback);

        if (check_task_control(taskId, queryTaskControlCb, progressCallback)) {
            unregister_task(taskId);
            return create_cancelled_response("Task cancelled by user");
        }

        // 执行卸载操作（使用MountImg.c的UnmountDrive逻辑）
        WCHAR cmdLine[MAX_PATH + 20];
        _snwprintf(cmdLine, _countof(cmdLine), L"ImDisk-Dlg RM \"%s\"", wide_drive.c_str());

        report_progress(taskId, 60, progressCallback);

        // 调用MountImg.c中的start_process函数
        int unmount_result = start_process(cmdLine, TRUE);

        report_progress(taskId, 100, progressCallback);

        // 生成响应
        if (unmount_result == 0) {
            char response[512];
            sprintf_s(response, sizeof(response),
                "{"
                "\"status\":\"success\","
                "\"message\":\"Unmount operation completed successfully\","
                "\"unmounted_drive\":\"%s\","
                "\"cleanup_completed\":true"
                "}",
                drive_letter.c_str());

            unregister_task(taskId);
            return std::string(response);
        } else {
            char response[512];
            sprintf_s(response, sizeof(response),
                "{"
                "\"status\":\"error\","
                "\"message\":\"Unmount operation failed\","
                "\"error_code\":%d"
                "}",
                unmount_result);

            unregister_task(taskId);
            return std::string(response);
        }

    } catch (const std::exception& e) {
        unregister_task(taskId);
        return create_error_response(std::string("Exception: ") + e.what());
    } catch (...) {
        unregister_task(taskId);
        return create_error_response("Unknown exception occurred");
    }
}

/*
 * 获取挂载状态 - 符合006_Dll标准
 */
std::string GetMountStatus(
    const std::string& params,
    ProgressCallback progressCallback,
    const std::string& taskId,
    QueryTaskControlCallback queryTaskControlCb)
{
    register_task(taskId);

    try {
        report_progress(taskId, 0, progressCallback);

        if (check_task_control(taskId, queryTaskControlCb, progressCallback)) {
            unregister_task(taskId);
            return create_cancelled_response("Task cancelled by user");
        }

        // 提取参数
        std::string drive_letter = get_json_string_value(params, "drive", "");

        report_progress(taskId, 20, progressCallback);

        // 检查所有挂载的驱动器
        char mounted_drives[1024] = "[";
        bool first = true;

        for (char drive = 'A'; drive <= 'Z'; drive++) {
            char drive_path[4] = {drive, ':', '\\', '\0'};
            UINT drive_type = GetDriveTypeA(drive_path);

            if (drive_type != DRIVE_NO_ROOT_DIR && drive_type != DRIVE_UNKNOWN) {
                if (!first) {
                    strcat_s(mounted_drives, sizeof(mounted_drives), ",");
                }
                first = false;

                char drive_info[256];
                sprintf_s(drive_info, sizeof(drive_info),
                    "{"
                    "\"drive\":\"%c:\","
                    "\"type\":%d,"
                    "\"mounted\":true"
                    "}",
                    drive, drive_type);

                strcat_s(mounted_drives, sizeof(mounted_drives), drive_info);
            }

            // 检查任务控制状态
            if (check_task_control(taskId, queryTaskControlCb, progressCallback)) {
                unregister_task(taskId);
                return create_cancelled_response("Task cancelled by user");
            }
        }

        strcat_s(mounted_drives, sizeof(mounted_drives), "]");

        report_progress(taskId, 100, progressCallback);

        // 生成响应
        char response[2048];
        sprintf_s(response, sizeof(response),
            "{"
            "\"status\":\"success\","
            "\"message\":\"Status retrieved successfully\","
            "\"mounted_drives\":%s"
            "}",
            mounted_drives);

        unregister_task(taskId);
        return std::string(response);

    } catch (const std::exception& e) {
        unregister_task(taskId);
        return create_error_response(std::string("Exception: ") + e.what());
    } catch (...) {
        unregister_task(taskId);
        return create_error_response("Unknown exception occurred");
    }
}

// ========================================
// DLL入口点
// ========================================

/*
 * DLL入口点
 */
BOOL APIENTRY DllMain(HMODULE hModule, DWORD ul_reason_for_call, LPVOID lpReserved)
{
    switch (ul_reason_for_call)
    {
    case DLL_PROCESS_ATTACH:
        // C++11全局对象会自动初始化
        break;

    case DLL_PROCESS_DETACH:
        // C++11全局对象会自动析构
        break;

    case DLL_THREAD_ATTACH:
    case DLL_THREAD_DETACH:
        break;
    }
    return TRUE;
}
