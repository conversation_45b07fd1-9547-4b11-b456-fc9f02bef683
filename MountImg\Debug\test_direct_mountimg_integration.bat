@echo off
chcp 65001 >nul
echo ========================================
echo Testing Direct MountImg.c Integration
echo ========================================

echo.
echo 这个测试验证 VirtualDiskLib 直接集成 MountImg.c 挂载逻辑的功能
echo.
echo 实现特点:
echo 1. 直接调用 MountImg.c 中的挂载函数
echo 2. 不通过外部 MountImg32.exe 进程
echo 3. 重用现有的挂载逻辑，避免重复实现
echo 4. 支持 ImDisk 和 DiscUtils 双重挂载策略
echo 5. 完整的挂载验证和错误处理
echo.

echo 实现的关键步骤:
echo Step 1: 解析 JSON 输入参数
echo Step 2: 设置 MountImg.c 全局变量
echo Step 3: 执行挂载流程 (调用 Imdisk_Mount/DiscUtils_Mount)
echo Step 4: 错误处理和挂载验证
echo Step 5: 验证挂载结果并生成响应
echo.

echo 启动 VirtualDiskTool32.exe 测试直接集成...
echo ----------------------------------------

VirtualDiskTool32.exe --test-mount

echo.
echo ----------------------------------------
echo 程序执行完毕，退出码: %ERRORLEVEL%
echo.

if %ERRORLEVEL% EQU 0 (
    echo ✅ SUCCESS: 直接集成模式执行成功
) else (
    echo ❌ FAILED: 程序运行出错，退出码: %ERRORLEVEL%
)

echo.
echo 检查目标 X: 驱动器...
if exist X:\ (
    echo ✅ SUCCESS: X: 驱动器已挂载 (直接集成模式正常工作)
    echo 目录列表:
    dir X: /w
) else (
    echo ❌ FAILED: X: 驱动器未挂载
)

echo.
echo ========================================
echo 直接集成实现技术说明:
echo ========================================
echo.
echo ✅ 外部函数声明 (VirtualDiskLib.h):
echo   extern int Imdisk_Mount(BYTE no_check_fs);
echo   extern int DiscUtils_Mount();
echo   extern DWORD start_process(WCHAR *cmd, BYTE flag);
echo   extern void reg_save();
echo   
echo   extern WCHAR filename[MAX_PATH];
echo   extern WCHAR drive[MAX_PATH + 2];
echo   extern BOOL mount_point, readonly, removable, new_file, win_boot, net_installed;
echo   extern UINT dev_type, partition;
echo   extern long device_number;
echo   extern HANDLE mount_mutex;
echo.
echo ✅ JSON 参数映射:
echo   request.file_path → filename (宽字符转换)
echo   request.drive → drive (宽字符转换)
echo   request.readonly → readonly
echo   request.partition → partition
echo   固定设置: mount_point=FALSE, dev_type=0, removable=FALSE
echo.
echo ✅ 挂载流程 (参考 Mount 函数):
echo   1. WaitForSingleObject(mount_mutex, INFINITE);
echo   2. if (mount_point) wcscpy(drive, mountdir);
echo   3. error = Imdisk_Mount(new_file || !net_installed);
echo   4. if (error && !new_file && net_installed) {
echo          device_number = -1;
echo          error = DiscUtils_Mount();
echo      }
echo   5. if (win_boot) reg_save();
echo.
echo ✅ 挂载验证 (参考 Mount 函数第769-788行):
echo   do {
echo       if (GetVolumeInformation(temp_drive, NULL, 0, NULL, NULL, NULL, NULL, 0)) {
echo           // 挂载成功
echo           break;
echo       } else if (GetLastError() == ERROR_UNRECOGNIZED_VOLUME) {
echo           // 无法识别的卷
echo           break;
echo       }
echo       Sleep(100);
echo   } while (++i < 100);
echo.
echo ✅ 优势:
echo   - 零重复: 完全重用 MountImg.c 中的现有函数
echo   - 高效率: 直接函数调用，无进程间通信开销
echo   - 强兼容: 与 MountImg_Simple 使用相同的挂载逻辑
echo   - 易维护: 集中的代码逻辑，便于维护和调试
echo.

pause
