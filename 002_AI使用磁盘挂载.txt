
MountImg_Simple项目固定不动，

添加两个项目需求如下：
添加位置是MountImg文件夹下，
虚拟磁盘挂载，支持xp以上   vmdk VHDX vhd 等虚拟定向格式

挂载虚拟磁盘至wimdows上面，显示一个分区
程序写成两个两项目，一个DLL，提供接口, 挂载虚拟磁盘接口，卸载虚拟磁盘接口，传入json，返回json，

另一个是exe程序，exe程序调用DLL，传入json，返回json，

要添加的两个项目可以参考MountImg_Simple项目相关，
DLL参考MountImg_Simple项目比较多，
DLL与MountImg_Simple项目的区别在于，
一个是DLL，导出接口，共exe调用，
一个exe，直接可以运行相对应的功能


#include <winternl.h>

保留VirtualDiskTool的现有代码，
添加新的测试功能，需求如下：
对VirtualDiskLib的导出函数全部测试，
使用明文字符串传入



我修改了一部分代码，
测试挂载接口时，测试挂载这几个文件
E:\2G.vmdk
E:\666666.vmdk
E:\002_VHD、vhd.vhd
E:\003_VHDX\VHDX.vhdx
E:\004_VMDK\666666.vmdk

挂载以后等待10秒

测试卸载时，
全部卸载

    strncpy(result, start, len);
这行代码，字符串少了一个字符


// 构建ImDisk命令行
    swprintf(cmdline, sizeof(cmdline) / sizeof(WCHAR),
        L"imdisk -a -f \"%s\" -m %s %s",
        imagePath,
        driveLetter,
        readonly ? L"-p \"r\"" : L"");

参考MountImg_Simple项目里的代码挂载文件，不要使用imdisk命令


MountImg_Simple项目是个界面exe，
有一些是界面操作，界面有交互，
然后执行挂载，
完整学习MountImg_Simple项目怎么挂载的

我修改乐意写代码
MountImg_Simple项目是exe，有界面流程操作，
VirtualDiskTool项目是exe，没有界面流程操作，
 VirtualDiskLib项目是DLL，把挂载，卸载接口封装，参考MountImg_Simple项目实现，
继续全面检查，MountImg_Simple项目和VirtualDiskTool， VirtualDiskLib项目，

Mount operation
      Mount failed
      Error code: 1004
Response: {"success":false,"error_code":1004,"error_message":"Mount operation failed with code -1","drive_letter":""}


分析MountImg_Simple项目流程，分析执行了什么流程和VirtualDiskTool， VirtualDiskLib项目有什么区别：
挂载：
1.界面选择E:\\002_VHD\\vhd.vhd，分区信息更新了，选择一个分区
2.界面选择只读，设别类型：硬盘
3.界面选择驱动器号
4.界面点击“确定”，等待界面出结果

卸载
1.选择驱动器号
2.界面点击“卸载”，等待界面出结果

VirtualDiskLib项目参考分析MountImg_Simple项目流程，
添加备用方案	DiscUtils自动切换
      

MountImg_Simple的完整流程
挂载流程
文件选择 → file_check() → 自动检测文件类型
分区扫描 → list_thread() → 临时挂载每个分区获取信息
界面设置 → 用户选择只读、设备类型、驱动器号
点击确定 → Mount() → Imdisk_Mount() → 双重挂载策略
结果显示 → 界面状态更新和用户反馈
卸载流程
选择驱动器 → 界面选择
点击卸载 → UnmountDrive() → 执行卸载命令
界面更新 → 恢复状态，重新扫描分区
🔄 关键差异对比
特性	MountImg_Simple	VirtualDiskLib	VirtualDiskTool
界面	完整GUI交互	无界面（DLL）	命令行
分区检测	自动扫描显示	简化处理	无检测
挂载策略	双重挂载	单次→双重(已修正)	调用DLL
用户反馈	实时GUI反馈	JSON响应	控制台输出
错误处理	弹窗提示	错误码	文本消息
备用方案	DiscUtils自动切换	仅ImDisk	依赖DLL

VirtualDiskLib项目参考MountImg_Simple项目流程，
点击确定 → Mount() → Imdisk_Mount() → 双重挂载策略



imdisk -a -u 0 -o h,ro,fix -f "E:\002_VHD\vhd.vhd" -b auto


MountImg.c已经添加至VirtualDiskLib项目，
根据这个文件更改流程


VirtualDiskLib项目挂载流程错误，与MountImg.c不一样，
重新分析流程，规划流程，之前的去掉

MountImg.c里的函数，VirtualDiskLib项目里面不要实现，使用MountImg.c里的函数，
相同的函数VirtualDiskLib项目里面删掉

VHDX
vmdk


MountImg.c里面代码说明
if (LOWORD(wParam) == ID_PBUTTON3) {
卸载按钮响应代码
UnmountDrive(LPVOID lpParam)
卸载函数
参考这个，实现卸载功能



MountImg.c里面代码说明
if (LOWORD(wParam) == IDOK) {
挂载的确定按钮响应代码


start_process(cmd_line, TRUE);
开始卸载，弹出提示框，确认框

start_process(WCHAR *cmd, BYTE flag)
开始卸载函数


imdisk -a -u 10 -o hd,ro,fix -f "E:\\666666.vmdk" -b auto

imdisk -a -u 10 -o hd,ro,fix -f "E:\004_VMDK\666666.vmdk" -b auto



#ifdef _USRDLL
int __stdcall wWinMain1(HINSTANCE hinstance, HINSTANCE hPrevInstance, LPWSTR lpCmdLine, int nCmdShow)
#else
int __stdcall wWinMain(HINSTANCE hinstance, HINSTANCE hPrevInstance, LPWSTR lpCmdLine, int nCmdShow)
#endif
函数里面有初始化的一些信息，

再函数
// 初始化函数
BOOL InitializeVirtualDiskLib(void)
里面初始化这些信息


/name=ImDisk1b332a537e4 /filename="E:\004_VMDK\666666.vmdk"
-o shm,hd,rw,fix -f ImDisk1b332a537e4
F:

/name=ImDisk1bd04be21f8 /filename="E:\\004_VMDK\\666666.vmdk"
-o shm,hd,rw,fix -f ImDisk1bd04be21f8
X:


StartService(h_svc, 3, (void*)cmdline_ptr);
此函数在MountImg_Simple项目里面运行成功
此函数在VirtualDiskLib项目里面运行失败

VirtualDiskLib项目参考MountImg_Simple项目流程，
查询是否有其他的地方需要初始化，做什么工作

InitializeMountImg(void)
函数里面的SC_HANDLE h_scman
应该存活在DLL被卸载时，
不能关闭清空太早


#ifdef _USRDLL
int __stdcall wWinMain1(HINSTANCE hinstance, HINSTANCE hPrevInstance, LPWSTR lpCmdLine, int nCmdShow)
#else
int __stdcall wWinMain(HINSTANCE hinstance, HINSTANCE hPrevInstance, LPWSTR lpCmdLine, int nCmdShow)
#endif
从此函数开始，VirtualDiskLib项目参考MountImg_Simple项目初始化，
能够挂载VHDX，vmdk


if (!StartService(h_svc, 3, (LPCWSTR*)cmdline_ptr)) {
VirtualDiskLib项目调用卡顿，结果失败
分析什么原因


VirtualDiskLib项目，卸载接口保持不变，
挂载接口内部实现进行修改，需求如下：
解析json数据，启动VirtualDiskLib32.dll同目录下的MountImg32.exe，
启动MountImg32.exe后，等待MountImg32.exe完成，
最多等待30秒，如果30秒后没有等到结果，返回错误信息，

MountImg32.exe启动时，
把挂载的参数传给MountImg32.exe，
MountImg32.exe启动并接收到参数时，
进行挂载操作，操作完成后，返回成功或失败信息，MountImg32.exe关闭

之前的挂载功能代码，初始化代码，全部注释掉



启动MountImg32.exe的指令，参数
"E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\MountImg\Debug\MountImg32.exe" /JSON "{"file_path":"E:\\004_VMDK\\666666.vmdk","drive":"X:","readonly":false,"partition":1}"


MountImg32.exe启动，收到参数，解析参数界面赋值，执行确定按钮，执行挂载功能，
传输参数：
"E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\MountImg\Debug\MountImg32.exe" /JSON "{\"file_path\":\"E:\\004_VMDK\\666666.vmdk\",\"drive\":\"X:\",\"readonly\":false,\"partition\":1}"
参数说明：
界面镜像文件输入框 对应 file_path：文件路径
驱动器号 下拉框对应 drive：加载驱动号
只读复选框 对应 readonly：只读设置
分区列表框当前选择项 对应 partition：分区索引


本地不实现start_process函数，使用MountImg.c里面的

解析 /JSON "{"file_path":"E:\\004_VMDK\\666666.vmdk","drive":"X:","readonly":false,"partition":1}"
设置界面信息，信息对应关系如下：
ID_COMBO1, filename);
ID_CHECK1, readonly
ID_COMBO2, drive
ID_LISTVIEW, 实现当前文件的所有分区，根据partition设置当前选择项
ID_COMBO1的当前值修改后，
ID_PBUTTON1 选择挂载文件按钮，
if (LOWORD(wParam) == ID_PBUTTON1) {
选择挂载文件按钮响应代码

解析 /JSON "{"file_path":"E:\\004_VMDK\\666666.vmdk","drive":"X:","readonly":false,"partition":1}"
的代码要实下面的函数里
int __stdcall wWinMain(HINSTANCE hinstance, HINSTANCE hPrevInstance, LPWSTR lpCmdLine, int nCmdShow)

MountImg32.cpp文件当中的wWinMain我已经注释，
解析 /JSON "{"file_path":"E:\\004_VMDK\\666666.vmdk","drive":"X:","readonly":false,"partition":1}"
的代码现实在MountImg.c的wWinMain函数里
添加在吗的地方是1152行
if ((argc >= 2 || wcscmp(argv[1], L"/JSON"))) {


检查修改后的代码 - 查看您的修改是否正确
测试功能 - 验证 JSON 参数解析和挂载功能是否正常工作
添加功能 - 如果需要额外的功能

E:\004_VMDK\666666.vmdk


修改wWinMain获取指令的方式，通过控制台输入

if ((os_ver.dwMajorVersion >= 6) && (argc <= 1 || wcscmp(argv[1], L"/UAC"))) {
在代码的前面打印三个字符串
argv[0]
argv[1]
argv[2]



我修改了代码，再基础上修改，

在代码
// 解析JSON数据
位置修改：

解析 /JSON "{"file_path":"E:\\004_VMDK\\666666.vmdk","drive":"X:","readonly":false,"partition":1}"
设置界面信息，信息对应关系如下：
ID_COMBO1, filename);
ID_CHECK1, readonly
ID_COMBO2, drive
ID_LISTVIEW, 实现当前文件的所有分区，根据partition设置当前选择项
ID_COMBO1的当前值修改后，
ID_PBUTTON1 选择挂载文件按钮，
if (LOWORD(wParam) == ID_PBUTTON1) {
选择挂载文件按钮响应代码

"E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\MountImg\Debug\MountImg32.exe" /JSON "{\"file_path\":\"E:\\004_VMDK\\666666.vmdk\",\"drive\":\"X:\",\"readonly\":true,\"partition\":1}"


// 解析JSON数据
此处代码，应用JSON数据解析方式位正式JSON数据解析，






输入的参数是


"E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\MountImg\Debug\MountImg32.exe" /JSON "{\"file_path\":\"E:\\004_VMDK\\666666.vmdk\",\"drive\":\"X:\",\"readonly\":true,\"partition\":1}"






接收到参数是
argv[0] = E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\MountImg\Debug\MountImg32.exe
argv[1] = /JSON
argv[2] = {file_path:E:\\004_VMDK\\666666.vmdk,drive:X:,readonly:false,partition:1}

"不见了，给出原因

printf("  ✓ 驱动器设置完成: %S\n", drive);
此行代码前面添加，根据drive更新ID_COMBO2当前项


			hwnd_combo2 = GetDlgItem(hDlg, ID_COMBO2);

// 开启定时，500毫秒以后，开始执行IDOK按钮点击功能示例：SendMessage
在此处代码添加此功能，启动定时器代码加到此处，不要加到别的地方
在当前代码位置添加启动定时器的代码。在别的位置添加定时器功能，
不要影响别的功能，功能简介


SendMessage(hwnd_combo2, CB_SETCURSEL, i, 0);	

int __stdcall wWinMain(HINSTANCE hinstance, HINSTANCE hPrevInstance, LPWSTR lpCmdLine, int nCmdShow)
函数里面，刚刚添加的 启动定时器 ，代码注释掉
还有代码没有注释


// 开启定时，2000毫秒以后，开始执行IDOK按钮点击功能示例：SendMessage
在此处代码添加此功能，启动定时器代码加到此处，不要加到别的地方
在当前代码位置添加启动定时器的代码。在别的位置添加定时器功能，
不要影响别的功能，功能简介

#ifdef DEBUG_InCMD

#endif // DEBUG_InCMD



UAC 0 "E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\MountImg\Debug\MountImg32.exe" /JSON "{"file_path":"E:\\001_MountVirtualDisk_File\\004_VMDK\\666666.vmdk","drive":"X:","readonly":false,"partition":1}"


argc = 6
argv[0] = E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\MountImg\Debug\MountImg32.exe
argv[1] = /UAC
argv[2] = 28
argv[3] = E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\MountImg\Debug\MountImg32.exe
argv[4] = /JSON
argv[5] = {file_path:E:\\004_VMDK\\666666.vmdk,drive:X:,readonly:false,partition:1}

我修改了一些代码，在这个基础上修改，
/*
 * 卸载虚拟磁盘
 */
VIRTUALDISKLIB_API int UnmountVirtualDisk(const char* jsonInput, char* jsonOutput, int bufferSize)
函数实现，参考别的地方代码实现，不要修改别的功能，简单处理，之前的
参考代码地方如下：
ImDisk-Dlg.c
int __stdcall wWinMain(HINSTANCE hinstance, HINSTANCE hPrevInstance, LPWSTR lpCmdLine, int nCmdShow)
if (!wcscmp(argv[1], L"RM")) {

不使用ImDisk-Dlg.exe RM指令，而是使用if (!wcscmp(argv[1], L"RM")) {
代码

MountImg_Simple项目，执行文件MountImg32.exe，
g_isJSON为真时，
窗口隐藏，不显示界面，后台执行，
能否实现，给出详细方案

VirtualDiskLib项目，
/*
 * 挂载虚拟磁盘 (新实现 - 通过MountImg32.exe)
 */
VIRTUALDISKLIB_API int MountVirtualDisk(const char* jsonInput, char* jsonOutput, int bufferSize)
功能修改，需求如下：
参考MountImg.c，MountImg.c里面存在函数，不要重复实现，
把MountImg.c里面的磁盘文件挂载的流程，实现在MountVirtualDisk里面，
不使用 MountImg32.exe 进程调用，
功能实现简单，不修改别的功能，
给出实现方案，不修改代码


if ((os_ver.dwMajorVersion >= 6) && (argc <= 1 || wcscmp(argv[1], L"/UAC"))) {
		// send non-elevated drive list to the elevated process
		_snwprintf(txt, _countof(txt) - 1, L"/UAC %d %s", GetLogicalDrives(), cmdline_ptr);
#ifdef DEBUG_InCMD
#else
		ShellExecute(NULL, L"runas", argv[0], txt, NULL, SW_SHOWDEFAULT);
		ExitProcess(0);
#endif
	}
VirtualDiskTool项目参照提示代码，实现权限提升

不通过调用 MountImg32.exe 的后台模式来实现挂载功能

如果有MountImg.c里的函数连接问题，
可以把想读应的函数声明放在MountImg.h里面

TASKKILL /F /IM VirtualDiskTool32.exe /T

E:\001_MountVirtualDisk_File\004_VMDK\666666.vmdk

ShellExecute(NULL, L"runas", “.exe”, "/UAC", NULL, SW_SHOWDEFAULT);
启动一个程序 和 管理员运行exe，有什么区别



