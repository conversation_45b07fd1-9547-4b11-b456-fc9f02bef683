@echo off
rem 磁盘挂载工具启动脚本 - 模仿原始结构

if not "%1"=="7" start /min cmd /c ""%~0" 7 %*" & exit /b

set F=%TEMP%\DiskUp%TIME::=%

rem 创建临时目录并解压ZIP文件
mkdir "%F%"
echo Set s=CreateObject("Shell.Application") > "%TEMP%\uz.vbs"
echo s.NameSpace("%F%").CopyHere s.NameSpace("%~dp0files.zip").Items,20 >> "%TEMP%\uz.vbs"
cscript //nologo "%TEMP%\uz.vbs"
del "%TEMP%\uz.vbs"

rem 等待解压完成
ping 127.0.0.1 -n 3 >nul

rem 运行config.exe
if exist "%F%\config.exe" (
    "%F%\config.exe" %2 %3 %4
) else (
    for /r "%F%" %%i in (config.exe) do if exist "%%i" "%%i" %2 %3 %4 & goto :end
)

:end
rd /s /q "%F%"
