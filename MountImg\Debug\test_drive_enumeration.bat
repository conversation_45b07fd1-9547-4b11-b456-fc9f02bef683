@echo off
chcp 65001 >nul
echo ========================================
echo Testing Enhanced Drive Enumeration
echo ========================================

echo.
echo 这个测试验证增强的驱动器枚举功能
echo.
echo 修改前的问题:
echo   ❌ 只枚举 DRIVE_NO_ROOT_DIR (未占用) 驱动器
echo   ❌ F盘等已存在的驱动器被忽略
echo   ❌ 可用驱动器列表不完整
echo.
echo 修改后的改进:
echo   ✅ 枚举所有类型的驱动器 (A-Z)
echo   ✅ 包含已存在的驱动器 (F盘等)
echo   ✅ 显示详细的驱动器状态信息
echo   ✅ 支持所有驱动器类型
echo.

echo 支持的驱动器类型:
echo   - DRIVE_NO_ROOT_DIR (0): 未占用
echo   - DRIVE_REMOVABLE (2): 可移动 (软盘、USB)
echo   - DRIVE_FIXED (3): 固定磁盘 (硬盘)
echo   - DRIVE_REMOTE (4): 网络驱动器
echo   - DRIVE_CDROM (5): 光驱
echo   - DRIVE_RAMDISK (6): 内存盘
echo.

echo 启动 MountImg.exe 测试增强的驱动器枚举...
echo ----------------------------------------

echo 2 | MountImg.exe

echo.
echo ----------------------------------------
echo 程序执行完毕，退出码: %ERRORLEVEL%
echo.

echo 验证系统驱动器状态...
echo.
echo 当前系统驱动器:
for %%d in (A B C D E F G H I J K L M N O P Q R S T U V W X Y Z) do (
    if exist %%d:\ (
        echo   %%d: - 存在
    ) else (
        echo   %%d: - 不存在
    )
)

echo.
echo 检查目标 X: 驱动器...
if exist X:\ (
    echo ✅ SUCCESS: X: 驱动器已挂载
    echo 目录列表:
    dir X: /w
) else (
    echo ❌ FAILED: X: 驱动器未挂载
)

echo.
echo ========================================
echo 增强驱动器枚举技术说明:
echo ========================================
echo.
echo 1. 完整枚举逻辑:
echo    for (WCHAR c = L'A'; c <= L'Z'; c++) {
echo        UINT testDriveType = GetDriveType(testDrive);
echo        // 根据类型决定是否添加到列表
echo    }
echo.
echo 2. 驱动器类型判断:
echo    switch (testDriveType) {
echo        case DRIVE_NO_ROOT_DIR: shouldAdd = TRUE; break;
echo        case DRIVE_REMOVABLE:   shouldAdd = TRUE; break;
echo        case DRIVE_FIXED:       shouldAdd = TRUE; break;
echo        case DRIVE_REMOTE:      shouldAdd = TRUE; break;
echo        case DRIVE_CDROM:       shouldAdd = TRUE; break;
echo        case DRIVE_RAMDISK:     shouldAdd = TRUE; break;
echo    }
echo.
echo 3. 详细状态显示:
echo    printf("      %%C: - %%S (类型: %%d)\n", c, driveTypeStr, testDriveType);
echo.
echo 4. 目标驱动器查找:
echo    if (c == driveLetter) {
echo        driveIndex = driveCount;
echo        printf("      ✓ 找到目标驱动器 %%C: 在索引 %%d\n", c, driveCount);
echo    }
echo.
echo 预期输出示例:
echo   📋 枚举所有驱动器状态:
echo     A: - 未占用 (类型: 0)
echo     B: - 未占用 (类型: 0)
echo     C: - 固定磁盘 (类型: 3)
echo     D: - 光驱 (类型: 5)
echo     E: - 固定磁盘 (类型: 3)
echo     F: - 固定磁盘 (类型: 3)  ← 现在会被包含
echo     ...
echo     X: - 未占用 (类型: 0)
echo     ✓ 找到目标驱动器 X: 在索引 15
echo.

pause
