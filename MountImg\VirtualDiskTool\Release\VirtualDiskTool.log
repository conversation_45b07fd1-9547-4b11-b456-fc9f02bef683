﻿  main.cpp
main.cpp(8): warning C4005: '_CRT_SECURE_NO_WARNINGS' : macro redefinition
          command-line arguments :  see previous definition of '_CRT_SECURE_NO_WARNINGS'
main.cpp(350): warning C4566: character represented by universal-character-name '\UDD0DD83D' cannot be represented in the current code page (936)
main.cpp(360): warning C4566: character represented by universal-character-name '\UDD0DD83D' cannot be represented in the current code page (936)
main.cpp(368): warning C4566: character represented by universal-character-name '\u26A0' cannot be represented in the current code page (936)
main.cpp(368): warning C4566: character represented by universal-character-name '\uFE0F' cannot be represented in the current code page (936)
main.cpp(378): warning C4566: character represented by universal-character-name '\u2705' cannot be represented in the current code page (936)
main.cpp(381): warning C4566: character represented by universal-character-name '\u26A0' cannot be represented in the current code page (936)
main.cpp(381): warning C4566: character represented by universal-character-name '\uFE0F' cannot be represented in the current code page (936)
main.cpp(385): warning C4566: character represented by universal-character-name '\u274C' cannot be represented in the current code page (936)
main.cpp(387): warning C4566: character represented by universal-character-name '\UDEABD83D' cannot be represented in the current code page (936)
main.cpp(388): warning C4566: character represented by universal-character-name '\UDCCBD83D' cannot be represented in the current code page (936)
main.cpp(389): warning C4566: character represented by universal-character-name '\u2022' cannot be represented in the current code page (936)
main.cpp(390): warning C4566: character represented by universal-character-name '\u2022' cannot be represented in the current code page (936)
main.cpp(391): warning C4566: character represented by universal-character-name '\u2022' cannot be represented in the current code page (936)
main.cpp(392): warning C4566: character represented by universal-character-name '\u2022' cannot be represented in the current code page (936)
main.cpp(394): warning C4566: character represented by universal-character-name '\UDEE0D83D' cannot be represented in the current code page (936)
main.cpp(394): warning C4566: character represented by universal-character-name '\uFE0F' cannot be represented in the current code page (936)
main.cpp(408): warning C4566: character represented by universal-character-name '\u26A0' cannot be represented in the current code page (936)
main.cpp(408): warning C4566: character represented by universal-character-name '\uFE0F' cannot be represented in the current code page (936)
  cmdline_parser.cpp
cmdline_parser.cpp(6): warning C4005: '_CRT_SECURE_NO_WARNINGS' : macro redefinition
          command-line arguments :  see previous definition of '_CRT_SECURE_NO_WARNINGS'
  json_builder.cpp
json_builder.cpp(6): warning C4005: '_CRT_SECURE_NO_WARNINGS' : macro redefinition
          command-line arguments :  see previous definition of '_CRT_SECURE_NO_WARNINGS'
json_builder.cpp(232): warning C4566: character represented by universal-character-name '\u2705' cannot be represented in the current code page (936)
json_builder.cpp(243): warning C4566: character represented by universal-character-name '\u274C' cannot be represented in the current code page (936)
json_builder.cpp(264): warning C4566: character represented by universal-character-name '\u2705' cannot be represented in the current code page (936)
json_builder.cpp(271): warning C4566: character represented by universal-character-name '\u274C' cannot be represented in the current code page (936)
json_builder.cpp(297): warning C4566: character represented by universal-character-name '\UDCCAD83D' cannot be represented in the current code page (936)
json_builder.cpp(312): warning C4566: character represented by universal-character-name '\u274C' cannot be represented in the current code page (936)
json_builder.cpp(329): warning C4566: character represented by universal-character-name '\u274C' cannot be represented in the current code page (936)
  test_functions.cpp
test_functions.cpp(6): warning C4005: '_CRT_SECURE_NO_WARNINGS' : macro redefinition
          command-line arguments :  see previous definition of '_CRT_SECURE_NO_WARNINGS'
test_functions.cpp(42): warning C4566: character represented by universal-character-name '\UDCCAD83D' cannot be represented in the current code page (936)
test_functions.cpp(56): warning C4566: character represented by universal-character-name '\u23F8' cannot be represented in the current code page (936)
test_functions.cpp(56): warning C4566: character represented by universal-character-name '\uFE0F' cannot be represented in the current code page (936)
test_functions.cpp(60): warning C4566: character represented by universal-character-name '\u25B6' cannot be represented in the current code page (936)
test_functions.cpp(60): warning C4566: character represented by universal-character-name '\uFE0F' cannot be represented in the current code page (936)
test_functions.cpp(71): warning C4566: character represented by universal-character-name '\UDF9BD83C' cannot be represented in the current code page (936)
test_functions.cpp(71): warning C4566: character represented by universal-character-name '\uFE0F' cannot be represented in the current code page (936)
test_functions.cpp(116): warning C4566: character represented by universal-character-name '\UDCC8D83D' cannot be represented in the current code page (936)
test_functions.cpp(133): warning C4566: character represented by universal-character-name '\UDDEAD83E' cannot be represented in the current code page (936)
test_functions.cpp(154): warning C4566: character represented by universal-character-name '\UDCC1D83D' cannot be represented in the current code page (936)
test_functions.cpp(155): warning C4566: character represented by universal-character-name '\UDFAFD83C' cannot be represented in the current code page (936)
test_functions.cpp(156): warning C4566: character represented by universal-character-name '\UDCCAD83D' cannot be represented in the current code page (936)
test_functions.cpp(198): warning C4566: character represented by universal-character-name '\UDDEAD83E' cannot be represented in the current code page (936)
test_functions.cpp(219): warning C4566: character represented by universal-character-name '\UDCC1D83D' cannot be represented in the current code page (936)
test_functions.cpp(220): warning C4566: character represented by universal-character-name '\UDFAFD83C' cannot be represented in the current code page (936)
test_functions.cpp(221): warning C4566: character represented by universal-character-name '\UDF9BD83C' cannot be represented in the current code page (936)
test_functions.cpp(221): warning C4566: character represented by universal-character-name '\uFE0F' cannot be represented in the current code page (936)
test_functions.cpp(268): warning C4566: character represented by universal-character-name '\UDDEAD83E' cannot be represented in the current code page (936)
test_functions.cpp(287): warning C4566: character represented by universal-character-name '\UDFAFD83C' cannot be represented in the current code page (936)
test_functions.cpp(288): warning C4566: character represented by universal-character-name '\UDCCAD83D' cannot be represented in the current code page (936)
test_functions.cpp(337): warning C4566: character represented by universal-character-name '\UDDEAD83E' cannot be represented in the current code page (936)
test_functions.cpp(347): warning C4566: character represented by universal-character-name '\UDCCBD83D' cannot be represented in the current code page (936)
test_functions.cpp(350): warning C4566: character represented by universal-character-name '\UDD38D83D' cannot be represented in the current code page (936)
test_functions.cpp(360): warning C4566: character represented by universal-character-name '\UDD38D83D' cannot be represented in the current code page (936)
test_functions.cpp(370): warning C4566: character represented by universal-character-name '\UDD38D83D' cannot be represented in the current code page (936)
test_functions.cpp(378): warning C4566: character represented by universal-character-name '\UDD38D83D' cannot be represented in the current code page (936)
test_functions.cpp(383): warning C4566: character represented by universal-character-name '\UDCCAD83D' cannot be represented in the current code page (936)
test_functions.cpp(397): warning C4566: character represented by universal-character-name '\u2705' cannot be represented in the current code page (936)
test_functions.cpp(397): warning C4566: character represented by universal-character-name '\u274C' cannot be represented in the current code page (936)
  privilege_manager.cpp
privilege_manager.cpp(8): warning C4005: '_CRT_SECURE_NO_WARNINGS' : macro redefinition
          command-line arguments :  see previous definition of '_CRT_SECURE_NO_WARNINGS'
  Generating code
  Finished generating code
  VirtualDiskTool.vcxproj -> E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\Release\VirtualDiskTool32.exe
