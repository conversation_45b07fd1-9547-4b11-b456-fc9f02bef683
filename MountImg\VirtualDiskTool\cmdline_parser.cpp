﻿/*
 * cmdline_parser.cpp
 * 命令行参数解析器实现
 */

#define _CRT_SECURE_NO_WARNINGS
#include <stdio.h>
#include <stdlib.h>
#include <string.h>

#include "cmdline_parser.h"

/*
 * 初始化参数结构
 */
void InitCommandLineArgs(CommandLineArgs* args)
{
    if (!args) return;
    
    memset(args, 0, sizeof(CommandLineArgs));
    args->readonly = 0;
    args->partition = 1;
    args->auto_assign = 0;
    args->force = 0;
    args->json_mode = 0;

    // 初始化测试选项
    args->test_all = 0;
    args->test_mount = 0;
    args->test_unmount = 0;
    args->test_status = 0;
    args->test_error = 0;
    args->test_info = 0;
    args->test_callbacks = 0;
}

/*
 * 检查参数是否匹配
 */
static int MatchArg(const char* arg, const char* shortForm, const char* longForm)
{
    if (!arg) return 0;
    
    if (shortForm && strcmp(arg, shortForm) == 0) return 1;
    if (longForm && strcmp(arg, longForm) == 0) return 1;
    
    return 0;
}

/*
 * 获取下一个参数值
 */
static const char* GetNextArgValue(int argc, char* argv[], int* index)
{
    if (*index + 1 >= argc) return NULL;
    
    (*index)++;
    return argv[*index];
}

/*
 * 解析命令行参数
 */
int ParseCommandLine(int argc, char* argv[], CommandLineArgs* args)
{
    if (argc < 2 || !argv || !args) return -1;
    
    // 初始化参数结构
    InitCommandLineArgs(args);
    
    int i = 1;
    
    // 检查JSON模式
    if (MatchArg(argv[i], NULL, "--json")) {
        args->json_mode = 1;
        const char* jsonInput = GetNextArgValue(argc, argv, &i);
        if (!jsonInput) {
            return -1; // JSON输入为空
        }
        strncpy(args->json_input, jsonInput, sizeof(args->json_input) - 1);
        return 0;
    }
    
    // 获取命令
    if (i < argc) {
        strncpy(args->command, argv[i], sizeof(args->command) - 1);
        i++;
    } else {
        return -1; // 没有命令
    }
    
    // 解析选项
    while (i < argc) {
        const char* arg = argv[i];
        
        if (MatchArg(arg, "-f", "--file")) {
            const char* value = GetNextArgValue(argc, argv, &i);
            if (!value) return -1;
            strncpy(args->file_path, value, sizeof(args->file_path) - 1);
            
        } else if (MatchArg(arg, "-d", "--drive")) {
            const char* value = GetNextArgValue(argc, argv, &i);
            if (!value) return -1;
            strncpy(args->drive_letter, value, sizeof(args->drive_letter) - 1);
            
        } else if (MatchArg(arg, "-r", "--readonly")) {
            args->readonly = 1;
            
        } else if (MatchArg(arg, "-p", "--partition")) {
            const char* value = GetNextArgValue(argc, argv, &i);
            if (!value) return -1;
            args->partition = atoi(value);
            if (args->partition < 1) args->partition = 1;
            
        } else if (MatchArg(arg, "-a", "--auto-assign")) {
            args->auto_assign = 1;
            
        } else if (MatchArg(arg, NULL, "--force")) {
            args->force = 1;

        } else if (MatchArg(arg, NULL, "--test-all")) {
            args->test_all = 1;

        } else if (MatchArg(arg, NULL, "--test-mount")) {
            args->test_mount = 1;

        } else if (MatchArg(arg, NULL, "--test-unmount")) {
            args->test_unmount = 1;

        } else if (MatchArg(arg, NULL, "--test-status")) {
            args->test_status = 1;

        } else if (MatchArg(arg, NULL, "--test-error")) {
            args->test_error = 1;

        } else if (MatchArg(arg, NULL, "--test-info")) {
            args->test_info = 1;

        } else if (MatchArg(arg, NULL, "--test-callbacks")) {
            args->test_callbacks = 1;

        } else if (MatchArg(arg, NULL, "--test-file")) {
            const char* value = GetNextArgValue(argc, argv, &i);
            if (!value) return -1;
            strncpy(args->test_file, value, sizeof(args->test_file) - 1);

        } else {
            // 未知参数
            return -1;
        }
        
        i++;
    }
    
    return ValidateCommandLineArgs(args);
}

/*
 * 验证参数有效性
 */
int ValidateCommandLineArgs(const CommandLineArgs* args)
{
    if (!args) return -1;
    
    // 检查命令
    if (strlen(args->command) == 0) return -1;
    
    if (strcmp(args->command, "mount") == 0) {
        // 挂载命令需要文件路径
        if (strlen(args->file_path) == 0) return -1;
        
    } else if (strcmp(args->command, "unmount") == 0) {
        // 卸载命令需要驱动器号
        if (strlen(args->drive_letter) == 0) return -1;
        
    } else if (strcmp(args->command, "status") == 0) {
        // 状态查询需要驱动器号
        if (strlen(args->drive_letter) == 0) return -1;

    } else if (strcmp(args->command, "test") == 0) {
        // 测试命令不需要额外验证

    } else {
        // 未知命令
        return -1;
    }
    
    return 0;
}
