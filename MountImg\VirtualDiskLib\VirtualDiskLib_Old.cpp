﻿/*
 * VirtualDiskLib.cpp
 * 虚拟磁盘挂载库 - 主实现文件
 */

#define _WIN32_WINNT 0x0601
#define OEMRESOURCE
#define _CRT_SECURE_NO_WARNINGS

#include <windows.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <dbt.h>               // 用于设备广播消息
#include <shlwapi.h>           // 用于 PathAddBackslash 函数
#include <winternl.h>

#include "VirtualDiskLib.h"
#include "json_helper.h"       // 仍需要用于UnmountVirtualDisk和GetMountStatus
#include "../MountImg.h"       // MountImg.c的头文件，包含挂载函数声明
#include "../../inc/imdisk.h"  // ImDisk API 定义
#include "../../inc/imdisktk.h" // ImDisk 工具包定义
// 以下头文件在外部进程模式下不再使用，已注释
//#include "mount_core.h"      // 挂载核心逻辑现在在MountImg32.exe中处理

// 全局初始化标志
static BOOL g_bInitialized = FALSE;
static CRITICAL_SECTION g_cs;

// ========================================
// 本地参数存储（避免外部依赖）
// ========================================



/*
 * 将JSON参数映射到MountImg.c全局变量
 */
void SetMountImgParameters(const MountRequest* request)
{
    // 文件路径转换
    MultiByteToWideChar(CP_UTF8, 0, request->file_path, -1, filename, MAX_PATH);

    // 驱动器号转换
    MultiByteToWideChar(CP_UTF8, 0, request->drive, -1, drive, MAX_PATH + 2);

    // 挂载选项设置
    readonly = request->readonly;
    partition = request->partition;

    // 固定参数设置（参考MountImg.c的默认值）
    mount_point = FALSE;        // 使用驱动器号挂载
    removable = FALSE;          // 非可移动设备
    dev_type = 0;              // 硬盘类型（HD）
    new_file = FALSE;          // 现有文件
    win_boot = FALSE;          // 非启动盘
    device_number = -1;        // 自动分配设备号

    // 清空挂载目录（使用驱动器号时不需要）
    mountdir[0] = L'\0';
}

/*
 * 执行挂载操作（直接调用 MountImg.c 函数）
 */
int ExecuteMountOperation()
{
    char debug_info[512];
    OutputDebugStringA("=== Direct MountImg.c Function Calls ===\n");

    sprintf(debug_info, "Mounting: %S -> %S (readonly=%d, partition=%d)\n",
            filename, drive, readonly, partition);
    OutputDebugStringA(debug_info);

    // === 第0步: 初始化 ImDisk ===
    OutputDebugStringA("Step 0: Initializing ImDisk...\n");
    if (InitializeImDisk() != 0) {
        OutputDebugStringA("ERROR: Failed to initialize ImDisk\n");
        return 1;
    }
    OutputDebugStringA("✅ ImDisk initialization successful\n");

    // 输出关键变量状态
    sprintf(debug_info, "net_installed: %s, init_ok: %s, h_svc: %s (0x%p)\n",
            net_installed ? "TRUE" : "FALSE", init_ok ? "TRUE" : "FALSE",
            h_svc ? "VALID" : "NULL", h_svc);
    OutputDebugStringA(debug_info);

    // 如果 h_svc 为空，输出详细的服务状态信息
    if (!h_svc) {
        OutputDebugStringA("WARNING: h_svc is NULL - DiscUtils mount will not be available\n");
        OutputDebugStringA("  Possible causes:\n");
        OutputDebugStringA("  1. ImDisk service not installed\n");
        OutputDebugStringA("  2. Insufficient privileges to access service\n");
        OutputDebugStringA("  3. Service creation failed during initialization\n");
        OutputDebugStringA("  4. OpenSCManager failed\n");
    } else {
        OutputDebugStringA("✅ h_svc is valid - DiscUtils mount is available\n");
    }

    // === 第1步: 等待挂载互斥锁（参考Mount函数第739行） ===
    OutputDebugStringA("Step 1: Waiting for mount mutex...\n");
    if (mount_mutex) {
        WaitForSingleObject(mount_mutex, INFINITE);
    }

    // === 第2步: 设置挂载目标（参考Mount函数第742行） ===
    if (mount_point) {
        wcscpy(drive, mountdir);
    }

    // === 第3步: 尝试ImDisk挂载（参考Mount函数第744行） ===
    OutputDebugStringA("Step 3: Attempting ImDisk mount...\n");

    BYTE no_check_fs = new_file || !net_installed;
    sprintf(debug_info, "ImDisk mount parameters: no_check_fs=%s (new_file=%s, net_installed=%s)\n",
            no_check_fs ? "TRUE" : "FALSE",
            new_file ? "TRUE" : "FALSE",
            net_installed ? "TRUE" : "FALSE");
    OutputDebugStringA(debug_info);

    int error = Imdisk_Mount(no_check_fs);

    sprintf(debug_info, "ImDisk mount result: %d\n", error);
    OutputDebugStringA(debug_info);

    // === 第4步: 如果ImDisk失败，尝试DiscUtils（参考Mount函数第745-748行） ===
    if (error && !new_file && net_installed) {
        OutputDebugStringA("Step 4: ImDisk failed, trying DiscUtils mount...\n");
        sprintf(debug_info, "DiscUtils conditions: error=%d, new_file=%s, net_installed=%s\n",
                error, new_file ? "TRUE" : "FALSE", net_installed ? "TRUE" : "FALSE");
        OutputDebugStringA(debug_info);

        device_number = -1;
        error = DiscUtils_Mount();

        sprintf(debug_info, "DiscUtils mount result: %d\n", error);
        OutputDebugStringA(debug_info);
    } else {
        sprintf(debug_info, "Skipping DiscUtils: error=%d, new_file=%s, net_installed=%s\n",
                error, new_file ? "TRUE" : "FALSE", net_installed ? "TRUE" : "FALSE");
        OutputDebugStringA(debug_info);
    }

    // === 第5步: 保存启动配置（参考Mount函数第767行） ===
    if (win_boot) {
        OutputDebugStringA("Step 5: Saving boot configuration...\n");
        reg_save();
    }

    // === 第6步: 释放互斥锁 ===
    if (mount_mutex) {
        ReleaseMutex(mount_mutex);
    }

    if (error == 0) {
        OutputDebugStringA("✅ Mount operation successful\n");
    } else {
        OutputDebugStringA("❌ Mount operation failed\n");
    }

    return error;
}

/*
 * 验证挂载结果（参考Mount函数第769-788行）
 */
int VerifyMountResult(const char* drive_letter)
{
    WCHAR temp_drive[MAX_PATH + 2];
    wcscpy(temp_drive, drive);  // 使用MountImg.c的全局变量

    // 如果是挂载点，添加反斜杠
    if (mount_point) {
        PathAddBackslash(temp_drive);
    }

    // 循环验证挂载结果（参考Mount函数的验证逻辑）
    int i = 0;
    do {
        if (GetVolumeInformation(temp_drive, NULL, 0, NULL, NULL, NULL, NULL, 0)) {
            // 挂载成功
            return 0;
        } else if (GetLastError() == ERROR_UNRECOGNIZED_VOLUME) {
            // 无法识别的卷
            return -1;
        }
        Sleep(100);
    } while (++i < 100);  // 最多等待10秒

    // 验证超时
    return -2;
}

///*
// * 本地实现的start_process函数 (基于MountImg.c第135-170行)
// */
//static DWORD start_process(WCHAR *cmd, BYTE flag)
//{
//    STARTUPINFOW si = {sizeof(si)};
//    PROCESS_INFORMATION pi;
//    DWORD exitCode = 0;
//
//    char debug_info[512];
//    sprintf(debug_info, "start_process: Executing command: %S\n", cmd);
//    OutputDebugStringA(debug_info);
//    sprintf(debug_info, "start_process: Flag: %d\n", flag);
//    OutputDebugStringA(debug_info);
//
//    // 启动进程
//    if (!CreateProcessW(NULL, cmd, NULL, NULL, FALSE, 0, NULL, NULL, &si, &pi)) {
//        DWORD error = GetLastError();
//        sprintf(debug_info, "start_process: CreateProcess failed, error: %lu\n", error);
//        OutputDebugStringA(debug_info);
//        return error;
//    }
//
//    sprintf(debug_info, "start_process: Process created, PID: %lu\n", pi.dwProcessId);
//    OutputDebugStringA(debug_info);
//
//    // 如果flag为TRUE，等待进程完成
//    if (flag) {
//        OutputDebugStringA("start_process: Waiting for process completion...\n");
//        WaitForSingleObject(pi.hProcess, INFINITE);
//        GetExitCodeProcess(pi.hProcess, &exitCode);
//        sprintf(debug_info, "start_process: Process completed with exit code: %lu\n", exitCode);
//        OutputDebugStringA(debug_info);
//    }
//
//    CloseHandle(pi.hProcess);
//    CloseHandle(pi.hThread);
//
//    return exitCode;
//}

/*
 * 获取DLL所在目录路径
 */
static BOOL GetCurrentDllDirectory(char* dllDir, size_t bufferSize)
{
    HMODULE hModule = NULL;
    if (!GetModuleHandleExA(GET_MODULE_HANDLE_EX_FLAG_FROM_ADDRESS |
                           GET_MODULE_HANDLE_EX_FLAG_UNCHANGED_REFCOUNT,
                           (LPCSTR)&GetCurrentDllDirectory, &hModule)) {
        return FALSE;
    }

    char dllPath[MAX_PATH];
    if (GetModuleFileNameA(hModule, dllPath, sizeof(dllPath)) == 0) {
        return FALSE;
    }

    // 提取目录路径
    char* lastSlash = strrchr(dllPath, '\\');
    if (lastSlash) {
        size_t dirLen = lastSlash - dllPath;
        if (dirLen >= bufferSize) {
            return FALSE;
        }
        strncpy_s(dllDir, bufferSize, dllPath, dirLen);
        dllDir[dirLen] = '\0';
        return TRUE;
    }

    return FALSE;
}

/*
 * 启动MountImg32.exe并等待结果
 */
static int LaunchMountImgProcess(const char* jsonInput, char* jsonOutput, int bufferSize)
{
    char dllDir[MAX_PATH];
    char exePath[MAX_PATH];
    char commandLine[2048];
    STARTUPINFOA si = {0};
    PROCESS_INFORMATION pi = {0};
    DWORD exitCode = 0;
    DWORD waitResult;

    // 获取DLL所在目录
    if (!GetCurrentDllDirectory(dllDir, sizeof(dllDir))) {
        OutputDebugStringA("ERROR: Failed to get DLL directory\n");
        return VDL_ERROR_INTERNAL;
    }

    // 构建MountImg32.exe路径
    sprintf_s(exePath, sizeof(exePath), "%s\\MountImg32.exe", dllDir);

    char debug_info[512];
    sprintf(debug_info, "MountImg32.exe path: %s\n", exePath);
    OutputDebugStringA(debug_info);

    // 检查文件是否存在
    if (GetFileAttributesA(exePath) == INVALID_FILE_ATTRIBUTES) {
        OutputDebugStringA("ERROR: MountImg32.exe not found\n");
        sprintf(debug_info, "Expected path: %s\n", exePath);
        OutputDebugStringA(debug_info);
        return VDL_ERROR_FILE_NOT_FOUND;
    }

    // 构建命令行参数 (将JSON作为命令行参数传递)
    sprintf_s(commandLine, sizeof(commandLine), "\"%s\" /JSON \"%s\"", exePath, jsonInput);

    sprintf(debug_info, "Command line: %s\n", commandLine);
    OutputDebugStringA(debug_info);

    // 设置启动信息
    si.cb = sizeof(STARTUPINFOA);
    si.dwFlags = STARTF_USESHOWWINDOW;
    si.wShowWindow = SW_HIDE; // 隐藏窗口

    OutputDebugStringA("🚀 Launching MountImg32.exe...\n");

    OutputDebugStringA(commandLine);

    OutputDebugStringA("\n");

    // 启动进程
    if (!CreateProcessA(NULL, commandLine, NULL, NULL, FALSE,
                       CREATE_NO_WINDOW, NULL, dllDir, &si, &pi)) {
        DWORD error = GetLastError();
        sprintf(debug_info, "ERROR: Failed to create process, error code: %lu\n", error);
        OutputDebugStringA(debug_info);
        return VDL_ERROR_PROCESS_FAILED;
    }

    OutputDebugStringA("✅ Process created successfully\n");
    sprintf(debug_info, "Process ID: %lu\n", pi.dwProcessId);
    OutputDebugStringA(debug_info);

    // 关闭线程句柄，我们不需要它
    CloseHandle(pi.hThread);

    // 等待进程完成，最多30秒
    OutputDebugStringA("⏳ Waiting for process completion (timeout: 30 seconds)...\n");
    DWORD startTime = GetTickCount();

    waitResult = WaitForSingleObject(pi.hProcess, 30000); // 30秒超时

    DWORD elapsedTime = GetTickCount() - startTime;
    sprintf(debug_info, "Wait completed: result=%lu, elapsed=%lu ms\n", waitResult, elapsedTime);
    OutputDebugStringA(debug_info);

    if (waitResult == WAIT_TIMEOUT) {
        OutputDebugStringA("❌ Process timeout (30 seconds)\n");
        TerminateProcess(pi.hProcess, 1);
        CloseHandle(pi.hProcess);
        return VDL_ERROR_TIMEOUT;
    } else if (waitResult != WAIT_OBJECT_0) {
        OutputDebugStringA("❌ Wait failed\n");
        CloseHandle(pi.hProcess);
        return VDL_ERROR_PROCESS_FAILED;
    }

    // 获取进程退出码
    if (!GetExitCodeProcess(pi.hProcess, &exitCode)) {
        OutputDebugStringA("❌ Failed to get exit code\n");
        CloseHandle(pi.hProcess);
        return VDL_ERROR_PROCESS_FAILED;
    }

    CloseHandle(pi.hProcess);

    sprintf(debug_info, "✅ Process completed with exit code: %lu\n", exitCode);
    OutputDebugStringA(debug_info);

    // 根据退出码判断结果
    if (exitCode == 0) {
        // 成功 - 构建成功响应
        const char* successJson = "{\"success\":true,\"error_code\":0,\"drive_letter\":\"\",\"file_system\":\"\",\"image_path\":\"\",\"size_bytes\":0,\"size_mb\":0,\"mreadonly\":false,\"is_mounted\":false,\"mount_time\":\"\",\"message\":\"Mount successful\"}";
        strncpy_s(jsonOutput, bufferSize, successJson, _TRUNCATE);
        return VDL_SUCCESS;
    } else {
        // 失败 - 构建错误响应
        char errorJson[512];
        sprintf_s(errorJson, sizeof(errorJson),
                 "{\"success\":false,\"error_code\":%lu,\"error_message\":\"Mount operation failed with code %lu\",\"drive_letter\":\"\"}",
                 exitCode, exitCode);
        strncpy_s(jsonOutput, bufferSize, errorJson, _TRUNCATE);
        return VDL_ERROR_MOUNT_FAILED;
    }
}

/*
 * DLL入口点
 */
BOOL APIENTRY DllMain(HMODULE hModule, DWORD ul_reason_for_call, LPVOID lpReserved)
{
    switch (ul_reason_for_call)
    {
    case DLL_PROCESS_ATTACH:
        // 初始化临界区
        InitializeCriticalSection(&g_cs);
        //// 初始化挂载核心
        //if (InitMountCore() == 0) {
        //    g_bInitialized = TRUE;
        //}
        break;
        
    case DLL_PROCESS_DETACH:
        //// 清理资源
        //if (g_bInitialized) {
        //    CleanupMountCore();
        //    g_bInitialized = FALSE;
        //}
        DeleteCriticalSection(&g_cs);
        break;
        
    case DLL_THREAD_ATTACH:
    case DLL_THREAD_DETACH:
        break;
    }
    return TRUE;
}

/*
 * 挂载虚拟磁盘 (新实现 - 通过MountImg32.exe)
 */
VIRTUALDISKLIB_API int MountVirtualDisk(const char* jsonInput, char* jsonOutput, int bufferSize)
{
#if 0
    // 输出API调用调试信息
    OutputDebugStringA("========================================\n");
    OutputDebugStringA("VirtualDiskLib: MountVirtualDisk called (External Process Mode)\n");
    OutputDebugStringA("========================================\n");

    char debug_info[512];
    sprintf(debug_info, "JSON Input: %s\n", jsonInput ? jsonInput : "NULL");
    OutputDebugStringA(debug_info);
    sprintf(debug_info, "Buffer Size: %d\n", bufferSize);
    OutputDebugStringA(debug_info);

    // 参数验证
    if (!jsonInput || !jsonOutput || bufferSize <= 0) {
        OutputDebugStringA("ERROR: Invalid parameters\n");
        return VDL_ERROR_INVALID_PARAMETER;
    }

    // 清空输出缓冲区
    memset(jsonOutput, 0, bufferSize);

    //// 检查初始化状态
    //if (!g_bInitialized) {
    //    OutputDebugStringA("ERROR: Library not initialized\n");
    //    const char* errorJson = "{\"success\":false,\"error_code\":1011,\"error_message\":\"Library not initialized\"}";
    //    strncpy_s(jsonOutput, bufferSize, errorJson, _TRUNCATE);
    //    return VDL_ERROR_NOT_INITIALIZED;
    //}

    EnterCriticalSection(&g_cs);

    int result = VDL_SUCCESS;

    // 简单验证JSON格式 (确保基本参数存在)
    if (!jsonInput || strlen(jsonInput) < 10) {
        OutputDebugStringA("ERROR: Invalid JSON input\n");
        result = VDL_ERROR_INVALID_JSON;
        const char* errorJson = "{\"success\":false,\"error_code\":1001,\"error_message\":\"Invalid JSON format\"}";
        strncpy_s(jsonOutput, bufferSize, errorJson, _TRUNCATE);
        goto cleanup;
    }

    // 验证file_path参数存在
    if (!strstr(jsonInput, "file_path")) {
        OutputDebugStringA("ERROR: file_path parameter is required\n");
        result = VDL_ERROR_INVALID_PARAMETER;
        const char* errorJson = "{\"success\":false,\"error_code\":1010,\"error_message\":\"file_path is required\"}";
        strncpy_s(jsonOutput, bufferSize, errorJson, _TRUNCATE);
        goto cleanup;
    }

    // ===== 旧的内部挂载代码已注释 (现在使用外部进程模式) =====
    /*
    // 以下代码为原来的内部挂载实现，现已改为外部进程模式
    // 保留代码以备将来参考或回滚需要

    // 验证JSON格式
    MountRequest request = {0};
    if (ParseMountRequest(jsonInput, &request) != 0) {
        OutputDebugStringA("ERROR: Failed to parse JSON input\n");
        result = VDL_ERROR_INVALID_JSON;
        const char* errorJson = "{\"success\":false,\"error_code\":1001,\"error_message\":\"Invalid JSON format\"}";
        strncpy_s(jsonOutput, bufferSize, errorJson, _TRUNCATE);
        goto cleanup;
    }

    // 输出解析结果
    OutputDebugStringA("=== Parsed Mount Request ===\n");
    sprintf(debug_info, "  file_path: %s\n", request.file_path);
    OutputDebugStringA(debug_info);
    sprintf(debug_info, "  drive: %s\n", request.drive);
    OutputDebugStringA(debug_info);
    sprintf(debug_info, "  readonly: %d\n", request.readonly);
    OutputDebugStringA(debug_info);
    sprintf(debug_info, "  partition: %d\n", request.partition);
    OutputDebugStringA(debug_info);

    // 验证必需参数
    if (!request.file_path[0]) {
        OutputDebugStringA("ERROR: file_path is required\n");
        result = VDL_ERROR_INVALID_PARAMETER;
        const char* errorJson = "{\"success\":false,\"error_code\":1010,\"error_message\":\"file_path is required\"}";
        strncpy_s(jsonOutput, bufferSize, errorJson, _TRUNCATE);
        goto cleanup;
    }
    */

    // 启动外部进程执行挂载
    OutputDebugStringA("=== Starting External Mount Process ===\n");
    result = LaunchMountImgProcess(jsonInput, jsonOutput, bufferSize);

cleanup:
    LeaveCriticalSection(&g_cs);

    OutputDebugStringA("========================================\n");
    OutputDebugStringA("VirtualDiskLib: MountVirtualDisk completed (External Process Mode)\n");
    //char debug_info[256];
    sprintf(debug_info, "Final return code: %d\n", result);
    OutputDebugStringA(debug_info);
    OutputDebugStringA("========================================\n");

    return result;
#else
    // 输出API调用调试信息
    OutputDebugStringA("========================================\n");
    OutputDebugStringA("VirtualDiskLib: MountVirtualDisk called (Direct MountImg.c Integration)\n");
    OutputDebugStringA("========================================\n");

    char debug_info[512];
    sprintf(debug_info, "JSON Input: %s\n", jsonInput ? jsonInput : "NULL");
    OutputDebugStringA(debug_info);
    sprintf(debug_info, "Buffer Size: %d\n", bufferSize);
    OutputDebugStringA(debug_info);

    // === 第1步: 参数验证和JSON解析 ===
    if (!jsonInput || !jsonOutput || bufferSize <= 0) {
        OutputDebugStringA("ERROR: Invalid parameters\n");
        return VDL_ERROR_INVALID_PARAMETER;
    }

    // 清空输出缓冲区
    memset(jsonOutput, 0, bufferSize);

    EnterCriticalSection(&g_cs);

    int result = VDL_SUCCESS;
    MountRequest request = {0};
    MountResponse response = {0};

    OutputDebugStringA("=== Step 1: Parsing JSON Input ===\n");
    if (ParseMountRequest(jsonInput, &request) != 0) {
        OutputDebugStringA("ERROR: Failed to parse JSON input\n");
        result = VDL_ERROR_INVALID_JSON;
        strcpy_s(response.error_message, sizeof(response.error_message), "Invalid JSON format");
        goto cleanup;
    }

    // 输出解析结果
    sprintf(debug_info, "Parsed: file_path=%s, drive=%s, readonly=%d, partition=%d\n",
            request.file_path, request.drive, request.readonly, request.partition);
    OutputDebugStringA(debug_info);

    // 验证必需参数
    if (!request.file_path[0]) {
        OutputDebugStringA("ERROR: file_path is required\n");
        result = VDL_ERROR_INVALID_PARAMETER;
        strcpy_s(response.error_message, sizeof(response.error_message), "file_path is required");
        goto cleanup;
    }

    if (!request.drive[0]) {
        OutputDebugStringA("ERROR: drive is required\n");
        result = VDL_ERROR_INVALID_PARAMETER;
        strcpy_s(response.error_message, sizeof(response.error_message), "drive is required");
        goto cleanup;
    }

    // === 第2步: 设置MountImg.c全局变量 ===
    OutputDebugStringA("=== Step 2: Setting MountImg.c Global Variables ===\n");
    SetMountImgParameters(&request);

    sprintf(debug_info, "Set: filename=%S, drive=%S, readonly=%d, partition=%d\n",
            filename, drive, readonly, partition);
    OutputDebugStringA(debug_info);

    // === 第3步: 执行挂载操作 ===
    OutputDebugStringA("=== Step 3: Executing Mount Operation ===\n");
    int mount_error = ExecuteMountOperation();

    sprintf(debug_info, "Mount operation result: %d\n", mount_error);
    OutputDebugStringA(debug_info);

    if (mount_error != 0) {
        OutputDebugStringA("ERROR: Mount operation failed\n");
        result = VDL_ERROR_MOUNT_FAILED;
        strcpy_s(response.error_message, sizeof(response.error_message), "Mount operation failed");
        goto cleanup;
    }

    // === 第4步: 验证挂载结果 ===
    OutputDebugStringA("=== Step 4: Verifying Mount Result ===\n");
    int verify_result = VerifyMountResult(request.drive);

    sprintf(debug_info, "Mount verification result: %d\n", verify_result);
    OutputDebugStringA(debug_info);

    if (verify_result == 0) {
        // 挂载成功
        OutputDebugStringA("✅ Mount verification successful\n");
        response.success = 1;
        response.error_code = 0;
        strcpy_s(response.drive_letter, sizeof(response.drive_letter), request.drive);
        strcpy_s(response.message, sizeof(response.message), "Mount successful");
    } else {
        // 挂载失败
        OutputDebugStringA("❌ Mount verification failed\n");
        result = VDL_ERROR_MOUNT_FAILED;
        if (verify_result == -1) {
            strcpy_s(response.error_message, sizeof(response.error_message), "Unrecognized volume");
        } else {
            strcpy_s(response.error_message, sizeof(response.error_message), "Mount verification timeout");
        }
    }

cleanup:
    // === 第5步: 生成响应 ===
    response.error_code = result;

    // 生成响应JSON
    int jsonResult = GenerateMountResponse(&response, jsonOutput, bufferSize);
    if (jsonResult != 0) {
        result = VDL_ERROR_BUFFER_TOO_SMALL;
    }

    LeaveCriticalSection(&g_cs);

    OutputDebugStringA("========================================\n");
    OutputDebugStringA("VirtualDiskLib: MountVirtualDisk completed (Direct MountImg.c Integration)\n");
    sprintf(debug_info, "Final return code: %d\n", result);
    OutputDebugStringA(debug_info);
    OutputDebugStringA("========================================\n");

    return result;


#endif
}

/*
 * 卸载虚拟磁盘
 */
VIRTUALDISKLIB_API int UnmountVirtualDisk(const char* jsonInput, char* jsonOutput, int bufferSize)
{
#if 0
    // 参数验证
    if (!jsonInput || !jsonOutput || bufferSize <= 0) {
        return VDL_ERROR_INVALID_PARAMETER;
    }
    
    // 清空输出缓冲区
    memset(jsonOutput, 0, bufferSize);
    
    //// 检查初始化状态
    //if (!g_bInitialized) {
    //    return GenerateErrorResponse(VDL_ERROR_UNMOUNT_FAILED, 
    //        "Library not initialized", jsonOutput, bufferSize);
    //}
    
    EnterCriticalSection(&g_cs);
    
    MountRequest request;
    MountResponse response;
    int result = VDL_SUCCESS;
    
    // 初始化结构
    memset(&request, 0, sizeof(request));
    memset(&response, 0, sizeof(response));
    
    // 解析JSON请求
    if (ParseMountRequest(jsonInput, &request) != 0) {
        result = VDL_ERROR_INVALID_JSON;
        strcpy_s(response.error_message, sizeof(response.error_message), 
                "Invalid JSON format");
        goto cleanup;
    }
    
    // 验证驱动器号
    if (strlen(request.drive) == 0) {
        result = VDL_ERROR_INVALID_PARAMETER;
        strcpy_s(response.error_message, sizeof(response.error_message), 
                "Drive letter is required");
        goto cleanup;
    }
    
    // 执行卸载操作 - 使用MountImg.c中的直接函数调用
    OutputDebugStringA("=== Starting Unmount Operation ===\n");
    char debug_info[256];
    sprintf(debug_info, "Unmounting drive: %s\n", request.drive);
    OutputDebugStringA(debug_info);

    // 转换驱动器号为宽字符
    WCHAR wDrive[8];
    MultiByteToWideChar(CP_UTF8, 0, request.drive, -1, wDrive, 8);

    // 直接实现卸载逻辑 (基于MountImg.c第424-438行的UnmountDrive函数)
    WCHAR cmdLine[MAX_PATH + 20];
    _snwprintf(cmdLine, _countof(cmdLine), L"ImDisk-Dlg RM \"%s\"", wDrive);

    sprintf(debug_info, "Executing command: ImDisk-Dlg RM \"%S\"\n", wDrive);
    OutputDebugStringA(debug_info);

    // 调用MountImg.c中的start_process函数
    int unmountResult = start_process(cmdLine, TRUE);

    sprintf(debug_info, "Unmount result: %d\n", unmountResult);
    OutputDebugStringA(debug_info);

    if (unmountResult == 0) {
        // 卸载成功
        response.success = 1;
        response.error_code = 0;
        strcpy_s(response.drive_letter, sizeof(response.drive_letter), request.drive);
        strcpy_s(response.message, sizeof(response.message), "Unmount successful");
        OutputDebugStringA("✅ Unmount operation successful\n");
    } else {
        // 卸载失败
        result = VDL_ERROR_UNMOUNT_FAILED;
        _snprintf_s(response.error_message, sizeof(response.error_message), _TRUNCATE,
                   "Unmount operation failed with code %d", unmountResult);
        OutputDebugStringA("❌ Unmount operation failed\n");
    }
    
cleanup:
    response.error_code = result;
    
    // 生成响应JSON
    int jsonResult = GenerateMountResponse(&response, jsonOutput, bufferSize);
    if (jsonResult != 0) {
        result = VDL_ERROR_BUFFER_TOO_SMALL;
    }
    
    LeaveCriticalSection(&g_cs);
    return result;
#else
    // 参数验证
    if (!jsonInput || !jsonOutput || bufferSize <= 0) {
        return VDL_ERROR_INVALID_PARAMETER;
    }

    // 清空输出缓冲区
    memset(jsonOutput, 0, bufferSize);

    //// 检查初始化状态
    //if (!g_bInitialized) {
    //    return GenerateErrorResponse(VDL_ERROR_UNMOUNT_FAILED, 
    //        "Library not initialized", jsonOutput, bufferSize);
    //}

    EnterCriticalSection(&g_cs);

    MountRequest request;
    MountResponse response;
    int result = VDL_SUCCESS;

    struct { IMDISK_CREATE_DATA icd; WCHAR buff[MAX_PATH + 15]; } create_data = {};
    HMODULE h_cpl = NULL;

    // 初始化结构
    memset(&request, 0, sizeof(request));
    memset(&response, 0, sizeof(response));

    // 解析JSON请求
    if (ParseMountRequest(jsonInput, &request) != 0) {
        result = VDL_ERROR_INVALID_JSON;
        strcpy_s(response.error_message, sizeof(response.error_message),
            "Invalid JSON format");
        goto cleanup;
    }

    // 验证驱动器号
    if (strlen(request.drive) == 0) {
        result = VDL_ERROR_INVALID_PARAMETER;
        strcpy_s(response.error_message, sizeof(response.error_message),
            "Drive letter is required");
        goto cleanup;
    }

    // 执行卸载操作 - 直接实现 ImDisk-Dlg.c RM 代码块逻辑
    OutputDebugStringA("=== Starting Unmount Operation (Direct RM Logic) ===\n");
    char debug_info[256];
    sprintf(debug_info, "Unmounting drive: %s\n", request.drive);
    OutputDebugStringA(debug_info);

    // 转换驱动器号为宽字符
    WCHAR mount_point[8];
    MultiByteToWideChar(CP_UTF8, 0, request.drive, -1, mount_point, 8);

    // 声明所有变量 (避免 goto 跳过初始化的问题)
    HANDLE h = INVALID_HANDLE_VALUE;
    DWORD access_list[] = { GENERIC_READ | GENERIC_WRITE, GENERIC_READ, GENERIC_WRITE };
    int n_access;
    //struct { IMDISK_CREATE_DATA icd; WCHAR buff[MAX_PATH + 15]; } create_data = {};
    DWORD dw;
    DEV_BROADCAST_VOLUME dbv;  // 简单声明，避免复杂初始化
    DWORD_PTR dwp;
    //HMODULE h_cpl = NULL;

    // 初始化 dbv 结构
    memset(&dbv, 0, sizeof(dbv));
    dbv.dbcv_size = sizeof(dbv);
    dbv.dbcv_devicetype = DBT_DEVTYP_VOLUME;

    // 参考 ImDisk-Dlg.c 第425-429行: 动态加载 ImDisk 函数
    OutputDebugStringA("Step 0: Loading ImDisk functions...\n");
    h_cpl = LoadLibraryA("imdisk.cpl");
    if (!h_cpl) {
        result = VDL_ERROR_UNMOUNT_FAILED;
        strcpy_s(response.error_message, sizeof(response.error_message), "Cannot find imdisk.cpl");
        OutputDebugStringA("❌ Cannot find imdisk.cpl\n");
        goto cleanup;
    }

    // 获取 ImDiskOpenDeviceByMountPoint 函数指针
    typedef HANDLE (WINAPI *ImDiskOpenDeviceByMountPointProc)(LPCWSTR, DWORD);
    ImDiskOpenDeviceByMountPointProc ImDiskOpenDeviceByMountPoint =
        (ImDiskOpenDeviceByMountPointProc)GetProcAddress(h_cpl, "ImDiskOpenDeviceByMountPoint");

    if (!ImDiskOpenDeviceByMountPoint) {
        result = VDL_ERROR_UNMOUNT_FAILED;
        strcpy_s(response.error_message, sizeof(response.error_message), "Cannot find ImDiskOpenDeviceByMountPoint function");
        OutputDebugStringA("❌ Cannot find ImDiskOpenDeviceByMountPoint function\n");
        FreeLibrary(h_cpl);
        goto cleanup;
    }

    OutputDebugStringA("✅ ImDisk functions loaded successfully\n");

    // 参考 ImDisk-Dlg.c 第449-454行: 打开设备
    OutputDebugStringA("Step 1: Opening ImDisk device...\n");
    for (n_access = 0; n_access < _countof(access_list); n_access++) {
        h = (HANDLE)ImDiskOpenDeviceByMountPoint(mount_point, access_list[n_access]);
        if (h != INVALID_HANDLE_VALUE) break;
    }

    if (h == INVALID_HANDLE_VALUE) {
        result = VDL_ERROR_UNMOUNT_FAILED;
        strcpy_s(response.error_message, sizeof(response.error_message), "Cannot open ImDisk device");
        OutputDebugStringA("❌ Cannot open ImDisk device\n");
        goto cleanup;
    }

    sprintf(debug_info, "✅ Device opened with access mode %d\n", n_access);
    OutputDebugStringA(debug_info);

    // 参考 ImDisk-Dlg.c 第455-460行: 查询设备信息

    OutputDebugStringA("Step 2: Querying device info...\n");
    if (!DeviceIoControl(h, IOCTL_IMDISK_QUERY_DEVICE, NULL, 0, &create_data, sizeof(create_data), &dw, NULL)) {
        result = VDL_ERROR_UNMOUNT_FAILED;
        _snprintf_s(response.error_message, sizeof(response.error_message), _TRUNCATE,
                   "%s is not an ImDisk virtual disk", request.drive);
        OutputDebugStringA("❌ Device is not an ImDisk virtual disk\n");
        CloseHandle(h);
        goto cleanup;
    }

    OutputDebugStringA("✅ Device info queried successfully\n");

    // 参考 ImDisk-Dlg.c 第469-472行: 发送设备移除通知
    if (mount_point[1] == L':' && !mount_point[2]) {
        dbv.dbcv_unitmask = 1 << (mount_point[0] - L'A');

        OutputDebugStringA("Step 3: Sending device remove notification...\n");
        SendMessageTimeout(HWND_BROADCAST, WM_DEVICECHANGE, DBT_DEVICEREMOVEPENDING,
                          (LPARAM)&dbv, SMTO_BLOCK | SMTO_ABORTIFHUNG, 4000, &dwp);
        OutputDebugStringA("✅ Device remove notification sent\n");
    }

    // 参考 ImDisk-Dlg.c 第474-475行: 刷新文件缓冲区
    OutputDebugStringA("Step 4: Flushing file buffers...\n");
    FlushFileBuffers(h);
    OutputDebugStringA("✅ File buffers flushed\n");

    // 参考 ImDisk-Dlg.c 第477-481行: 锁定卷
    OutputDebugStringA("Step 5: Locking volume...\n");
    if (!DeviceIoControl(h, FSCTL_LOCK_VOLUME, NULL, 0, NULL, 0, &dw, NULL)) {
        OutputDebugStringA("⚠️ Warning: Cannot lock volume, continuing...\n");
    } else {
        OutputDebugStringA("✅ Volume locked\n");
    }

    // 参考 ImDisk-Dlg.c 第483-484行: 卸载卷
    OutputDebugStringA("Step 6: Dismounting volume...\n");
    DeviceIoControl(h, FSCTL_DISMOUNT_VOLUME, NULL, 0, NULL, 0, &dw, NULL);
    OutputDebugStringA("✅ Volume dismounted\n");

    // 再次锁定卷
    DeviceIoControl(h, FSCTL_LOCK_VOLUME, NULL, 0, NULL, 0, &dw, NULL);

    // 参考 ImDisk-Dlg.c 第497-501行: 弹出媒体
    OutputDebugStringA("Step 7: Ejecting media...\n");
    if (!DeviceIoControl(h, IOCTL_STORAGE_EJECT_MEDIA, NULL, 0, NULL, 0, &dw, NULL)) {
        // 尝试强制移除设备 (重用已加载的 h_cpl)
        typedef BOOL (WINAPI *ImDiskForceRemoveDeviceProc)(HANDLE, DWORD);
        ImDiskForceRemoveDeviceProc ForceRemove = (ImDiskForceRemoveDeviceProc)GetProcAddress(h_cpl, "ImDiskForceRemoveDevice");
        if (ForceRemove && !ForceRemove(h, 0)) {
            result = VDL_ERROR_UNMOUNT_FAILED;
            strcpy_s(response.error_message, sizeof(response.error_message), "Cannot remove device");
            OutputDebugStringA("❌ Cannot remove device\n");
            CloseHandle(h);
            goto cleanup;
        }
    }

    CloseHandle(h);
    OutputDebugStringA("✅ Media ejected\n");

    // 参考 ImDisk-Dlg.c 第504-508行: 移除挂载点
    OutputDebugStringA("Step 8: Removing mount point...\n");
    // 重用已加载的 h_cpl
    typedef BOOL (WINAPI *ImDiskRemoveMountPointProc)(LPCWSTR);
    ImDiskRemoveMountPointProc RemoveMountPoint = (ImDiskRemoveMountPointProc)GetProcAddress(h_cpl, "ImDiskRemoveMountPoint");
    if (RemoveMountPoint && !RemoveMountPoint(mount_point)) {
        result = VDL_ERROR_UNMOUNT_FAILED;
        strcpy_s(response.error_message, sizeof(response.error_message), "Cannot remove mount point");
        OutputDebugStringA("❌ Cannot remove mount point\n");
        goto cleanup;
    }

    OutputDebugStringA("✅ Mount point removed\n");

    // 卸载成功
    response.success = 1;
    response.error_code = 0;
    strcpy_s(response.drive_letter, sizeof(response.drive_letter), request.drive);
    strcpy_s(response.message, sizeof(response.message), "Unmount successful");
    OutputDebugStringA("✅ Unmount operation completed successfully\n");

cleanup:
    // 释放 imdisk.cpl 库
    if (h_cpl) {
        FreeLibrary(h_cpl);
        OutputDebugStringA("✅ imdisk.cpl library released\n");
    }

    response.error_code = result;

    // 生成响应JSON
    int jsonResult = GenerateMountResponse(&response, jsonOutput, bufferSize);
    if (jsonResult != 0) {
        result = VDL_ERROR_BUFFER_TOO_SMALL;
    }

    LeaveCriticalSection(&g_cs);
    return result;
#endif
}

/*
 * 获取挂载状态
 */
VIRTUALDISKLIB_API int GetMountStatus(const char* jsonInput, char* jsonOutput, int bufferSize)
{
    // 参数验证
    if (!jsonInput || !jsonOutput || bufferSize <= 0) {
        return VDL_ERROR_INVALID_PARAMETER;
    }
    
    // 清空输出缓冲区
    memset(jsonOutput, 0, bufferSize);
    
    //// 检查初始化状态
    //if (!g_bInitialized) {
    //    return GenerateErrorResponse(VDL_ERROR_MOUNT_FAILED, 
    //        "Library not initialized", jsonOutput, bufferSize);
    //}
    
    EnterCriticalSection(&g_cs);
    
    MountRequest request;
    MountResponse response;
    int result = VDL_SUCCESS;
    
    // 初始化结构
    memset(&request, 0, sizeof(request));
    memset(&response, 0, sizeof(response));
    
    // 解析JSON请求
    if (ParseMountRequest(jsonInput, &request) != 0) {
        result = VDL_ERROR_INVALID_JSON;
        strcpy_s(response.error_message, sizeof(response.error_message), 
                "Invalid JSON format");
        goto cleanup;
    }
    
    // 验证驱动器号
    if (strlen(request.drive) == 0) {
        result = VDL_ERROR_INVALID_PARAMETER;
        strcpy_s(response.error_message, sizeof(response.error_message), 
                "Drive letter is required");
        goto cleanup;
    }
    
    // 简化的挂载状态检查 (不依赖复杂的MountInfo结构)
    OutputDebugStringA("=== Checking Mount Status ===\n");
    char debug_info[256];
    sprintf(debug_info, "Checking drive: %s\n", request.drive);
    OutputDebugStringA(debug_info);

    WCHAR wDrive[8];
    MultiByteToWideChar(CP_UTF8, 0, request.drive, -1, wDrive, sizeof(wDrive)/sizeof(WCHAR));
    UINT driveType = GetDriveTypeW(wDrive);

    sprintf(debug_info, "Drive type: %d\n", driveType);
    OutputDebugStringA(debug_info);

    response.success = 1;
    response.error_code = 0;
    strcpy_s(response.drive_letter, sizeof(response.drive_letter), request.drive);

    if (driveType != DRIVE_NO_ROOT_DIR && driveType != DRIVE_UNKNOWN) {
        // 驱动器存在，认为已挂载
        response.is_mounted = 1;

        // 简化的信息填充 (不依赖MountInfo)
        strcpy_s(response.file_system, sizeof(response.file_system), "Unknown");
        strcpy_s(response.image_path, sizeof(response.image_path), "Unknown");
        response.readonly = 0;  // 默认为可写

        // 使用当前时间作为挂载时间
        SYSTEMTIME st;
        GetLocalTime(&st);
        sprintf(response.mount_time, "%04d-%02d-%02d %02d:%02d:%02d",
                st.wYear, st.wMonth, st.wDay, st.wHour, st.wMinute, st.wSecond);

        strcpy_s(response.message, sizeof(response.message), "Drive is mounted");
        OutputDebugStringA("✅ Drive is mounted\n");
    } else {
        // 驱动器不存在，认为未挂载
        response.is_mounted = 0;
        strcpy_s(response.message, sizeof(response.message), "Drive is not mounted");
        OutputDebugStringA("❌ Drive is not mounted\n");
    }
    
cleanup:
    response.error_code = result;
    
    // 生成响应JSON
    int jsonResult = GenerateMountResponse(&response, jsonOutput, bufferSize);
    if (jsonResult != 0) {
        result = VDL_ERROR_BUFFER_TOO_SMALL;
    }
    
    LeaveCriticalSection(&g_cs);
    return result;
}

/*
 * 获取错误描述
 */
VIRTUALDISKLIB_API const char* GetErrorDescription(int errorCode)
{
    switch (errorCode) {
        case VDL_SUCCESS:
            return "Operation successful";
        case VDL_ERROR_INVALID_JSON:
            return "Invalid JSON format";
        case VDL_ERROR_BUFFER_TOO_SMALL:
            return "Output buffer too small";
        case VDL_ERROR_FILE_NOT_FOUND:
            return "Image file not found";
        case VDL_ERROR_MOUNT_FAILED:
            return "Mount operation failed";
        case VDL_ERROR_UNMOUNT_FAILED:
            return "Unmount operation failed";
        case VDL_ERROR_INVALID_DRIVE:
            return "Invalid drive letter";
        case VDL_ERROR_UNSUPPORTED_FORMAT:
            return "Unsupported file format";
        case VDL_ERROR_ACCESS_DENIED:
            return "Access denied";
        case VDL_ERROR_DRIVE_IN_USE:
            return "Drive letter already in use";
        case VDL_ERROR_INVALID_PARAMETER:
            return "Invalid parameter";
        default:
            return "Unknown error";
    }
}

/*
 * 获取库版本信息
 */
VIRTUALDISKLIB_API int GetLibraryInfo(char* jsonOutput, int bufferSize)
{
    if (!jsonOutput || bufferSize <= 0) {
        return VDL_ERROR_INVALID_PARAMETER;
    }

    // 清空输出缓冲区
    memset(jsonOutput, 0, bufferSize);

    // 生成版本信息JSON
    int result = SafeJsonPrintf(jsonOutput, bufferSize,
        "{"
        "\"version\":\"1.0.0\","
        "\"build_date\":\"2025-01-11\","
        "\"supported_formats\":[\"VMDK\",\"VHDX\",\"VHD\",\"ISO\",\"IMG\"],"
        "\"min_windows_version\":\"Windows XP SP3\","
#ifdef _WIN64
        "\"architecture\":\"x64\","
#else
        "\"architecture\":\"x86\","
#endif
        "\"description\":\"Virtual Disk Mount Library\","
        "\"author\":\"VirtualDiskLib Team\""
        "}");

    return result;
}

/*
 * 初始化VirtualDiskLib
 */
VIRTUALDISKLIB_API int InitializeVirtualDiskLib(void)
{
    OutputDebugStringA("=== Initializing VirtualDiskLib ===\n");

    if (g_bInitialized) {
        OutputDebugStringA("Library already initialized\n");
        return VDL_SUCCESS;
    }

    // 检查MountImg32.exe是否存在
    char dllDir[MAX_PATH];
    char exePath[MAX_PATH];

    if (!GetCurrentDllDirectory(dllDir, sizeof(dllDir))) {
        OutputDebugStringA("ERROR: Failed to get DLL directory\n");
        return VDL_ERROR_INTERNAL;
    }

    sprintf_s(exePath, sizeof(exePath), "%s\\MountImg32.exe", dllDir);

    if (GetFileAttributesA(exePath) == INVALID_FILE_ATTRIBUTES) {
        OutputDebugStringA("ERROR: MountImg32.exe not found\n");
        char debug_info[512];
        sprintf(debug_info, "Expected path: %s\n", exePath);
        OutputDebugStringA(debug_info);
        return VDL_ERROR_FILE_NOT_FOUND;
    }

    // 标记为已初始化
    g_bInitialized = TRUE;

    OutputDebugStringA("✅ VirtualDiskLib initialized successfully\n");
    return VDL_SUCCESS;
}

/*
 * 清理VirtualDiskLib
 */
VIRTUALDISKLIB_API void CleanupVirtualDiskLib(void)
{
    OutputDebugStringA("=== Cleaning up VirtualDiskLib ===\n");

    if (!g_bInitialized) {
        OutputDebugStringA("Library not initialized, nothing to cleanup\n");
        return;
    }

    // 标记为未初始化
    g_bInitialized = FALSE;

    OutputDebugStringA("✅ VirtualDiskLib cleanup completed\n");
}
