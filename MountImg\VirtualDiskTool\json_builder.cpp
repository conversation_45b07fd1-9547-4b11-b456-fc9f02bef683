﻿/*
 * json_builder.cpp
 * JSON请求构建和响应解析器实现
 */

#define _CRT_SECURE_NO_WARNINGS
#include <stdio.h>
#include <stdlib.h>
#include <string.h>

#include "json_builder.h"

/*
 * 转义JSON字符串
 */
int EscapeJsonString(const char* input, char* output, int outputSize)
{
    if (!input || !output || outputSize <= 0) return -1;
    
    int inputLen = (int)strlen(input);
    int outputPos = 0;
    
    for (int i = 0; i < inputLen && outputPos < outputSize - 1; i++) {
        char c = input[i];
        
        switch (c) {
            case '"':
                if (outputPos < outputSize - 2) {
                    output[outputPos++] = '\\';
                    output[outputPos++] = '"';
                }
                break;
            case '\\':
                if (outputPos < outputSize - 2) {
                    output[outputPos++] = '\\';
                    output[outputPos++] = '\\';
                }
                break;
            case '\n':
                if (outputPos < outputSize - 2) {
                    output[outputPos++] = '\\';
                    output[outputPos++] = 'n';
                }
                break;
            case '\r':
                if (outputPos < outputSize - 2) {
                    output[outputPos++] = '\\';
                    output[outputPos++] = 'r';
                }
                break;
            case '\t':
                if (outputPos < outputSize - 2) {
                    output[outputPos++] = '\\';
                    output[outputPos++] = 't';
                }
                break;
            default:
                output[outputPos++] = c;
                break;
        }
    }
    
    output[outputPos] = '\0';
    return 0;
}

/*
 * 构建挂载请求JSON
 */
int BuildMountRequest(const CommandLineArgs* args, char* jsonOutput, int bufferSize)
{
    if (!args || !jsonOutput || bufferSize <= 0) return -1;
    
    char escapedPath[1024];
    char escapedDrive[16];
    
    // 转义字符串
    EscapeJsonString(args->file_path, escapedPath, sizeof(escapedPath));
    EscapeJsonString(args->drive_letter, escapedDrive, sizeof(escapedDrive));
    
    // 构建JSON
    int result = _snprintf_s(jsonOutput, bufferSize, _TRUNCATE,
        "{"
        "\"file_path\":\"%s\","
        "\"drive\":\"%s\","
        "\"readonly\":%s,"
        "\"partition\":%d,"
        "\"auto_assign\":%s"
        "}",
        escapedPath,
        escapedDrive,
        args->readonly ? "true" : "false",
        args->partition,
        args->auto_assign ? "true" : "false");
    
    return (result > 0 && result < bufferSize) ? 0 : -1;
}

/*
 * 构建卸载请求JSON
 */
int BuildUnmountRequest(const CommandLineArgs* args, char* jsonOutput, int bufferSize)
{
    if (!args || !jsonOutput || bufferSize <= 0) return -1;
    
    char escapedDrive[16];
    
    // 转义字符串
    EscapeJsonString(args->drive_letter, escapedDrive, sizeof(escapedDrive));
    
    // 构建JSON
    int result = _snprintf_s(jsonOutput, bufferSize, _TRUNCATE,
        "{"
        "\"drive\":\"%s\","
        "\"force\":%s"
        "}",
        escapedDrive,
        args->force ? "true" : "false");
    
    return (result > 0 && result < bufferSize) ? 0 : -1;
}

/*
 * 构建状态查询请求JSON
 */
int BuildStatusRequest(const CommandLineArgs* args, char* jsonOutput, int bufferSize)
{
    if (!args || !jsonOutput || bufferSize <= 0) return -1;
    
    char escapedDrive[16];
    
    // 转义字符串
    EscapeJsonString(args->drive_letter, escapedDrive, sizeof(escapedDrive));
    
    // 构建JSON
    int result = _snprintf_s(jsonOutput, bufferSize, _TRUNCATE,
        "{"
        "\"drive\":\"%s\""
        "}",
        escapedDrive);
    
    return (result > 0 && result < bufferSize) ? 0 : -1;
}

/*
 * 从JSON中提取字符串值
 */
int ExtractJsonString(const char* json, const char* key, char* output, int outputSize)
{
    if (!json || !key || !output || outputSize <= 0) return -1;
    
    // 构建搜索模式
    char pattern[128];
    _snprintf_s(pattern, sizeof(pattern), _TRUNCATE, "\"%s\":\"", key);
    
    // 查找键
    const char* start = strstr(json, pattern);
    if (!start) return -1;
    
    start += strlen(pattern);
    
    // 查找值的结束位置
    const char* end = start;
    while (*end && *end != '"') {
        if (*end == '\\' && *(end + 1)) {
            end += 2; // 跳过转义字符
        } else {
            end++;
        }
    }
    
    if (!*end) return -1;
    
    // 复制值
    int len = (int)(end - start);
    if (len >= outputSize) len = outputSize - 1;
    
    strncpy(output, start, len);
    output[len] = '\0';
    
    return 0;
}

/*
 * 从JSON中提取布尔值
 */
int ExtractJsonBool(const char* json, const char* key, int defaultValue)
{
    char value[16];
    if (ExtractJsonString(json, key, value, sizeof(value)) != 0) {
        return defaultValue;
    }
    
    if (strcmp(value, "true") == 0) return 1;
    if (strcmp(value, "false") == 0) return 0;
    
    return defaultValue;
}

/*
 * 从JSON中提取整数值
 */
int ExtractJsonInt(const char* json, const char* key, int defaultValue)
{
    char value[32];
    if (ExtractJsonString(json, key, value, sizeof(value)) != 0) {
        return defaultValue;
    }
    
    return atoi(value);
}

/*
 * 打印挂载响应
 */
void PrintMountResponse(const char* jsonResponse)
{
    if (!jsonResponse) return;
    
    char drive[8], fs[32], path[512], message[256];
    int success, readonly, size_mb;
    
    success = ExtractJsonBool(jsonResponse, "success", 0);
    ExtractJsonString(jsonResponse, "drive_letter", drive, sizeof(drive));
    ExtractJsonString(jsonResponse, "file_system", fs, sizeof(fs));
    ExtractJsonString(jsonResponse, "image_path", path, sizeof(path));
    ExtractJsonString(jsonResponse, "message", message, sizeof(message));
    readonly = ExtractJsonBool(jsonResponse, "readonly", 0);
    size_mb = ExtractJsonInt(jsonResponse, "size_mb", 0);
    
    if (success) {
        printf("✅ Mount Successful\n");
        printf("   Drive Letter: %s\n", drive);
        printf("   Image Path: %s\n", path);
        printf("   File System: %s\n", fs);
        printf("   Size: %d MB\n", size_mb);
        printf("   Read-Only: %s\n", readonly ? "Yes" : "No");
        printf("   Message: %s\n", message);
    } else {
        char error[256];
        int error_code = ExtractJsonInt(jsonResponse, "error_code", 0);
        ExtractJsonString(jsonResponse, "error_message", error, sizeof(error));
        printf("❌ Mount Failed\n");
        printf("   Error Code: %d\n", error_code);
        printf("   Error Message: %s\n", error);
    }
}

/*
 * 打印卸载响应
 */
void PrintUnmountResponse(const char* jsonResponse)
{
    if (!jsonResponse) return;
    
    char drive[8], message[256];
    int success;
    
    success = ExtractJsonBool(jsonResponse, "success", 0);
    ExtractJsonString(jsonResponse, "drive_letter", drive, sizeof(drive));
    ExtractJsonString(jsonResponse, "message", message, sizeof(message));
    
    if (success) {
        printf("✅ Unmount Successful\n");
        printf("   Drive Letter: %s\n", drive);
        printf("   Message: %s\n", message);
    } else {
        char error[256];
        int error_code = ExtractJsonInt(jsonResponse, "error_code", 0);
        ExtractJsonString(jsonResponse, "error_message", error, sizeof(error));
        printf("❌ Unmount Failed\n");
        printf("   Error Code: %d\n", error_code);
        printf("   Error Message: %s\n", error);
    }
}

/*
 * 打印状态响应
 */
void PrintStatusResponse(const char* jsonResponse)
{
    if (!jsonResponse) return;
    
    char drive[8], fs[32], path[512], mount_time[32];
    int success, is_mounted, readonly, size_mb;
    
    success = ExtractJsonBool(jsonResponse, "success", 0);
    is_mounted = ExtractJsonBool(jsonResponse, "is_mounted", 0);
    ExtractJsonString(jsonResponse, "drive_letter", drive, sizeof(drive));
    ExtractJsonString(jsonResponse, "file_system", fs, sizeof(fs));
    ExtractJsonString(jsonResponse, "image_path", path, sizeof(path));
    ExtractJsonString(jsonResponse, "mount_time", mount_time, sizeof(mount_time));
    readonly = ExtractJsonBool(jsonResponse, "readonly", 0);
    size_mb = ExtractJsonInt(jsonResponse, "size_mb", 0);
    
    if (success) {
        printf("📊 Mount Status\n");
        printf("   Drive Letter: %s\n", drive);
        printf("   Status: %s\n", is_mounted ? "Mounted" : "Not Mounted");
        
        if (is_mounted) {
            printf("   Image Path: %s\n", path);
            printf("   File System: %s\n", fs);
            printf("   Size: %d MB\n", size_mb);
            printf("   Read-Only: %s\n", readonly ? "Yes" : "No");
            printf("   Mount Time: %s\n", mount_time);
        }
    } else {
        char error[256];
        int error_code = ExtractJsonInt(jsonResponse, "error_code", 0);
        ExtractJsonString(jsonResponse, "error_message", error, sizeof(error));
        printf("❌ Status Query Failed\n");
        printf("   Error Code: %d\n", error_code);
        printf("   Error Message: %s\n", error);
    }
}

/*
 * 打印错误响应
 */
void PrintErrorResponse(const char* jsonResponse)
{
    if (!jsonResponse) return;
    
    char error[256];
    int error_code = ExtractJsonInt(jsonResponse, "error_code", 0);
    ExtractJsonString(jsonResponse, "error_message", error, sizeof(error));
    
    printf("❌ Operation Failed\n");
    printf("   Error Code: %d\n", error_code);
    printf("   Error Message: %s\n", error);
}
