﻿/*
 * json_helper.h
 * 轻量级JSON处理辅助函数
 * 
 * 功能：提供简单的JSON解析和生成功能
 * 特点：无外部依赖，专为VirtualDiskLib设计
 */

#ifndef JSON_HELPER_H
#define JSON_HELPER_H

#ifdef __cplusplus
extern "C" {
#endif

// JSON节点结构
typedef struct JsonNode {
    char* key;                  // 键名
    char* value;                // 值
    struct JsonNode* next;      // 下一个节点
} JsonNode;

// 挂载请求结构
typedef struct {
    char file_path[512];        // 镜像文件路径
    char drive[8];              // 驱动器号
    char mount_point[512];      // 挂载点路径
    int readonly;               // 是否只读
    int partition;              // 分区号
    int auto_assign;            // 是否自动分配驱动器
    int force;                  // 是否强制操作
} MountRequest;

// 挂载响应结构
typedef struct {
    int success;                // 操作是否成功
    int error_code;             // 错误代码
    char error_message[256];    // 错误消息
    char drive_letter[8];       // 驱动器号
    char file_system[32];       // 文件系统类型
    char image_path[512];       // 镜像文件路径
    long long size_bytes;       // 磁盘大小（字节）
    int size_mb;                // 磁盘大小（MB）
    int readonly;               // 是否只读
    int is_mounted;             // 是否已挂载
    char mount_time[32];        // 挂载时间
    char message[256];          // 操作消息
} MountResponse;

/*
 * 解析JSON字符串中的键值对
 * 
 * 参数：
 *   json: JSON字符串
 * 
 * 返回值：
 *   JsonNode链表头指针，失败返回NULL
 */
JsonNode* ParseSimpleJson(const char* json);

/*
 * 释放JSON节点链表
 * 
 * 参数：
 *   nodes: JSON节点链表头指针
 */
void FreeJsonNodes(JsonNode* nodes);

/*
 * 从JSON节点中获取字符串值
 * 
 * 参数：
 *   nodes: JSON节点链表
 *   key: 键名
 *   defaultValue: 默认值
 * 
 * 返回值：
 *   找到的值或默认值
 */
const char* GetJsonString(JsonNode* nodes, const char* key, const char* defaultValue);

/*
 * 从JSON节点中获取整数值
 * 
 * 参数：
 *   nodes: JSON节点链表
 *   key: 键名
 *   defaultValue: 默认值
 * 
 * 返回值：
 *   找到的值或默认值
 */
int GetJsonInt(JsonNode* nodes, const char* key, int defaultValue);

/*
 * 从JSON节点中获取布尔值
 * 
 * 参数：
 *   nodes: JSON节点链表
 *   key: 键名
 *   defaultValue: 默认值
 * 
 * 返回值：
 *   找到的值或默认值
 */
int GetJsonBool(JsonNode* nodes, const char* key, int defaultValue);

/*
 * 解析挂载请求JSON
 * 
 * 参数：
 *   json: JSON字符串
 *   request: 输出的请求结构
 * 
 * 返回值：
 *   0: 成功，非0: 失败
 */
int ParseMountRequest(const char* json, MountRequest* request);

/*
 * 生成挂载响应JSON
 * 
 * 参数：
 *   response: 响应结构
 *   jsonOutput: 输出缓冲区
 *   bufferSize: 缓冲区大小
 * 
 * 返回值：
 *   0: 成功，非0: 失败
 */
int GenerateMountResponse(const MountResponse* response, char* jsonOutput, int bufferSize);

/*
 * 生成错误响应JSON
 * 
 * 参数：
 *   errorCode: 错误代码
 *   errorMessage: 错误消息
 *   jsonOutput: 输出缓冲区
 *   bufferSize: 缓冲区大小
 * 
 * 返回值：
 *   0: 成功，非0: 失败
 */
int GenerateErrorResponse(int errorCode, const char* errorMessage, char* jsonOutput, int bufferSize);

/*
 * 安全的JSON字符串格式化
 * 
 * 参数：
 *   buffer: 输出缓冲区
 *   bufferSize: 缓冲区大小
 *   format: 格式字符串
 *   ...: 可变参数
 * 
 * 返回值：
 *   0: 成功，非0: 失败
 */
int SafeJsonPrintf(char* buffer, int bufferSize, const char* format, ...);

/*
 * 转义JSON字符串中的特殊字符
 * 
 * 参数：
 *   input: 输入字符串
 *   output: 输出缓冲区
 *   outputSize: 输出缓冲区大小
 * 
 * 返回值：
 *   0: 成功，非0: 失败
 */
int EscapeJsonString(const char* input, char* output, int outputSize);

#ifdef __cplusplus
}
#endif

#endif // JSON_HELPER_H
