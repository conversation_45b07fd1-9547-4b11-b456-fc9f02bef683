@echo off
echo Drive Check Fix Test
echo ===================

echo.
echo 1. Checking current ImDisk devices...
imdisk -l
echo ImDisk list result: %errorlevel%

echo.
echo 2. Checking drive availability...
echo Testing X: drive...
if exist "X:\" (
    echo X: drive exists
    dir X:\ /b | findstr /c:"." >nul
    if %errorlevel% equ 0 (
        echo X: drive has content
    ) else (
        echo X: drive is empty or not accessible
    )
) else (
    echo X: drive does not exist - AVAILABLE
)

echo.
echo Testing Y: drive...
if exist "Y:\" (
    echo Y: drive exists
) else (
    echo Y: drive does not exist - AVAILABLE
)

echo.
echo Testing Z: drive...
if exist "Z:\" (
    echo Z: drive exists
) else (
    echo Z: drive does not exist - AVAILABLE
)

echo.
echo 3. Manual mount test with available drive...
echo Trying to mount VHD file to first available drive...

for %%d in (X Y Z W V) do (
    echo Testing drive %%d:...
    if not exist "%%d:\" (
        echo Drive %%d: appears available, attempting mount...
        imdisk -a -t file -f "E:\002_VHD\vhd.vhd" -m "%%d:"
        set MOUNT_RESULT=!errorlevel!
        echo Mount result: !MOUNT_RESULT!
        if !MOUNT_RESULT! equ 0 (
            echo SUCCESS: Mounted to %%d:
            echo Checking mounted drive...
            if exist "%%d:\" (
                echo Drive %%d: is now accessible
                dir "%%d:\" /b
                echo.
                echo Unmounting...
                imdisk -d -m "%%d:"
                echo Unmount result: !errorlevel!
            )
            goto :done
        ) else (
            echo FAILED: Mount to %%d: failed
        )
    ) else (
        echo Drive %%d: already exists, skipping
    )
)

:done
echo.
echo Test completed.
pause
