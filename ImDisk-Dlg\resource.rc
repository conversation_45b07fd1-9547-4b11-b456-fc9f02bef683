#include <windows.h>
#include <afxres.h>
#include "resource.h"

// 1 RT_MANIFEST "UAC.manifest"  // 注释掉以避免与Visual Studio自动生成的manifest冲突

1 ICON "..\\VD.ico"

STATUS_DLG DIALOGEX 0, 0, 170, 25
		   FONT 9, "MS Shell Dlg"
		   STYLE DS_SHELLFONT | WS_VISIBLE
		   EXSTYLE WS_EX_TOOLWINDOW | WS_EX_TOPMOST | WS_EX_NOACTIVATE
		   CAPTION "ImDisk"
BEGIN
	LTEXT "", ID_TEXT1, 5, 4, 162, 21
END

WARN_DLG DIALOGEX 0, 0, 260, 110
		   STYLE DS_SHELLFONT | DS_CENTER
		   EXSTYLE WS_EX_TOPMOST
		   FONT 9, "MS Shell Dlg"
		   CAPTION "ImDisk"
BEGIN
	LTEXT "", ID_TEXT1, 44, 13, 210, 67
	AUTOCHECKBOX "Do not show this warning again", ID_CHECK1, 8, 92, 152, 10
	PUSHB<PERSON>TON "OK", IDOK, 166, 91, 43, 13
	DEFPUSHBUTTON "Cancel", <PERSON><PERSON><PERSON><PERSON>, 213, 91, 43, 13
	GROUPBOX "", IDC_STATIC, -5, 80, 270, 40
END

IMGMOD_DLG DIALOGEX 0, 0, 260, 86
		   STYLE DS_SHELLFONT | DS_CENTER
		   EXSTYLE WS_EX_TOPMOST
		   FONT 9, "MS Shell Dlg"
		   CAPTION "ImDisk"
BEGIN
	LTEXT "", ID_TEXT1, 44, 13, 210, 43
	AUTOCHECKBOX "Do not ask again", ID_CHECK1, 8, 68, 105, 10
	DEFPUSHBUTTON "Yes", IDYES, 119, 67, 43, 13
	PUSHBUTTON "No", IDNO, 166, 67, 43, 13
	PUSHBUTTON "Cancel", IDCANCEL, 213, 67, 43, 13
	GROUPBOX "", IDC_STATIC, -5, 56, 270, 40
END

SAVE_DLG DIALOGEX 0, 0, 230, 166
		   STYLE WS_SYSMENU | DS_SHELLFONT | DS_CENTER
		   FONT 9, "MS Shell Dlg"
		   CAPTION "ImDisk"
BEGIN
	GROUPBOX " Save Disk Image ", ID_TITLE, 6, 6, 218, 135
	LTEXT "Image File:", ID_TEXT1, 18, 22, 204, 10
	EDITTEXT ID_EDIT1, 12, 33, 190, 12, ES_AUTOHSCROLL
	PUSHBUTTON "...", ID_PBUTTON1, 204, 33, 16, 12
	LTEXT "Offset in Image File:", ID_TEXT2, 18, 55, 204, 10
	EDITTEXT ID_EDIT2, 18, 66, 60, 12, ES_NUMBER
	AUTOCHECKBOX "Write a MBR at the beginning of the file", ID_CHECK1, 18, 89, 200, 10
	CONTROL "", IDC_STATIC, "Static", SS_SUNKEN, 12, 113, 206, 22
	LTEXT "", ID_TEXT3, 13, 114, 204, 20
	DEFPUSHBUTTON "OK", IDOK, 136, 147, 43, 13
	PUSHBUTTON "Cancel", IDCANCEL, 183, 147, 43, 13
END


1 VERSIONINFO
FILEVERSION 1,1,0,0
BEGIN
BLOCK "StringFileInfo"
	BEGIN
	BLOCK "040904b0"
		BEGIN
			VALUE "FileDescription", "ImDisk"
			VALUE "ProductName", "imdisk"
			VALUE "ProductVersion", "*******"
		END
	END
BLOCK "VarFileInfo"
	BEGIN
		VALUE "Translation", 0x0409, 0x04B0
	END
END
