# PowerShell script to download and install WinRAR
# 自动下载并安装WinRAR以支持CAB文件创建

Write-Host "WinRAR自动安装脚本" -ForegroundColor Green
Write-Host "==================" -ForegroundColor Green

# 检查是否已经安装了WinRAR
$winrarPaths = @(
    "C:\Program Files\WinRAR\WinRAR.exe",
    "C:\Program Files (x86)\WinRAR\WinRAR.exe"
)

$winrarInstalled = $false
$winrarPath = ""

foreach ($path in $winrarPaths) {
    if (Test-Path $path) {
        $winrarInstalled = $true
        $winrarPath = $path
        break
    }
}

if ($winrarInstalled) {
    Write-Host "WinRAR已经安装在: $winrarPath" -ForegroundColor Green
    
    # 测试CAB创建功能
    Write-Host "测试CAB创建功能..." -ForegroundColor Yellow
    
    $testResult = & $winrarPath "a" "-afcab" "test_winrar.cab" "files\config.exe" 2>&1
    
    if (Test-Path "test_winrar.cab") {
        Write-Host "SUCCESS: WinRAR可以创建CAB文件!" -ForegroundColor Green
        Remove-Item "test_winrar.cab" -Force
        
        Write-Host "`n现在可以使用WinRAR创建完整的CAB文件:" -ForegroundColor Cyan
        Write-Host "命令: `"$winrarPath`" a -afcab -r files_complete.cab files\*" -ForegroundColor Yellow
        
        exit 0
    } else {
        Write-Host "WARNING: WinRAR已安装但CAB创建测试失败" -ForegroundColor Yellow
    }
} else {
    Write-Host "WinRAR未安装，开始下载安装..." -ForegroundColor Yellow
}

# 设置下载参数
$winrarUrl = "https://www.rarlab.com/rar/winrar-x64-712.exe"
$downloadPath = "$env:TEMP\winrar-x64-712.exe"

Write-Host "下载WinRAR 7.12 (64位)..." -ForegroundColor Yellow
Write-Host "下载地址: $winrarUrl" -ForegroundColor Cyan

try {
    # 使用PowerShell下载文件
    $progressPreference = 'SilentlyContinue'
    Invoke-WebRequest -Uri $winrarUrl -OutFile $downloadPath -UseBasicParsing
    
    if (Test-Path $downloadPath) {
        $fileSize = (Get-Item $downloadPath).Length
        Write-Host "下载完成! 文件大小: $([math]::Round($fileSize/1KB, 2)) KB" -ForegroundColor Green
        
        # 检查文件是否完整
        if ($fileSize -gt 3000000) {  # 大于3MB
            Write-Host "开始安装WinRAR..." -ForegroundColor Yellow
            
            # 静默安装WinRAR
            $installArgs = "/S"  # 静默安装参数
            
            Write-Host "执行安装命令: $downloadPath $installArgs" -ForegroundColor Cyan
            $installProcess = Start-Process -FilePath $downloadPath -ArgumentList $installArgs -Wait -PassThru
            
            if ($installProcess.ExitCode -eq 0) {
                Write-Host "WinRAR安装成功!" -ForegroundColor Green
                
                # 等待安装完成
                Start-Sleep -Seconds 3
                
                # 再次检查安装
                foreach ($path in $winrarPaths) {
                    if (Test-Path $path) {
                        Write-Host "WinRAR安装位置: $path" -ForegroundColor Green
                        $winrarPath = $path
                        break
                    }
                }
                
                if ($winrarPath) {
                    # 测试CAB创建功能
                    Write-Host "测试CAB创建功能..." -ForegroundColor Yellow
                    
                    try {
                        $testResult = & $winrarPath "a" "-afcab" "test_install.cab" "files\config.exe" 2>&1
                        
                        if (Test-Path "test_install.cab") {
                            Write-Host "SUCCESS: WinRAR安装成功并可以创建CAB文件!" -ForegroundColor Green
                            Remove-Item "test_install.cab" -Force
                            
                            Write-Host "`n=== 安装完成 ===" -ForegroundColor Green
                            Write-Host "WinRAR路径: $winrarPath" -ForegroundColor Cyan
                            Write-Host "创建CAB命令: `"$winrarPath`" a -afcab -r files_complete.cab files\*" -ForegroundColor Yellow
                            
                        } else {
                            Write-Host "WARNING: WinRAR安装成功但CAB创建测试失败" -ForegroundColor Yellow
                            Write-Host "可能需要手动配置或重启系统" -ForegroundColor Yellow
                        }
                    } catch {
                        Write-Host "ERROR: 测试CAB创建时出错: $($_.Exception.Message)" -ForegroundColor Red
                    }
                } else {
                    Write-Host "ERROR: WinRAR安装后未找到可执行文件" -ForegroundColor Red
                }
                
            } else {
                Write-Host "ERROR: WinRAR安装失败，退出代码: $($installProcess.ExitCode)" -ForegroundColor Red
            }
            
        } else {
            Write-Host "ERROR: 下载的文件大小异常，可能下载不完整" -ForegroundColor Red
        }
        
    } else {
        Write-Host "ERROR: 文件下载失败" -ForegroundColor Red
    }
    
} catch {
    Write-Host "ERROR: 下载过程中出错: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "请手动下载安装WinRAR:" -ForegroundColor Yellow
    Write-Host "下载地址: https://www.rarlab.com/download.htm" -ForegroundColor Cyan
}

# 清理下载文件
if (Test-Path $downloadPath) {
    Remove-Item $downloadPath -Force -ErrorAction SilentlyContinue
    Write-Host "清理下载文件完成" -ForegroundColor Green
}

Write-Host "`n脚本执行完成" -ForegroundColor Green
