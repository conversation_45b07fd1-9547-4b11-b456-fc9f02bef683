@echo off
echo Building RamDyn.c...

rem Use MSBuild instead
set MSBUILD="C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\MSBuild\Current\Bin\MSBuild.exe"

rem Clean first
%MSBUILD% RamDyn.vcxproj /p:Configuration=Release /p:Platform=x64 /t:Clean

rem Build
%MSBUILD% RamDyn.vcxproj /p:Configuration=Release /p:Platform=x64

if %ERRORLEVEL% EQU 0 (
    echo Build successful!
    if exist "x64\Release\RamDyn.exe" (
        echo Generated RamDyn.exe in x64\Release\
    )
) else (
    echo Build failed with error code: %ERRORLEVEL%
)

pause
