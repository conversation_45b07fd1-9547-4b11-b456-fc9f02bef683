﻿/*
 * cmdline_parser.h
 * 命令行参数解析器
 */

#ifndef CMDLINE_PARSER_H
#define CMDLINE_PARSER_H

#ifdef __cplusplus
extern "C" {
#endif

// 命令行参数结构
typedef struct {
    char command[32];           // 命令 (mount/unmount/status)
    char file_path[512];        // 镜像文件路径
    char drive_letter[8];       // 驱动器号
    int readonly;               // 是否只读
    int partition;              // 分区号
    int auto_assign;            // 是否自动分配驱动器
    int force;                  // 是否强制操作
    int json_mode;              // 是否JSON模式
    char json_input[2048];      // JSON输入字符串

    // 测试选项
    int test_all;               // 运行所有测试
    int test_mount;             // 测试挂载功能
    int test_unmount;           // 测试卸载功能
    int test_status;            // 测试状态查询
    int test_error;             // 测试错误描述
    int test_info;              // 测试库信息
    int test_callbacks;         // 测试回调功能
    char test_file[512];        // 测试文件路径
} CommandLineArgs;

/*
 * 解析命令行参数
 * 
 * 参数：
 *   argc: 参数数量
 *   argv: 参数数组
 *   args: 输出的参数结构
 * 
 * 返回值：
 *   0: 成功，非0: 失败
 */
int ParseCommandLine(int argc, char* argv[], CommandLineArgs* args);

/*
 * 初始化参数结构
 * 
 * 参数：
 *   args: 要初始化的参数结构
 */
void InitCommandLineArgs(CommandLineArgs* args);

/*
 * 验证参数有效性
 * 
 * 参数：
 *   args: 要验证的参数结构
 * 
 * 返回值：
 *   0: 有效，非0: 无效
 */
int ValidateCommandLineArgs(const CommandLineArgs* args);

#ifdef __cplusplus
}
#endif

#endif // CMDLINE_PARSER_H
