@echo off
echo ========================================
echo MountImg Windows XP 兼容性验证脚本
echo ========================================
echo.

echo 1. 检查项目文件中的XP兼容性设置...
echo.

echo 检查平台工具集设置:
findstr /C:"v141_xp" MountImg.vcxproj >nul
if %errorlevel%==0 (
    echo [✓] 平台工具集已设置为 v141_xp
) else (
    echo [✗] 平台工具集未正确设置
)

echo.
echo 检查Windows SDK版本:
findstr /C:"WindowsTargetPlatformVersion>7.0" MountImg.vcxproj >nul
if %errorlevel%==0 (
    echo [✓] Windows SDK版本已设置为 7.0
) else (
    echo [✗] Windows SDK版本未正确设置
)

echo.
echo 检查Windows版本定义:
findstr /C:"_WIN32_WINNT=0x0501" MountImg.vcxproj >nul
if %errorlevel%==0 (
    echo [✓] Windows版本定义已设置为 XP (0x0501)
) else (
    echo [✗] Windows版本定义未正确设置
)

echo.
echo 检查源代码中的版本定义:
findstr /C:"_WIN32_WINNT 0x0501" MountImg.c >nul
if %errorlevel%==0 (
    echo [✓] 源代码中Windows版本已设置为 XP
) else (
    echo [✗] 源代码中Windows版本未正确设置
)

echo.
echo 2. 检查编译环境...
echo.

echo 检查Visual Studio工具集:
where cl.exe >nul 2>&1
if %errorlevel%==0 (
    echo [✓] Visual Studio编译器可用
    cl.exe 2>&1 | findstr /C:"Microsoft" | head -1
) else (
    echo [✗] Visual Studio编译器不可用
)

echo.
echo 3. 生成版本信息...
call generate_build_h.bat

echo.
echo ========================================
echo XP兼容性验证完成
echo ========================================
echo.
echo 如果所有检查都显示 [✓]，则项目已正确配置为XP兼容。
echo 现在可以编译项目并在Windows XP系统上运行。
echo.
pause
