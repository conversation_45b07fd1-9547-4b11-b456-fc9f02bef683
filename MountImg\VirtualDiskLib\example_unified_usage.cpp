/*
 * example_unified_usage.cpp
 * VirtualDiskLib统一操作接口使用示例
 * 
 * 功能：演示完整的虚拟磁盘操作流程
 * 编译：cl example_unified_usage.cpp /I. VirtualDiskLib32.lib /Fe:example_unified.exe
 */

#include <iostream>
#include <string>
#include <windows.h>
#include "VirtualDiskLib.h"

// ========================================
// 回调函数定义
// ========================================

void MyProgressCallback(const std::string& taskId, int progress) {
    std::cout << "📊 [" << taskId << "] 进度: " << progress << "%" << std::endl;
}

bool MyControlCallback(const std::string& taskId, int controlType) {
    // controlType: 1=取消, 2=暂停
    std::cout << "🎛️ [" << taskId << "] 控制请求: " << (controlType == 1 ? "取消" : "暂停") << std::endl;
    return false; // 不取消任何任务
}

// ========================================
// 辅助函数
// ========================================

bool IsOperationSuccessful(const std::string& result) {
    return result.find("\"status\":\"success\"") != std::string::npos;
}

void PrintResult(const std::string& operation, const std::string& result) {
    std::cout << "🔍 " << operation << " 结果: " << std::endl;
    std::cout << "   " << result << std::endl;
    
    if (IsOperationSuccessful(result)) {
        std::cout << "✅ " << operation << " 成功!" << std::endl;
    } else {
        std::cout << "❌ " << operation << " 失败!" << std::endl;
    }
    std::cout << std::endl;
}

// ========================================
// 完整操作流程示例
// ========================================

int main() {
    std::cout << "🚀 VirtualDiskLib 统一操作接口使用示例" << std::endl;
    std::cout << "========================================" << std::endl;
    
    std::string result;
    
    try {
        // 步骤1: 获取库信息
        std::cout << "📋 步骤1: 获取库信息" << std::endl;
        result = VirtualDiskOperation(R"({
            "action": "info"
        })", MyProgressCallback, "get_info", MyControlCallback);
        PrintResult("获取库信息", result);
        
        // 步骤2: 初始化库
        std::cout << "🔧 步骤2: 初始化库" << std::endl;
        result = VirtualDiskOperation(R"({
            "action": "initialize",
            "check_dependencies": true,
            "enable_debug": true
        })", MyProgressCallback, "initialize", MyControlCallback);
        PrintResult("初始化库", result);
        
        if (!IsOperationSuccessful(result)) {
            std::cout << "❌ 初始化失败，退出程序" << std::endl;
            return 1;
        }
        
        // 步骤3: 挂载虚拟磁盘
        std::cout << "💾 步骤3: 挂载虚拟磁盘" << std::endl;
        result = VirtualDiskOperation(R"({
            "action": "mount",
            "image_path": "C:\\test\\example.vmdk",
            "drive_letter": "Z:",
            "readonly": false,
            "partition": 1
        })", MyProgressCallback, "mount_disk", MyControlCallback);
        PrintResult("挂载虚拟磁盘", result);
        
        // 步骤4: 检查挂载状态
        std::cout << "📊 步骤4: 检查挂载状态" << std::endl;
        result = VirtualDiskOperation(R"({
            "action": "status",
            "drive": "Z:"
        })", MyProgressCallback, "check_status", MyControlCallback);
        PrintResult("检查挂载状态", result);
        
        // 步骤5: 查询所有挂载的驱动器
        std::cout << "🔍 步骤5: 查询所有挂载的驱动器" << std::endl;
        result = VirtualDiskOperation(R"({
            "action": "status",
            "drive": ""
        })", MyProgressCallback, "check_all_status", MyControlCallback);
        PrintResult("查询所有挂载状态", result);
        
        // 模拟一些操作时间
        std::cout << "⏳ 模拟使用磁盘..." << std::endl;
        Sleep(2000);
        
        // 步骤6: 卸载虚拟磁盘
        std::cout << "📤 步骤6: 卸载虚拟磁盘" << std::endl;
        result = VirtualDiskOperation(R"({
            "action": "unmount",
            "drive": "Z:",
            "force": false
        })", MyProgressCallback, "unmount_disk", MyControlCallback);
        PrintResult("卸载虚拟磁盘", result);
        
        // 步骤7: 验证卸载结果
        std::cout << "✅ 步骤7: 验证卸载结果" << std::endl;
        result = VirtualDiskOperation(R"({
            "action": "status",
            "drive": "Z:"
        })", MyProgressCallback, "verify_unmount", MyControlCallback);
        PrintResult("验证卸载结果", result);
        
        // 步骤8: 清理库资源
        std::cout << "🧹 步骤8: 清理库资源" << std::endl;
        result = VirtualDiskOperation(R"({
            "action": "cleanup",
            "force_cleanup": false
        })", MyProgressCallback, "cleanup", MyControlCallback);
        PrintResult("清理库资源", result);
        
        std::cout << "🎉 所有操作完成！" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "❌ 操作过程中发生异常: " << e.what() << std::endl;
        
        // 尝试清理资源
        std::cout << "🧹 尝试清理资源..." << std::endl;
        try {
            VirtualDiskOperation(R"({
                "action": "cleanup",
                "force_cleanup": true
            })", nullptr, "emergency_cleanup");
        } catch (...) {
            std::cerr << "❌ 清理资源也失败了" << std::endl;
        }
        
        return 1;
    } catch (...) {
        std::cerr << "❌ 发生未知异常" << std::endl;
        return 1;
    }
    
    return 0;
}

/*
 * 使用说明：
 * 
 * 1. 编译程序：
 *    cl example_unified_usage.cpp /I. VirtualDiskLib32.lib /Fe:example_unified.exe
 * 
 * 2. 准备测试文件：
 *    - 创建一个虚拟磁盘文件 C:\test\example.vmdk
 *    - 或者修改代码中的image_path为实际存在的文件
 * 
 * 3. 运行程序：
 *    example_unified.exe
 * 
 * 4. 观察输出：
 *    - 程序会显示每个步骤的执行过程和结果
 *    - 成功的操作会显示 ✅
 *    - 失败的操作会显示 ❌
 * 
 * 5. 错误处理：
 *    - 程序包含完整的错误处理逻辑
 *    - 如果某个步骤失败，会尝试清理资源
 * 
 * 注意事项：
 * - 确保以管理员权限运行程序
 * - 确保目标驱动器号（如Z:）未被占用
 * - 确保虚拟磁盘文件路径正确且可访问
 */
