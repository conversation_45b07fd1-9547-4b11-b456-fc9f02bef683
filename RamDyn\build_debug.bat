@echo off
echo 诊断编译问题...

rem 设置Visual Studio环境
call "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat"

echo.
echo 1. 编译C文件...
cl /c /O2 /W3 /D_CRT_SECURE_NO_WARNINGS /DWIN32_LEAN_AND_MEAN /D_WIN64 /I"..\inc" RamDyn.c

if %ERRORLEVEL% NEQ 0 (
    echo C文件编译失败！
    pause
    exit /b 1
)

echo.
echo 2. 编译资源文件...
rc /I"..\inc" resource.rc

if %ERRORLEVEL% NEQ 0 (
    echo 资源文件编译失败！
    pause
    exit /b 1
)

echo.
echo 3. 链接生成可执行文件...
link RamDyn.obj resource.res /OUT:RamDyn64.exe /SUBSYSTEM:WINDOWS /ENTRY:wWinMain kernel32.lib user32.lib advapi32.lib wtsapi32.lib ntdll.lib

if %ERRORLEVEL% EQU 0 (
    echo 编译成功！生成了 RamDyn64.exe
    dir RamDyn64.exe
) else (
    echo 链接失败，错误代码: %ERRORLEVEL%
)

pause
