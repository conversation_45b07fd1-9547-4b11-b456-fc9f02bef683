﻿/*
 * mount_core.cpp
 * 虚拟磁盘挂载核心功能实现
 * 基于MountImg_Simple的核心逻辑
 */

#define _WIN32_WINNT 0x0601
#define OEMRESOURCE
#define _CRT_SECURE_NO_WARNINGS

#include <windows.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <shlwapi.h>
#include <wtsapi32.h>
#include <commdlg.h>
#include <winternl.h>
#include <winsvc.h>  // 服务管理API
#include <userenv.h>  // Token相关API

#include "mount_core.h"
#include "../../inc/imdisk.h"
#include "../../inc/imdisktk.h"

// ===== MountImg.c 完全兼容的全局变量定义 =====

// GetTickCount64运行时检测支持（完全按照MountImg.c）
typedef ULONGLONG (WINAPI *PGetTickCount64)(void);
static PGetTickCount64 g_pGetTickCount64 = NULL;
static BOOL g_bTickCountInitialized = FALSE;

// ImDisk API函数指针（完全按照MountImg.c第57行）
static FARPROC ImDisk_GetDeviceListEx = NULL;
static FARPROC ImDisk_ForceRemoveDevice = NULL;
static FARPROC ImDisk_NotifyShellDriveLetter = NULL;

// 设备类型和选项数组（完全按照MountImg.c第59-63行）
static WCHAR dev_list[] = {'h', 'c', 'f'};  // 硬盘、光盘、软盘
static WCHAR ro_list[] = {'w', 'o'};        // 读写、只读
static WCHAR *ro_discutils_list[] = {L"", L" /readonly"};  // DiscUtils只读参数
static WCHAR *rm_list[] = {L"fix", L"rem"}; // 固定、可移动
static WCHAR *boot_list[] = {L"", L" -P"};  // 启动参数

// 全局变量（完全按照MountImg.c第66-83行）
static WCHAR filename[MAX_PATH] = {L""};
static WCHAR mountdir[MAX_PATH] = {L""};
static WCHAR drive_list[27][4] = {L""};
static WCHAR drive[MAX_PATH + 2] = {L""};
static WCHAR txt[1024] = {L""};

// 状态变量（完全按照MountImg.c第70-75行）
static BYTE net_installed = FALSE;
static UINT dev_type = 0;
static UINT partition = 1;
static BOOL readonly = FALSE;
static BOOL removable = FALSE;
static BOOL win_boot = FALSE;
static long device_number = -1;

// 设备列表（完全按照MountImg.c第80行）
static ULONG list_device[64000];

// 服务句柄（完全按照MountImg.c第48行）
static SC_HANDLE h_svc = NULL;

// ===== MountImg.c 兼容性函数（完全按照MountImg.c第21-44行） =====

// 初始化GetTickCount64函数指针（完全按照MountImg.c第21-30行）
static void InitTickCountFunction(void)
{
    if (!g_bTickCountInitialized) {
        HMODULE hKernel32 = GetModuleHandle(L"kernel32.dll");
        if (hKernel32) {
            g_pGetTickCount64 = (PGetTickCount64)GetProcAddress(hKernel32, "GetTickCount64");
        }
        g_bTickCountInitialized = TRUE;
    }
}

// 兼容的GetTickCount函数（完全按照MountImg.c第33-44行）
static __int64 GetTickCount_Compatible(void)
{
    InitTickCountFunction();

    if (g_pGetTickCount64) {
        // Vista+系统：使用64位版本
        return (__int64)g_pGetTickCount64();
    } else {
        // XP系统：使用32位版本
        return (__int64)GetTickCount();
    }
}

/*
 * 初始化挂载核心模块
 */
int InitMountCore(void)
{
    // 检查ImDisk是否安装
    HKEY hKey;
    if (RegOpenKeyExW(HKEY_LOCAL_MACHINE,
                     L"SYSTEM\\CurrentControlSet\\Services\\ImDisk",
                     0, KEY_READ, &hKey) == ERROR_SUCCESS) {
        RegCloseKey(hKey);
        net_installed = TRUE;
    } else {
        net_installed = FALSE;
    }

    // 初始化驱动器列表
    DWORD drives = GetLogicalDrives();
    int index = 0;

    for (int i = 0; i < 26; i++) {
        if (!(drives & (1 << i))) {
            swprintf(drive_list[index], 4, L"%c:", L'A' + i);
            index++;
        }
    }

    return 0;
}

/*
 * 清理挂载核心模块
 */
void CleanupMountCore(void)
{
    // 清理全局变量
    memset(filename, 0, sizeof(filename));
    memset(mountdir, 0, sizeof(mountdir));
    memset(drive, 0, sizeof(drive));
    memset(txt, 0, sizeof(txt));
}

/*
 * 检测文件格式
 */
const char* DetectFileFormat(const char* filePath)
{
    if (!filePath) return NULL;
    
    // 转换为宽字符
    WCHAR wFilePath[MAX_PATH];
    MultiByteToWideChar(CP_UTF8, 0, filePath, -1, wFilePath, MAX_PATH);
    
    // 获取文件扩展名
    WCHAR* ext = PathFindExtensionW(wFilePath);
    if (!ext || !*ext) return "UNKNOWN";
    
    // 转换为小写进行比较
    _wcslwr(ext);
    
    if (wcscmp(ext, L".vmdk") == 0) return "VMDK";
    if (wcscmp(ext, L".vhdx") == 0) return "VHDX";
    if (wcscmp(ext, L".vhd") == 0) return "VHD";
    if (wcscmp(ext, L".iso") == 0) return "ISO";
    if (wcscmp(ext, L".img") == 0) return "IMG";
    if (wcscmp(ext, L".bin") == 0) return "BIN";
    if (wcscmp(ext, L".nrg") == 0) return "NRG";
    
    return "UNKNOWN";
}

/*
 * 获取磁盘信息
 */
int GetDiskInfo(const char* filePath, DiskInfo* diskInfo)
{
    if (!filePath || !diskInfo) return -1;
    
    memset(diskInfo, 0, sizeof(DiskInfo));
    
    // 转换路径
    WCHAR wFilePath[MAX_PATH];
    MultiByteToWideChar(CP_UTF8, 0, filePath, -1, wFilePath, MAX_PATH);
    
    // 复制文件路径
    strncpy(diskInfo->file_path, filePath, sizeof(diskInfo->file_path) - 1);
    
    // 检测格式
    const char* format = DetectFileFormat(filePath);
    strncpy(diskInfo->format_type, format, sizeof(diskInfo->format_type) - 1);
    
    // 获取文件大小
    HANDLE hFile = CreateFileW(wFilePath, GENERIC_READ, FILE_SHARE_READ, 
                              NULL, OPEN_EXISTING, FILE_ATTRIBUTE_NORMAL, NULL);
    if (hFile != INVALID_HANDLE_VALUE) {
        LARGE_INTEGER fileSize;
        if (GetFileSizeEx(hFile, &fileSize)) {
            diskInfo->size_bytes = fileSize.QuadPart;
            diskInfo->size_mb = (int)(fileSize.QuadPart / (1024 * 1024));
        }
        CloseHandle(hFile);
    }
    
    // 默认值
    diskInfo->partition_count = 1;
    diskInfo->is_readonly = 0;
    strcpy(diskInfo->file_system, "Unknown");
    
    return 0;
}

/*
 * 检查文件是否存在且可访问
 */
int IsFileAccessible(const char* filePath)
{
    if (!filePath) return 0;
    
    WCHAR wFilePath[MAX_PATH];
    MultiByteToWideChar(CP_UTF8, 0, filePath, -1, wFilePath, MAX_PATH);
    
    DWORD attrs = GetFileAttributesW(wFilePath);
    if (attrs == INVALID_FILE_ATTRIBUTES) return 0;
    
    // 检查是否为文件（不是目录）
    if (attrs & FILE_ATTRIBUTE_DIRECTORY) return 0;
    
    // 尝试打开文件
    HANDLE hFile = CreateFileW(wFilePath, GENERIC_READ, FILE_SHARE_READ, 
                              NULL, OPEN_EXISTING, FILE_ATTRIBUTE_NORMAL, NULL);
    if (hFile == INVALID_HANDLE_VALUE) return 0;
    
    CloseHandle(hFile);
    return 1;
}

/*
 * 查找可用的驱动器号
 */
const char* FindAvailableDrive(void)
{
    static char result[4];
    
    DWORD drives = GetLogicalDrives();
    
    // 从Z开始向前查找
    for (int i = 25; i >= 2; i--) { // 跳过A:和B:
        if (!(drives & (1 << i))) {
            sprintf(result, "%c:", 'A' + i);
            return result;
        }
    }
    
    return NULL;
}

/*
 * 检查驱动器是否被使用
 * 返回值：0=可用, 1=被占用, -1=参数错误
 */
int IsDriveInUse(const char* driveLetter)
{
    if (!driveLetter || strlen(driveLetter) < 2) return -1;

    char drive_path[4];
    sprintf(drive_path, "%c:\\", toupper(driveLetter[0]));

    // 检查驱动器类型
    UINT driveType = GetDriveTypeA(drive_path);

    // 输出调试信息
    char debug_info[128];
    sprintf(debug_info, "Checking drive %s: type=%d\n", drive_path, driveType);
    OutputDebugStringA(debug_info);

    // 如果驱动器不存在，则可用
    if (driveType == DRIVE_NO_ROOT_DIR) {
        OutputDebugStringA("Drive is available (not exists)\n");
        return 0;  // 可用
    }

    // 如果是可移动驱动器且没有媒体，也认为可用
    if (driveType == DRIVE_REMOVABLE) {
        // 尝试访问根目录来检查是否有媒体
        WIN32_FIND_DATAA findData;
        HANDLE hFind = FindFirstFileA(drive_path, &findData);
        if (hFind == INVALID_HANDLE_VALUE) {
            DWORD error = GetLastError();
            if (error == ERROR_NOT_READY || error == ERROR_PATH_NOT_FOUND) {
                OutputDebugStringA("Removable drive is available (no media)\n");
                return 0;  // 可用
            }
        } else {
            FindClose(hFind);
        }
    }

    // 其他情况认为被占用
    sprintf(debug_info, "Drive is in use (type=%d)\n", driveType);
    OutputDebugStringA(debug_info);
    return 1;  // 被占用
}

/*
 * 获取系统错误消息
 */
int GetSystemErrorMessage(DWORD errorCode, char* buffer, int bufferSize)
{
    if (!buffer || bufferSize <= 0) return -1;
    
    WCHAR wBuffer[512];
    DWORD result = FormatMessageW(
        FORMAT_MESSAGE_FROM_SYSTEM | FORMAT_MESSAGE_IGNORE_INSERTS,
        NULL, errorCode, MAKELANGID(LANG_NEUTRAL, SUBLANG_DEFAULT),
        wBuffer, sizeof(wBuffer) / sizeof(WCHAR), NULL);
    
    if (result > 0) {
        WideCharToMultiByte(CP_UTF8, 0, wBuffer, -1, buffer, bufferSize, NULL, NULL);
        
        // 移除末尾的换行符
        int len = (int)strlen(buffer);
        while (len > 0 && (buffer[len-1] == '\r' || buffer[len-1] == '\n')) {
            buffer[--len] = '\0';
        }
        
        return 0;
    }
    
    sprintf(buffer, "Error code: %lu", errorCode);
    return -1;
}

/*
 * 格式化时间为字符串
 */
int FormatTimeString(const SYSTEMTIME* systemTime, char* buffer, int bufferSize)
{
    if (!systemTime || !buffer || bufferSize <= 0) return -1;
    
    sprintf(buffer, "%04d-%02d-%02d %02d:%02d:%02d",
           systemTime->wYear, systemTime->wMonth, systemTime->wDay,
           systemTime->wHour, systemTime->wMinute, systemTime->wSecond);
    
    return 0;
}

/*
 * 检查.NET Framework是否安装（DiscUtils需要）
 */
static BOOL IsNetFrameworkInstalled(void)
{
    HKEY h_key;
    BOOL installed = FALSE;

    // 检查.NET Framework 4.0是否安装
    if (RegOpenKeyExA(HKEY_LOCAL_MACHINE,
                     "SOFTWARE\\Microsoft\\.NETFramework\\v4.0.30319",
                     0, KEY_QUERY_VALUE | KEY_WOW64_64KEY, &h_key) == ERROR_SUCCESS) {
        RegCloseKey(h_key);
        installed = TRUE;
        OutputDebugStringA("  .NET Framework 4.0: INSTALLED\n");
    } else {
        OutputDebugStringA("  .NET Framework 4.0: NOT FOUND\n");
    }

    return installed;
}

/*
 * 检查DiscUtils服务是否可用
 */
static BOOL IsDiscUtilsServiceAvailable(void)
{
    SC_HANDLE h_scman, h_svc;
    BOOL available = FALSE;

    h_scman = OpenSCManager(NULL, NULL, SC_MANAGER_CONNECT);
    if (h_scman) {
        h_svc = OpenService(h_scman, L"ImDiskImg", SERVICE_QUERY_STATUS | SERVICE_START);
        if (h_svc) {
            available = TRUE;
            CloseServiceHandle(h_svc);
            OutputDebugStringA("  DiscUtils service: AVAILABLE\n");
        } else {
            OutputDebugStringA("  DiscUtils service: NOT AVAILABLE\n");
        }
        CloseServiceHandle(h_scman);
    }

    return available;
}

/*
 * 检测文件格式并提供建议
 */
static void AnalyzeFileFormat(const WCHAR* imagePath, DWORD exitCode)
{
    // 获取文件扩展名
    const WCHAR* ext = wcsrchr(imagePath, L'.');
    if (!ext) {
        OutputDebugStringA("  📄 File format: Unknown (no extension)\n");
        return;
    }

    char debug_ext[32];
    WideCharToMultiByte(CP_UTF8, 0, ext, -1, debug_ext, sizeof(debug_ext), NULL, NULL);

    if (_wcsicmp(ext, L".vhd") == 0) {
        OutputDebugStringA("  📄 File format: VHD (Virtual Hard Disk)\n");
        if (exitCode == 0) {
            OutputDebugStringA("  ✅ ImDisk supports VHD format\n");
        } else {
            OutputDebugStringA("  ⚠️  VHD file may have partition/format issues\n");
        }
    } else if (_wcsicmp(ext, L".vhdx") == 0) {
        OutputDebugStringA("  📄 File format: VHDX (Virtual Hard Disk v2)\n");
        if (exitCode != 0) {
            OutputDebugStringA("  ⚠️  VHDX may need DiscUtils support\n");
        }
    } else if (_wcsicmp(ext, L".vmdk") == 0) {
        OutputDebugStringA("  📄 File format: VMDK (VMware Virtual Disk)\n");
        if (exitCode == 3) {
            OutputDebugStringA("  ❌ ImDisk does not support VMDK format\n");
            OutputDebugStringA("  💡 Suggestion: VMDK requires DiscUtils support\n");
            OutputDebugStringA("  💡 Please ensure DiscUtils service is installed and running\n");
        }
    } else if (_wcsicmp(ext, L".iso") == 0) {
        OutputDebugStringA("  📄 File format: ISO (CD/DVD Image)\n");
        OutputDebugStringA("  💡 Suggestion: Use -o cd option for ISO files\n");
    } else if (_wcsicmp(ext, L".img") == 0) {
        OutputDebugStringA("  📄 File format: IMG (Raw Disk Image)\n");
        OutputDebugStringA("  ✅ ImDisk supports IMG format\n");
    } else {
        sprintf(debug_ext, "  📄 File format: %s (Unknown)\n", debug_ext);
        OutputDebugStringA(debug_ext);
        OutputDebugStringA("  ⚠️  Unknown format, may need special handling\n");
    }
}

/*
 * 获取可用的临时挂载点
 */
static WCHAR GetAvailableTempDrive(void)
{
    // 从Z到A逆序查找可用驱动器
    for (WCHAR drive = L'Z'; drive >= L'A'; drive--) {
        WCHAR drive_path[4];
        swprintf(drive_path, sizeof(drive_path) / sizeof(WCHAR), L"%c:\\", drive);

        UINT driveType = GetDriveTypeW(drive_path);
        if (driveType == DRIVE_NO_ROOT_DIR) {
            char debug_temp[64];
            sprintf(debug_temp, "  Selected temp drive: %c:\n", (char)drive);
            OutputDebugStringA(debug_temp);
            return drive;
        }
    }

    OutputDebugStringA("  WARNING: No available temp drive found, using Z:\n");
    return L'Z';  // 默认使用Z:
}

/*
 * 获取可用的ImDisk设备号（完全按照MountImg.c第175-184行）
 */
static long get_imdisk_unit(void)
{
    long i, j;

    // 使用ImDisk API获取设备列表（完全按照MountImg.c）
    if (ImDisk_GetDeviceListEx && !((BOOL(*)(ULONG, PULONG))ImDisk_GetDeviceListEx)(_countof(list_device), list_device)) {
        OutputDebugStringA("  ERROR: ImDisk_GetDeviceListEx failed\n");
        return -1;
    }

    // 如果ImDisk API不可用，使用简化实现
    if (!ImDisk_GetDeviceListEx) {
        static long next_device = 0;
        long device_num = next_device++;
        if (device_num > 99) {
            next_device = 0;
            device_num = 0;
        }
        char debug_device[64];
        sprintf(debug_device, "  Allocated device number (fallback): %ld\n", device_num);
        OutputDebugStringA(debug_device);
        return device_num;
    }

    // 完全按照MountImg.c的算法查找可用设备号
    i = j = 0;
    while (++j <= list_device[0])
        if (list_device[j] == i) { j = 0; i++; }

    char debug_device[64];
    sprintf(debug_device, "  Allocated device number: %ld\n", i);
    OutputDebugStringA(debug_device);

    return i;
}

/*
 * DiscUtils挂载实现（完全参考MountImg.c的DiscUtils_Mount函数）
 */
static int DiscUtils_Mount(const WCHAR* imagePath, const WCHAR* driveLetter, int readonly, int partition)
{
    WCHAR cmdline1[MAX_PATH + 70], cmdline2[MAX_PATH + 70], txt_partition[24];
    WCHAR *cmdline_ptr[3] = {cmdline1, cmdline2, (WCHAR*)driveLetter};
    HANDLE h;
    int error;
    __int64 pipe;
    SC_HANDLE h_scman, h_svc;

    OutputDebugStringA("=== DiscUtils Mount Attempt ===\n");

    // 生成唯一管道名
    pipe = (GetTickCount() << 16) | (GetCurrentProcessId() & 0xFFFF);

    // 准备分区参数
    swprintf(txt_partition, sizeof(txt_partition) / sizeof(WCHAR),
             partition != 1 ? L" /partition=%d" : L"", partition);

    // 构建DiscUtils命令行参数
    swprintf(cmdline1, sizeof(cmdline1) / sizeof(WCHAR),
        L"/name=ImDisk%I64x%s /filename=\"%s\"%s",
        pipe, txt_partition, imagePath, readonly ? L" /readonly" : L"");

    swprintf(cmdline2, sizeof(cmdline2) / sizeof(WCHAR),
        L"-o shm,h,r%c,fix -f ImDisk%I64x",
        readonly ? L'o' : L'w', pipe);

    // 输出调试信息
    char debug_cmd1[MAX_PATH + 100], debug_cmd2[MAX_PATH + 100];
    WideCharToMultiByte(CP_UTF8, 0, cmdline1, -1, debug_cmd1, sizeof(debug_cmd1), NULL, NULL);
    WideCharToMultiByte(CP_UTF8, 0, cmdline2, -1, debug_cmd2, sizeof(debug_cmd2), NULL, NULL);
    OutputDebugStringA("  DiscUtils cmdline1: ");
    OutputDebugStringA(debug_cmd1);
    OutputDebugStringA("\n");
    OutputDebugStringA("  DiscUtils cmdline2: ");
    OutputDebugStringA(debug_cmd2);
    OutputDebugStringA("\n");

    // 创建信号量
    h = CreateSemaphoreA(NULL, 0, 2, "Global\\MountImgSvcSema");
    if (!h) {
        DWORD semError = GetLastError();
        char debug_sem[128];
        sprintf(debug_sem, "  CreateSemaphore failed, error=%lu\n", semError);
        OutputDebugStringA(debug_sem);
        return 1;
    }

    // 打开服务
    h_scman = OpenSCManager(NULL, NULL, SC_MANAGER_CONNECT);
    if (!h_scman) {
        OutputDebugStringA("  OpenSCManager failed\n");
        CloseHandle(h);
        return 1;
    }

    h_svc = OpenService(h_scman, L"ImDiskImg", SERVICE_START);
    if (!h_svc) {
        OutputDebugStringA("  OpenService failed\n");
        CloseServiceHandle(h_scman);
        CloseHandle(h);
        return 1;
    }

    // 启动服务
    OutputDebugStringA("  Starting DiscUtils service...\n");
    if (!StartService(h_svc, 3, (LPCWSTR*)cmdline_ptr)) {
        DWORD startError = GetLastError();
        char debug_start[128];
        sprintf(debug_start, "  StartService failed, error=%lu\n", startError);
        OutputDebugStringA(debug_start);
        error = 1;
    } else {
        // 等待服务完成
        OutputDebugStringA("  Waiting for DiscUtils service completion...\n");
        DWORD waitResult1 = WaitForSingleObject(h, 15000);
        DWORD waitResult2 = (waitResult1 == WAIT_OBJECT_0) ? WaitForSingleObject(h, 0) : WAIT_FAILED;

        error = (waitResult1 != WAIT_OBJECT_0) || (waitResult2 == WAIT_OBJECT_0);

        char debug_wait[128];
        sprintf(debug_wait, "  Wait results: %lu, %lu, error=%d\n", waitResult1, waitResult2, error);
        OutputDebugStringA(debug_wait);
    }

    CloseServiceHandle(h_svc);
    CloseServiceHandle(h_scman);
    CloseHandle(h);

    if (error == 0) {
        OutputDebugStringA("  DiscUtils mount: SUCCESS\n");
    } else {
        OutputDebugStringA("  DiscUtils mount: FAILED\n");
    }

    return error;
}

/*
 * 启动进程（完全按照MountImg.c第135-163行的start_process函数）
 */
static DWORD start_process(WCHAR *cmd, BYTE flag)
{
    STARTUPINFOW si = {sizeof(si)};
    PROCESS_INFORMATION pi;
    TOKEN_LINKED_TOKEN lt = { 0 };
    HANDLE token;
    DWORD dw;
    BOOL result;

    // 输出执行的命令（调试增强）
    char debug_cmd[MAX_PATH * 2 + 100];
    WideCharToMultiByte(CP_UTF8, 0, cmd, -1, debug_cmd, sizeof(debug_cmd), NULL, NULL);
    OutputDebugStringA("  Executing: ");
    OutputDebugStringA(debug_cmd);
    OutputDebugStringA("\n");

    // 完全按照MountImg.c的进程创建逻辑
    if (flag == 2 && (dw = WTSGetActiveConsoleSessionId()) != -1 && WTSQueryUserToken(dw, &token)) {
        if (!GetTokenInformation(token, TokenLinkedToken, &lt, sizeof lt, &dw) ||
            !(result = CreateProcessAsUserW(lt.LinkedToken, NULL, cmd, NULL, NULL, FALSE, CREATE_NO_WINDOW, NULL, NULL, &si, &pi)))
            result = CreateProcessAsUserW(token, NULL, cmd, NULL, NULL, FALSE, CREATE_NO_WINDOW, NULL, NULL, &si, &pi);
        CloseHandle(token);
        CloseHandle(lt.LinkedToken);
    } else
        result = CreateProcessW(NULL, cmd, NULL, NULL, FALSE, CREATE_NO_WINDOW, NULL, NULL, &si, &pi);

    dw = 1;
    if (result) {
        OutputDebugStringA("  Process created successfully\n");
        if (flag) {
            OutputDebugStringA("  Waiting for process completion...\n");
            WaitForSingleObject(pi.hProcess, INFINITE);
            GetExitCodeProcess(pi.hProcess, &dw);

            char debug_exit[64];
            sprintf(debug_exit, "  Process completed, exit code=%lu\n", dw);
            OutputDebugStringA(debug_exit);
        }
        CloseHandle(pi.hProcess);
        CloseHandle(pi.hThread);
    } else {
        DWORD error = GetLastError();
        char debug_error[128];
        sprintf(debug_error, "  Process creation failed, error=%lu\n", error);
        OutputDebugStringA(debug_error);
        return error;
    }

    return (dw != 0) ? dw : 0;  // 完全按照MountImg.c的返回逻辑
}

/*
 * ImDisk挂载实现（严格按照MountImg.c的Imdisk_Mount函数逻辑）
 * 参数no_check_fs对应MountImg.c中的no_check_fs参数
 */
static int Imdisk_Mount_Internal(const WCHAR* imagePath, const WCHAR* driveLetter,
                                int readonly, int partition, BYTE no_check_fs)
{
    WCHAR cmdline[MAX_PATH * 2 + 80];
    WCHAR txt_partition[16];
    BOOL fs_ok = FALSE;
    BYTE retry = !partition;  // 完全按照MountImg.c: BYTE retry = !partition;
    long device_number;

    // 设备类型和选项（完全按照MountImg.c的全局变量）
    WCHAR dev_list[] = {'h', 'c', 'f'};     // 硬盘、光盘、软盘
    WCHAR ro_list[] = {'w', 'o'};           // 读写、只读
    LPCWSTR rm_list[] = {L"fix", L"rem"};   // 固定、可移动
    LPCWSTR boot_list[] = {L"", L" -P"};    // 启动选项

    // 设备类型：默认硬盘(0)，可以根据文件扩展名判断
    int dev_type = 0;  // 0=硬盘, 1=光盘, 2=软盘
    int removable = 0; // 0=固定, 1=可移动
    int win_boot = 0;  // 0=无启动, 1=启动

    // 输出调试信息
    OutputDebugStringA("\n");
    OutputDebugStringA("╔════════════════════════════════════════╗\n");
    OutputDebugStringA("║     ImDisk Mount (MountImg Logic)     ║\n");
    OutputDebugStringA("╚════════════════════════════════════════╝\n");

    char debug_info[512];
    sprintf(debug_info, "Parameters:\n");
    OutputDebugStringA(debug_info);
    sprintf(debug_info, "  imagePath: %S\n", imagePath);
    OutputDebugStringA(debug_info);
    sprintf(debug_info, "  driveLetter: %S\n", driveLetter);
    OutputDebugStringA(debug_info);
    sprintf(debug_info, "  readonly: %d\n", readonly);
    OutputDebugStringA(debug_info);
    sprintf(debug_info, "  partition: %d\n", partition);
    OutputDebugStringA(debug_info);
    sprintf(debug_info, "  no_check_fs: %d\n", no_check_fs);
    OutputDebugStringA(debug_info);
    sprintf(debug_info, "  retry: %d\n", retry);
    OutputDebugStringA(debug_info);

    // 准备分区参数（完全按照MountImg.c）
    swprintf(txt_partition, sizeof(txt_partition) / sizeof(WCHAR),
             partition > 1 ? L" -v %d" : L"", partition);

    // 双重挂载策略的第一阶段：验证挂载
    OutputDebugStringA("\n┌─────────────────────────────────────────┐\n");
    OutputDebugStringA("│  Phase 1: Verification Mount Loop      │\n");
    OutputDebugStringA("└─────────────────────────────────────────┘\n");
    do {
        // 获取设备号（完全按照MountImg.c）
        if ((device_number = get_imdisk_unit()) < 0) {
            OutputDebugStringA("ERROR: Cannot get ImDisk unit\n");
            return 1;
        }

        sprintf(debug_info, "Attempt %d: Using device number %ld\n", retry + 1, device_number);
        OutputDebugStringA(debug_info);

        // 构建验证挂载命令（添加临时挂载点用于验证）
        WCHAR temp_drive_letter = GetAvailableTempDrive();
        WCHAR temp_drive[8];
        swprintf(temp_drive, sizeof(temp_drive) / sizeof(WCHAR), L"%c:", temp_drive_letter);

        // 构建验证挂载命令，根据分区参数决定是否添加-v选项
        if (partition > 1) {
            swprintf(cmdline, sizeof(cmdline) / sizeof(WCHAR),
                L"imdisk -a -u %ld -m \"%s\" -f \"%s\" -o ro -v %d",
                device_number,                    // 设备号
                temp_drive,                       // 临时挂载点
                imagePath,                        // 文件路径
                partition);                       // 分区号
        } else {
            swprintf(cmdline, sizeof(cmdline) / sizeof(WCHAR),
                L"imdisk -a -u %ld -m \"%s\" -f \"%s\" -o ro",
                device_number,                    // 设备号
                temp_drive,                       // 临时挂载点
                imagePath);                       // 文件路径
        }

        // 输出命令
        char debug_cmd[MAX_PATH * 2 + 100];
        WideCharToMultiByte(CP_UTF8, 0, cmdline, -1, debug_cmd, sizeof(debug_cmd), NULL, NULL);
        OutputDebugStringA("Verification mount command: ");
        OutputDebugStringA(debug_cmd);
        OutputDebugStringA("\n");

        // 执行验证挂载（完全按照MountImg.c第613行）
        DWORD mountResult = start_process(cmdline, 1);
        if (mountResult != 0) {
            OutputDebugStringA("Verification mount failed\n");

            // 分析文件格式并提供建议
            AnalyzeFileFormat(imagePath, mountResult);

            return 1;
        }
        OutputDebugStringA("Verification mount succeeded\n");

        // 验证文件系统（使用挂载点而不是设备路径）
        WCHAR volume_path[8];
        swprintf(volume_path, sizeof(volume_path) / sizeof(WCHAR), L"%s\\", temp_drive);

        char debug_volume[64];
        WideCharToMultiByte(CP_UTF8, 0, volume_path, -1, debug_volume, sizeof(debug_volume), NULL, NULL);
        OutputDebugStringA("Checking volume: ");
        OutputDebugStringA(debug_volume);
        OutputDebugStringA("\n");

        // 等待一下让挂载完成
        Sleep(1000);  // 增加等待时间

        // 详细的文件系统验证
        OutputDebugStringA("  Attempting file system verification...\n");

        // 首先检查驱动器是否存在
        UINT driveType = GetDriveTypeW(volume_path);
        char debug_drive_type[128];
        sprintf(debug_drive_type, "  Drive type: %d (0=unknown, 1=no_root, 2=removable, 3=fixed, 4=remote, 5=cdrom, 6=ramdisk)\n", driveType);
        OutputDebugStringA(debug_drive_type);

        if (driveType == DRIVE_NO_ROOT_DIR) {
            OutputDebugStringA("  ERROR: Drive not accessible (DRIVE_NO_ROOT_DIR)\n");
            fs_ok = FALSE;
        } else if (driveType == DRIVE_FIXED || driveType == DRIVE_REMOVABLE) {
            OutputDebugStringA("  Drive exists and is accessible\n");
            // 尝试获取卷信息
            WCHAR volumeLabel[256] = {0};
            WCHAR fileSystem[256] = {0};
            DWORD serialNumber = 0;
            DWORD maxComponentLength = 0;
            DWORD fileSystemFlags = 0;

            fs_ok = GetVolumeInformationW(volume_path,
                                        volumeLabel, sizeof(volumeLabel)/sizeof(WCHAR),
                                        &serialNumber,
                                        &maxComponentLength,
                                        &fileSystemFlags,
                                        fileSystem, sizeof(fileSystem)/sizeof(WCHAR));

            if (fs_ok) {
                char debug_fs[256];
                WideCharToMultiByte(CP_UTF8, 0, fileSystem, -1, debug_fs, sizeof(debug_fs), NULL, NULL);
                sprintf(debug_fs, "  File system: %s\n", debug_fs);
                OutputDebugStringA(debug_fs);

                char debug_serial[64];
                sprintf(debug_serial, "  Serial number: 0x%08X\n", serialNumber);
                OutputDebugStringA(debug_serial);
            } else {
                DWORD error = GetLastError();
                char debug_error[128];
                sprintf(debug_error, "  GetVolumeInformation failed, error=%lu\n", error);
                OutputDebugStringA(debug_error);

                // 尝试简单的目录访问测试
                OutputDebugStringA("  Trying simple directory access test...\n");
                WIN32_FIND_DATAW findData;
                WCHAR searchPath[16];
                swprintf(searchPath, sizeof(searchPath)/sizeof(WCHAR), L"%s*", volume_path);

                HANDLE hFind = FindFirstFileW(searchPath, &findData);
                if (hFind != INVALID_HANDLE_VALUE) {
                    OutputDebugStringA("  Directory access test: SUCCESS\n");
                    FindClose(hFind);
                    fs_ok = TRUE;  // 目录可访问，认为验证成功
                } else {
                    DWORD findError = GetLastError();
                    sprintf(debug_error, "  Directory access test failed, error=%lu\n", findError);
                    OutputDebugStringA(debug_error);

                    // 对于某些VHD文件，即使无法获取详细信息，但驱动器存在就认为成功
                    if (driveType == DRIVE_FIXED || driveType == DRIVE_REMOVABLE) {
                        OutputDebugStringA("  WARNING: Drive exists but filesystem info unavailable, treating as success\n");
                        fs_ok = TRUE;
                    }
                }
            }
        } else {
            OutputDebugStringA("  ERROR: Unsupported drive type\n");
            fs_ok = FALSE;
        }

        if (fs_ok) {
            OutputDebugStringA("File system verification: SUCCESS\n");
        } else {
            DWORD volError = GetLastError();
            sprintf(debug_info, "File system verification: FAILED (error=%lu)\n", volError);
            OutputDebugStringA(debug_info);
        }

        // 删除验证设备（使用挂载点卸载更可靠）
        swprintf(cmdline, sizeof(cmdline) / sizeof(WCHAR), L"imdisk -d -m \"%s\"", temp_drive);

        WideCharToMultiByte(CP_UTF8, 0, cmdline, -1, debug_cmd, sizeof(debug_cmd), NULL, NULL);
        OutputDebugStringA("Cleanup command: ");
        OutputDebugStringA(debug_cmd);
        OutputDebugStringA("\n");

        start_process(cmdline, 1);
        OutputDebugStringA("Verification device cleaned up\n");

    } while (!fs_ok && ++retry < 2);  // 完全按照MountImg.c第618行

    // 双重挂载策略的第二阶段：正式挂载
    OutputDebugStringA("\n┌─────────────────────────────────────────┐\n");
    OutputDebugStringA("│  Phase 2: Final Mount                  │\n");
    OutputDebugStringA("└─────────────────────────────────────────┘\n");
    if (fs_ok || no_check_fs) {  // 完全按照MountImg.c第619行
        sprintf(debug_info, "Proceeding with final mount (fs_ok=%d, no_check_fs=%d)\n", fs_ok, no_check_fs);
        OutputDebugStringA(debug_info);

        // 获取新的设备号用于正式挂载（完全按照MountImg.c第620行）
        if ((device_number = get_imdisk_unit()) < 0) {
            OutputDebugStringA("ERROR: Cannot get ImDisk unit for final mount\n");
            return 1;
        }

        sprintf(debug_info, "Final mount using device number: %ld\n", device_number);
        OutputDebugStringA(debug_info);

        // 构建正式挂载命令，根据分区参数决定是否添加-v选项
        if (partition > 1) {
            swprintf(cmdline, sizeof(cmdline) / sizeof(WCHAR),
                L"imdisk -a -u %ld -m \"%s\" -f \"%s\" -o %s -v %d",
                device_number,                    // 设备号
                driveLetter,                      // 挂载点
                imagePath,                        // 文件路径
                readonly ? L"ro" : L"rw",        // 读写模式
                partition);                       // 分区号
        } else {
            swprintf(cmdline, sizeof(cmdline) / sizeof(WCHAR),
                L"imdisk -a -u %ld -m \"%s\" -f \"%s\" -o %s",
                device_number,                    // 设备号
                driveLetter,                      // 挂载点
                imagePath,                        // 文件路径
                readonly ? L"ro" : L"rw");       // 读写模式
        }

        // 输出最终命令
        char debug_final_cmd[MAX_PATH * 2 + 100];
        WideCharToMultiByte(CP_UTF8, 0, cmdline, -1, debug_final_cmd, sizeof(debug_final_cmd), NULL, NULL);
        OutputDebugStringA("Final mount command: ");
        OutputDebugStringA(debug_final_cmd);
        OutputDebugStringA("\n");

        // 执行正式挂载（完全按照MountImg.c第623行）
        DWORD result = start_process(cmdline, 1);

        sprintf(debug_info, "Final mount result: %lu\n", result);
        OutputDebugStringA(debug_info);

        if (result == 0) {
            OutputDebugStringA("\n🎉 ╔═══════════════════════════════════════╗\n");
            OutputDebugStringA("   ║           MOUNT SUCCESS!              ║\n");
            OutputDebugStringA("   ╚═══════════════════════════════════════╝\n");

            // 验证驱动器可访问性
            char drive_check[8];
            WideCharToMultiByte(CP_UTF8, 0, driveLetter, -1, drive_check, sizeof(drive_check), NULL, NULL);
            if (strlen(drive_check) > 0 && drive_check[strlen(drive_check)-1] != '\\') {
                strcat(drive_check, "\\");
            }

            UINT driveType = GetDriveTypeA(drive_check);
            sprintf(debug_info, "Mounted drive %s type: %d\n", drive_check, driveType);
            OutputDebugStringA(debug_info);

            if (driveType != DRIVE_NO_ROOT_DIR) {
                OutputDebugStringA("✅ Drive is accessible - MOUNT VERIFIED\n");

                // 获取磁盘大小信息
                ULARGE_INTEGER freeBytesAvailable, totalNumberOfBytes, totalNumberOfFreeBytes;
                if (GetDiskFreeSpaceExA(drive_check, &freeBytesAvailable, &totalNumberOfBytes, &totalNumberOfFreeBytes)) {
                    char size_info[256];
                    double totalMB = (double)totalNumberOfBytes.QuadPart / (1024.0 * 1024.0);
                    double freeMB = (double)totalNumberOfFreeBytes.QuadPart / (1024.0 * 1024.0);
                    sprintf(size_info, "📊 Disk size: %.2f MB, Free: %.2f MB\n", totalMB, freeMB);
                    OutputDebugStringA(size_info);
                }

                // 尝试获取卷标
                char volumeLabel[256] = {0};
                if (GetVolumeInformationA(drive_check, volumeLabel, sizeof(volumeLabel), NULL, NULL, NULL, NULL, 0)) {
                    if (strlen(volumeLabel) > 0) {
                        char label_info[300];
                        sprintf(label_info, "🏷️  Volume label: %s\n", volumeLabel);
                        OutputDebugStringA(label_info);
                    }
                }
            } else {
                OutputDebugStringA("⚠️  WARNING: Drive not accessible after mount\n");
            }
        } else {
            OutputDebugStringA("\n❌ ╔═══════════════════════════════════════╗\n");
            OutputDebugStringA("   ║           MOUNT FAILED!               ║\n");
            OutputDebugStringA("   ╚═══════════════════════════════════════╝\n");
        }

        OutputDebugStringA("\n╚════════════════════════════════════════╝\n");
        return (result == 0) ? 0 : (int)result;  // 完全按照MountImg.c第623行
    } else {
        OutputDebugStringA("\n❌ ╔═══════════════════════════════════════╗\n");
        OutputDebugStringA("   ║        VERIFICATION FAILED!          ║\n");
        OutputDebugStringA("   ║  File system verification failed     ║\n");
        OutputDebugStringA("   ╚═══════════════════════════════════════╝\n");
        OutputDebugStringA("╚════════════════════════════════════════╝\n");
        return 1;  // 完全按照MountImg.c第624行
    }
}

/*
 * ImDisk挂载接口函数
 */
static int Imdisk_Mount(const WCHAR* imagePath, const WCHAR* driveLetter, int readonly, int partition)
{
    // 调用内部实现，no_check_fs=0（正常检查文件系统）
    return Imdisk_Mount_Internal(imagePath, driveLetter, readonly, partition, 0);
}




/*
 * 挂载虚拟磁盘
 */
int MountDiskImage(const char* filePath, const char* driveLetter,
                   int readonly, int partition, MountStrategy strategy,
                   MountInfo* mountInfo)
{
    if (!filePath || !mountInfo) return -1;

    // 初始化挂载信息
    memset(mountInfo, 0, sizeof(MountInfo));

    // 转换路径为宽字符
    WCHAR wFilePath[MAX_PATH];
    WCHAR wDriveLetter[8] = L"";

    MultiByteToWideChar(CP_UTF8, 0, filePath, -1, wFilePath, MAX_PATH);

    // 处理驱动器号
    if (driveLetter && strlen(driveLetter) > 0) {
        MultiByteToWideChar(CP_UTF8, 0, driveLetter, -1, wDriveLetter, 8);
    } else {
        // 自动分配驱动器号
        const char* availDrive = FindAvailableDrive();
        if (!availDrive) return -1;
        MultiByteToWideChar(CP_UTF8, 0, availDrive, -1, wDriveLetter, 8);
    }

    int result = -1;
    BOOL net_installed = FALSE;
    BOOL discutils_available = FALSE;

    // 检查DiscUtils可用性（参考MountImg_Simple逻辑）
    OutputDebugStringA("=== Checking DiscUtils Availability ===\n");
    net_installed = IsNetFrameworkInstalled();
    if (net_installed) {
        discutils_available = IsDiscUtilsServiceAvailable();
    }

    char debug_avail[128];
    sprintf(debug_avail, "  .NET installed: %s\n", net_installed ? "YES" : "NO");
    OutputDebugStringA(debug_avail);
    sprintf(debug_avail, "  DiscUtils available: %s\n", discutils_available ? "YES" : "NO");
    OutputDebugStringA(debug_avail);

    // 根据策略选择挂载方式（完全参考MountImg_Simple的Mount函数）
    switch (strategy) {
        case MOUNT_STRATEGY_IMDISK_FIRST:
            OutputDebugStringA("\n🎯 ═══ Strategy: ImDisk Only ═══\n");
            OutputDebugStringA("   Using ImDisk driver exclusively\n");
            result = Imdisk_Mount(wFilePath, wDriveLetter, readonly, partition);
            mountInfo->strategy_used = MOUNT_STRATEGY_IMDISK_FIRST;
            break;

        case MOUNT_STRATEGY_DISCUTILS_FIRST:
            OutputDebugStringA("\n🎯 ═══ Strategy: DiscUtils First ═══\n");
            if (net_installed && discutils_available) {
                OutputDebugStringA("   Using DiscUtils (.NET) driver\n");
                result = DiscUtils_Mount(wFilePath, wDriveLetter, readonly, partition);
                mountInfo->strategy_used = MOUNT_STRATEGY_DISCUTILS_FIRST;
            } else {
                OutputDebugStringA("   ⚠️  DiscUtils not available, falling back to ImDisk\n");
                result = Imdisk_Mount(wFilePath, wDriveLetter, readonly, partition);
                mountInfo->strategy_used = MOUNT_STRATEGY_IMDISK_FIRST;
            }
            break;

        case MOUNT_STRATEGY_AUTO:
        default:
            // 自动策略：完全参考MountImg_Simple的Mount函数逻辑
            OutputDebugStringA("\n🎯 ═══ Strategy: Auto (Smart Fallback) ═══\n");
            OutputDebugStringA("   Will try ImDisk first, then DiscUtils if needed\n");

            // 第一步：尝试ImDisk挂载
            OutputDebugStringA("   🔄 Step 1: Trying ImDisk...\n");
            result = Imdisk_Mount(wFilePath, wDriveLetter, readonly, partition);
            mountInfo->strategy_used = MOUNT_STRATEGY_IMDISK_FIRST;

            // 第二步：如果ImDisk失败且DiscUtils可用，尝试DiscUtils
            if (result != 0 && net_installed && discutils_available) {
                OutputDebugStringA("   🔄 Step 2: ImDisk failed, trying DiscUtils...\n");
                result = DiscUtils_Mount(wFilePath, wDriveLetter, readonly, partition);
                if (result == 0) {
                    mountInfo->strategy_used = MOUNT_STRATEGY_DISCUTILS_FIRST;
                    OutputDebugStringA("   ✅ DiscUtils succeeded!\n");
                } else {
                    OutputDebugStringA("   ❌ DiscUtils also failed\n");
                }
            } else if (result != 0) {
                OutputDebugStringA("   ❌ ImDisk failed, DiscUtils not available\n");
                OutputDebugStringA("   💡 Suggestion: Install DiscUtils for better format support\n");
            } else {
                OutputDebugStringA("   ✅ ImDisk succeeded!\n");
            }
            break;
    }

    if (result == 0) {
        // 挂载成功，填充挂载信息
        WideCharToMultiByte(CP_UTF8, 0, wDriveLetter, -1,
                           mountInfo->drive_letter, sizeof(mountInfo->drive_letter), NULL, NULL);
        strncpy(mountInfo->image_path, filePath, sizeof(mountInfo->image_path) - 1);
        mountInfo->readonly = readonly;
        mountInfo->partition = partition;
        GetSystemTime(&mountInfo->mount_time);

        // 尝试获取文件系统信息
        char rootPath[8];
        sprintf(rootPath, "%c:\\", mountInfo->drive_letter[0]);

        char fsName[32];
        if (GetVolumeInformationA(rootPath, NULL, 0, NULL, NULL, NULL, fsName, sizeof(fsName))) {
            strncpy(mountInfo->file_system, fsName, sizeof(mountInfo->file_system) - 1);
        } else {
            strcpy(mountInfo->file_system, "Unknown");
        }
    }

    return result;
}

/*
 * 卸载虚拟磁盘（使用命令行方式）
 */
int UnmountDiskImage(const char* driveLetter, int force)
{
    if (!driveLetter || strlen(driveLetter) < 1) return -1;

    WCHAR wDriveLetter[8];
    WCHAR cmdline[64];
    STARTUPINFOW si;
    PROCESS_INFORMATION pi;
    DWORD exit_code;

    // 转换驱动器号
    MultiByteToWideChar(CP_UTF8, 0, driveLetter, -1, wDriveLetter, 8);

    // 构建ImDisk卸载命令行
    swprintf(cmdline, sizeof(cmdline) / sizeof(WCHAR),
        L"imdisk -d -m \"%s\" %s",
        wDriveLetter,
        force ? L"-f" : L"");

    // 初始化启动信息
    memset(&si, 0, sizeof(si));
    si.cb = sizeof(si);
    si.dwFlags = STARTF_USESHOWWINDOW;
    si.wShowWindow = SW_HIDE;

    // 启动进程
    if (!CreateProcessW(NULL, cmdline, NULL, NULL, FALSE, 0, NULL, NULL, &si, &pi)) {
        return GetLastError();
    }

    // 等待进程完成
    WaitForSingleObject(pi.hProcess, 15000); // 15秒超时

    // 获取退出代码
    if (!GetExitCodeProcess(pi.hProcess, &exit_code)) {
        exit_code = GetLastError();
    }

    CloseHandle(pi.hProcess);
    CloseHandle(pi.hThread);

    return (exit_code == 0) ? 0 : (int)exit_code;
}

/*
 * 获取挂载状态
 */
int GetMountInfo(const char* driveLetter, MountInfo* mountInfo)
{
    if (!driveLetter || !mountInfo || strlen(driveLetter) < 1) return -1;

    // 初始化挂载信息
    memset(mountInfo, 0, sizeof(MountInfo));

    // 检查驱动器是否存在
    if (IsDriveInUse(driveLetter) <= 0) {
        return 1; // 未挂载
    }

    // 填充基本信息
    strncpy(mountInfo->drive_letter, driveLetter, sizeof(mountInfo->drive_letter) - 1);

    // 获取文件系统信息
    char rootPath[8];
    sprintf(rootPath, "%c:\\", driveLetter[0]);

    char fsName[32];
    if (GetVolumeInformationA(rootPath, NULL, 0, NULL, NULL, NULL, fsName, sizeof(fsName))) {
        strncpy(mountInfo->file_system, fsName, sizeof(mountInfo->file_system) - 1);
    } else {
        strcpy(mountInfo->file_system, "Unknown");
    }

    // 设置默认值
    strcpy(mountInfo->image_path, "Unknown");
    mountInfo->readonly = 0;
    mountInfo->partition = 1;
    GetSystemTime(&mountInfo->mount_time);
    mountInfo->strategy_used = MOUNT_STRATEGY_AUTO;

    return 0; // 已挂载
}
