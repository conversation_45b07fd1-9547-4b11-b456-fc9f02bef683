﻿  MountImg.c
E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\MountImg\MountImg.c(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\MountImg\MountImg.c(2,1): warning C4005: “OEMRESOURCE”: 宏重定义
E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\MountImg\MountImg.c : message : 参见“OEMRESOURCE”的前一个定义
E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\MountImg\MountImg.c(114,32): warning C4133: “初始化”: 从“char [1]”到“WCHAR *”的类型不兼容
E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\MountImg\MountImg.c(139,16): warning C4018: “<”: 有符号/无符号不匹配
E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\MountImg\MountImg.c(207,13): warning C4018: “<=”: 有符号/无符号不匹配
E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\MountImg\MountImg.c(320,16): warning C4244: “=”: 从“__int64”转换到“double”，可能丢失数据
E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\MountImg\MountImg.c(507,108): warning C4267: “函数”: 从“size_t”转换到“DWORD”，可能丢失数据
E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\MountImg\MountImg.c(509,102): warning C4267: “函数”: 从“size_t”转换到“DWORD”，可能丢失数据
E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\MountImg\MountImg.c(532,43): warning C4477: “_snwprintf”: 格式字符串“%s”需要类型“unsigned short *”的参数，但可变参数 1 拥有了类型“LPVOID”
E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\MountImg\MountImg.c(854,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\MountImg\MountImg.c(1231,69): warning C4267: “=”: 从“size_t”转换到“int”，可能丢失数据
E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\MountImg\MountImg.c(1478,112): warning C4267: “函数”: 从“size_t”转换到“DWORD”，可能丢失数据
E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\MountImg\MountImg.c(1643,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\MountImg\MountImg.c(1654,2): error C4996: 'GetVersionExW': 被声明为已否决
