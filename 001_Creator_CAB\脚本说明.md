# 磁盘挂载工具启动脚本说明

## 原始参考脚本
```batch
if not "%1"=="7" start /min cmd /c ""%~0" 7 %*" & exit /b
set F=%TEMP%\ImDisk%TIME::=%
extrac32.exe /e /l "%F%" "%~dp0files.cab"
"%F%\config.exe" %2 %3 %4
rd /s /q "%F%"
```

## 创建的脚本文件

### 1. `run.bat` - 超简洁版本
**特点**: 完全模仿原始脚本的简洁风格，一行代码完成解压
**兼容性**: Windows XP及以上
**推荐**: 追求极简的用户

```batch
@echo off
if not "%1"=="7" start /min cmd /c ""%~0" 7 %*" & exit /b
set F=%TEMP%\DiskUp%TIME::=%
mkdir "%F%" & echo Set s=CreateObject("Shell.Application"):s.NameSpace("%F%").CopyHere s.NameSpace("%~dp0files.zip").Items,20 > "%TEMP%\x.vbs" & cscript //nologo "%TEMP%\x.vbs" & del "%TEMP%\x.vbs" & timeout /t 2 /nobreak >nul 2>&1
if exist "%F%\config.exe" ("%F%\config.exe" %2 %3 %4) else for /r "%F%" %%i in (config.exe) do if exist "%%i" "%%i" %2 %3 %4
rd /s /q "%F%"
```

### 2. `launch.bat` - 易读版本
**特点**: 保持原始结构，代码易读
**兼容性**: Windows XP及以上
**推荐**: 需要维护和理解代码的用户

### 3. `DiskTool.bat` - VBScript版本
**特点**: 使用VBScript解压，兼容性最好
**兼容性**: Windows XP及以上
**推荐**: XP系统用户

### 4. `DiskTool_PS.bat` - PowerShell版本
**特点**: 使用PowerShell解压，速度最快
**兼容性**: Windows 7及以上
**推荐**: 现代Windows系统用户

### 5. `DiskTool_Enhanced.bat` - 智能版本
**特点**: 自动检测系统版本，选择最佳解压方法
**兼容性**: Windows XP及以上
**推荐**: 需要跨系统兼容的用户

## 核心设计理念

### 1. 参数传递机制
```batch
if not "%1"=="7" start /min cmd /c ""%~0" 7 %*" & exit /b
```
- 如果第一个参数不是"7"，则在最小化窗口中重新启动自己，传递"7"作为第一个参数
- 这样确保实际执行时第一个参数是"7"，后续参数从%2开始

### 2. 临时目录命名
```batch
set F=%TEMP%\DiskUp%TIME::=%
```
- 使用当前时间创建唯一的临时目录名
- `%TIME::=%` 去除时间中的冒号，避免路径问题
- 例如: `C:\Users\<USER>\AppData\Local\Temp\DiskUp143052`

### 3. 解压方法对比

| 方法 | 兼容性 | 速度 | 可靠性 | 代码复杂度 |
|------|--------|------|--------|------------|
| extrac32.exe | XP+ | 快 | 高 | 低 |
| VBScript | XP+ | 中 | 高 | 中 |
| PowerShell | 7+ | 最快 | 高 | 低 |

### 4. 错误处理策略
- **原始脚本**: 无错误处理，假设一切正常
- **新脚本**: 增加了config.exe查找逻辑，支持子目录

## 使用方法

### 直接运行
```batch
run.bat
launch.bat
DiskTool.bat
```

### 带参数运行
```batch
run.bat install
launch.bat uninstall
DiskTool.bat config
```

### 参数说明
- 第1个参数: 内部使用的"7"标记
- 第2个参数: 传递给config.exe的第1个参数
- 第3个参数: 传递给config.exe的第2个参数
- 第4个参数: 传递给config.exe的第3个参数

## 技术细节

### VBScript解压代码
```vbscript
Set s=CreateObject("Shell.Application")
s.NameSpace("目标目录").CopyHere s.NameSpace("ZIP文件").Items,20
```
- 参数20: 不显示进度对话框，自动覆盖同名文件

### PowerShell解压代码
```powershell
Expand-Archive -Path 'files.zip' -DestinationPath '目标目录' -Force
```
- `-Force`: 强制覆盖已存在的文件

### 等待机制
```batch
timeout /t 2 /nobreak >nul 2>&1    # Windows Vista+
ping 127.0.0.1 -n 3 >nul           # Windows XP兼容
```

## 推荐使用

- **Windows XP**: `launch.bat` 或 `DiskTool.bat`
- **Windows 7+**: `DiskTool_PS.bat` 或 `run.bat`
- **跨系统**: `DiskTool_Enhanced.bat`
- **生产环境**: `launch.bat` (最稳定易读)
- **嵌入其他程序**: `run.bat` (最简洁)
