@echo off
chcp 65001 >nul
echo ========================================
echo Testing DBV Initialization Compilation Fix
echo ========================================

echo.
echo 这个测试验证修复 DEV_BROADCAST_VOLUME 初始化编译错误的结果
echo.
echo 编译错误修复说明:
echo 错误: C4533: initialization of 'dbv' is skipped by 'goto cleanup'
echo 原因: 复杂的结构体初始化被 goto 跳过
echo 修复: 改为简单声明 + 手动初始化
echo.

echo 修复前的问题:
echo   DEV_BROADCAST_VOLUME dbv = { sizeof(dbv), DBT_DEVTYP_VOLUME };
echo   // 这种复杂初始化被 goto 跳过
echo.

echo 修复后的解决方案:
echo   DEV_BROADCAST_VOLUME dbv;  // 简单声明
echo   
echo   // 手动初始化
echo   memset(&dbv, 0, sizeof(dbv));
echo   dbv.dbcv_size = sizeof(dbv);
echo   dbv.dbcv_devicetype = DBT_DEVTYP_VOLUME;
echo.

echo 启动 VirtualDiskTool32.exe 测试修复结果...
echo ----------------------------------------

VirtualDiskTool32.exe --test-unmount

echo.
echo ----------------------------------------
echo 程序执行完毕，退出码: %ERRORLEVEL%
echo.

if %ERRORLEVEL% EQU 0 (
    echo ✅ SUCCESS: DBV 初始化编译错误已修复，程序正常运行
) else (
    echo ❌ FAILED: 程序运行出错，退出码: %ERRORLEVEL%
)

echo.
echo 检查目标 X: 驱动器...
if exist X:\ (
    echo ❌ FAILED: X: 驱动器仍然存在 (卸载失败)
    echo 目录列表:
    dir X: /w
) else (
    echo ✅ SUCCESS: X: 驱动器已卸载
)

echo.
echo ========================================
echo DBV 初始化修复技术说明:
echo ========================================
echo.
echo ✅ 修复前的复杂初始化:
echo   DEV_BROADCAST_VOLUME dbv = { sizeof(dbv), DBT_DEVTYP_VOLUME };
echo   问题: 这种聚合初始化被 C++ 编译器认为是复杂初始化
echo   结果: goto 语句不能跳过这种初始化
echo.
echo ✅ 修复后的简单初始化:
echo   1. 简单声明: DEV_BROADCAST_VOLUME dbv;
echo   2. 清零内存: memset(&dbv, 0, sizeof(dbv));
echo   3. 设置大小: dbv.dbcv_size = sizeof(dbv);
echo   4. 设置类型: dbv.dbcv_devicetype = DBT_DEVTYP_VOLUME;
echo.
echo ✅ C++ goto 和初始化规则:
echo   - 简单声明: 可以被 goto 跳过
echo   - 复杂初始化: 不能被 goto 跳过
echo   - 聚合初始化 { ... }: 被认为是复杂初始化
echo   - 赋值操作: 可以在 goto 之后进行
echo.
echo ✅ DEV_BROADCAST_VOLUME 结构说明:
echo   typedef struct _DEV_BROADCAST_VOLUME {
echo       DWORD dbcv_size;        // 结构大小
echo       DWORD dbcv_devicetype;  // 设备类型 (DBT_DEVTYP_VOLUME)
echo       DWORD dbcv_reserved;    // 保留字段
echo       DWORD dbcv_unitmask;    // 驱动器位掩码
echo       WORD  dbcv_flags;       // 标志
echo   } DEV_BROADCAST_VOLUME;
echo.
echo ✅ 使用示例:
echo   // 设置驱动器位掩码 (A=bit0, B=bit1, ..., X=bit23)
echo   dbv.dbcv_unitmask = 1 << (mount_point[0] - L'A');
echo   
echo   // 发送设备移除通知
echo   SendMessageTimeout(HWND_BROADCAST, WM_DEVICECHANGE, 
echo                     DBT_DEVICEREMOVEPENDING, (LPARAM)&dbv, 
echo                     SMTO_BLOCK | SMTO_ABORTIFHUNG, 4000, &dwp);
echo.

pause
