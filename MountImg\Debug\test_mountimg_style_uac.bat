@echo off
chcp 65001 >nul
echo ========================================
echo Testing MountImg.c Style UAC Implementation
echo ========================================

echo.
echo 这个测试验证 VirtualDiskTool 参照 MountImg.c 实现的权限提升功能
echo.
echo MountImg.c 原始代码:
echo if ((os_ver.dwMajorVersion >= 6) && (argc <= 1 || wcscmp(argv[1], L"/UAC"))) {
echo     // send non-elevated drive list to the elevated process
echo     _snwprintf(txt, _countof(txt) - 1, L"/UAC %d %s", GetLogicalDrives(), cmdline_ptr);
echo     ShellExecute(NULL, L"runas", argv[0], txt, NULL, SW_SHOWDEFAULT);
echo     ExitProcess(0);
echo }
echo.

echo VirtualDiskTool 实现特点:
echo 1. 完全参照 MountImg.c 的权限检查逻辑
echo 2. 使用相同的 UAC 参数格式: /UAC <logical_drives> <cmdline>
echo 3. 相同的 ShellExecute "runas" 调用方式
echo 4. 相同的 ExitProcess(0) 退出机制
echo 5. 支持 DEBUG_InCMD 调试模式
echo.

echo 权限提升流程:
echo 1. 检查操作系统版本 (dwMajorVersion >= 6)
echo 2. 检查命令行参数 (argc <= 1 或 argv[1] != "/UAC")
echo 3. 获取逻辑驱动器掩码 (GetLogicalDrives)
echo 4. 构建 UAC 参数字符串
echo 5. 调用 ShellExecute 请求权限提升
echo 6. 退出当前进程 (ExitProcess)
echo.

echo 检查当前系统信息...
echo ----------------------------------------

ver
echo.

echo 检查当前用户权限...
net session >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo ✅ 当前以管理员权限运行
    echo   VirtualDiskTool 应该检测到已有权限，直接执行
) else (
    echo ❌ 当前未以管理员权限运行
    echo   VirtualDiskTool 应该请求权限提升 (如果是 Vista+)
)

echo.
echo 启动 VirtualDiskTool32.exe 测试 MountImg.c 风格权限提升...
echo ----------------------------------------

echo 注意: 
echo - Debug 模式: 只显示权限提升信息，不实际执行
echo - Release 模式: 会弹出 UAC 对话框请求权限提升
echo.

VirtualDiskTool32.exe --test-mount

echo.
echo ----------------------------------------
echo 程序执行完毕，退出码: %ERRORLEVEL%
echo.

if %ERRORLEVEL% EQU 0 (
    echo ✅ SUCCESS: MountImg.c 风格权限提升功能正常工作
) else (
    echo ❌ FAILED: 程序运行出错，退出码: %ERRORLEVEL%
    echo.
    echo 可能的问题:
    echo 1. 用户拒绝了 UAC 权限提升请求
    echo 2. 系统不支持 UAC (Windows XP)
    echo 3. 程序编译或链接错误
    echo 4. 权限提升逻辑实现问题
)

echo.
echo 检查目标 X: 驱动器...
if exist X:\ (
    echo ✅ SUCCESS: X: 驱动器已挂载
    echo 目录列表:
    dir X: /w
) else (
    echo ❌ FAILED: X: 驱动器未挂载
)

echo.
echo ========================================
echo MountImg.c 风格权限提升实现技术说明:
echo ========================================
echo.
echo ✅ 权限检查逻辑 (完全参照 MountImg.c):
echo   // 获取操作系统版本
echo   OSVERSIONINFO os_ver;
echo   GetVersionEx(&os_ver);
echo   
echo   // 权限检查条件
echo   if ((os_ver.dwMajorVersion >= 6) && (argc <= 1 || strcmp(argv[1], "/UAC") != 0)) {
echo       // Windows Vista+ 且不是已提升的进程
echo       RequestAdministratorPrivileges(argc, argv, cmdline_ptr);
echo   }
echo.
echo ✅ UAC 参数格式 (完全参照 MountImg.c):
echo   格式: /UAC <logical_drives> <cmdline_ptr>
echo   示例: /UAC 67108863 --test-mount
echo   
echo   logical_drives: GetLogicalDrives() 返回的驱动器掩码
echo   cmdline_ptr: 原始命令行参数字符串
echo.
echo ✅ ShellExecute 调用 (完全参照 MountImg.c):
echo   ShellExecuteW(NULL, L"runas", exePath, txt, NULL, SW_SHOWDEFAULT);
echo   
echo   参数说明:
echo   - hwnd: NULL (无父窗口)
echo   - lpVerb: L"runas" (请求管理员权限)
echo   - lpFile: exePath (当前可执行文件路径)
echo   - lpParameters: txt (UAC 参数字符串)
echo   - lpDirectory: NULL (当前目录)
echo   - nShowCmd: SW_SHOWDEFAULT (默认显示方式)
echo.
echo ✅ 调试模式支持 (参照 MountImg.c):
echo   #ifdef DEBUG_InCMD
echo       // 调试模式: 只显示信息，不实际执行权限提升
echo       printf("DEBUG: Would execute ShellExecute with runas\n");
echo       return 0;
echo   #else
echo       // 正常模式: 实际执行权限提升
echo       ShellExecuteW(...);
echo       ExitProcess(0);
echo   #endif
echo.
echo ✅ 提升后进程处理:
echo   if (argc > 1 && strcmp(argv[1], "/UAC") == 0) {
echo       // 这是提升权限后的进程
echo       // argv[2] = logical_drives
echo       // argv[3] = original_cmdline
echo   }
echo.
echo ✅ 错误处理:
echo   - ShellExecute 返回值检查 (> 32 表示成功)
echo   - GetModuleFileName 失败处理
echo   - 操作系统版本获取失败处理
echo   - 命令行参数解析错误处理
echo.
echo ✅ 兼容性特点:
echo   - Windows XP: 不需要 UAC，直接执行
echo   - Windows Vista+: 自动检测和请求 UAC
echo   - 32位/64位: 通用实现
echo   - 调试/发布: 支持不同编译模式
echo.

pause
