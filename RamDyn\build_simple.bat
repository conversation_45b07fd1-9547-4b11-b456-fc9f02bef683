@echo off
echo Simple build for Ram<PERSON>yn...

rem Set Visual Studio environment
call "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat"

echo.
echo Compiling C file...
cl /c /O2 /W3 /D_CRT_SECURE_NO_WARNINGS /DWIN32_LEAN_AND_MEAN /I"..\inc" RamDyn.c

if %ERRORLEVEL% NEQ 0 (
    echo C compilation failed!
    pause
    exit /b 1
)

echo.
echo Compiling resource file...
rc /I"..\inc" resource.rc

if %ERRORLEVEL% NEQ 0 (
    echo Resource compilation failed!
    pause
    exit /b 1
)

echo.
echo Linking executable...
link RamDyn.obj resource.res /OUT:RamDyn64.exe /SUBSYSTEM:WINDOWS /ENTRY:wWinMain kernel32.lib user32.lib advapi32.lib wtsapi32.lib

if %ERRORLEVEL% EQU 0 (
    echo Build successful! Generated RamDyn64.exe
    dir RamDyn64.exe
) else (
    echo Link failed with error code: %ERRORLEVEL%
)

pause
