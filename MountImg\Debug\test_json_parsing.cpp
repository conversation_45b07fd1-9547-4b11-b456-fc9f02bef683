#include <windows.h>
#include <stdio.h>
#include <string.h>

/*
 * 简单的 JSON 解析测试程序
 * 验证 wWinMain 中的 JSON 参数解析逻辑
 */

typedef struct {
    char file_path[MAX_PATH];
    char drive[8];
    BOOL readonly;
    int partition;
} MOUNT_PARAMS;

BOOL ParseMountParams(const char* jsonInput, MOUNT_PARAMS* params)
{
    if (!jsonInput || !params) return FALSE;

    memset(params, 0, sizeof(MOUNT_PARAMS));

    printf("Parsing JSON: %s\n", jsonInput);

    // 解析 file_path
    const char* filePathStart = strstr(jsonInput, "file_path");
    if (filePathStart) {
        filePathStart = strchr(filePathStart, ':');
        if (filePathStart) {
            filePathStart++;
            while (*filePathStart == ' ' || *filePathStart == '\t') filePathStart++;
            
            if (*filePathStart == '"') {
                filePathStart++;
                const char* filePathEnd = strchr(filePathStart, '"');
                if (filePathEnd) {
                    size_t len = filePathEnd - filePathStart;
                    if (len < sizeof(params->file_path)) {
                        strncpy_s(params->file_path, sizeof(params->file_path), filePathStart, len);
                    }
                }
            }
        }
    }

    // 解析 drive
    const char* driveStart = strstr(jsonInput, "drive");
    if (driveStart) {
        driveStart = strchr(driveStart, ':');
        if (driveStart) {
            driveStart++;
            while (*driveStart == ' ' || *driveStart == '\t') driveStart++;
            
            if (*driveStart == '"') {
                driveStart++;
                const char* driveEnd = strchr(driveStart, '"');
                if (driveEnd) {
                    size_t len = driveEnd - driveStart;
                    if (len < sizeof(params->drive)) {
                        strncpy_s(params->drive, sizeof(params->drive), driveStart, len);
                    }
                }
            }
        }
    }

    // 解析 readonly
    const char* readonlyStart = strstr(jsonInput, "readonly");
    if (readonlyStart) {
        readonlyStart = strchr(readonlyStart, ':');
        if (readonlyStart) {
            readonlyStart++;
            while (*readonlyStart == ' ' || *readonlyStart == '\t') readonlyStart++;
            
            if (strncmp(readonlyStart, "true", 4) == 0) {
                params->readonly = TRUE;
            } else if (strncmp(readonlyStart, "false", 5) == 0) {
                params->readonly = FALSE;
            }
        }
    }

    // 解析 partition
    const char* partitionStart = strstr(jsonInput, "partition");
    if (partitionStart) {
        partitionStart = strchr(partitionStart, ':');
        if (partitionStart) {
            partitionStart++;
            while (*partitionStart == ' ' || *partitionStart == '\t') partitionStart++;
            
            params->partition = atoi(partitionStart);
        }
    }

    return (strlen(params->file_path) > 0);
}

int __stdcall wWinMain(HINSTANCE hinstance, HINSTANCE hPrevInstance, LPWSTR lpCmdLine, int nCmdShow)
{
    char jsonInput[2048];
    MOUNT_PARAMS params;
    WCHAR wJsonInput[2048];

    AllocConsole();
    freopen_s((FILE**)stdout, "CONOUT$", "w", stdout);
    freopen_s((FILE**)stderr, "CONOUT$", "w", stderr);
    SetConsoleOutputCP(CP_UTF8);

    printf("========================================\n");
    printf("JSON Parsing Test Program\n");
    printf("========================================\n");
    printf("Command line: %S\n\n", lpCmdLine ? lpCmdLine : L"(empty)");

    if (!lpCmdLine || wcslen(lpCmdLine) == 0) {
        printf("ERROR: No command line arguments\n");
        printf("Usage: test_json_parsing.exe /JSON \"{...}\"\n");
        system("pause");
        return 1;
    }

    // 查找 /JSON 参数
    WCHAR* jsonStart = wcsstr(lpCmdLine, L"/JSON");
    if (!jsonStart) {
        printf("ERROR: /JSON parameter not found\n");
        system("pause");
        return 1;
    }

    // 跳过 "/JSON" 和空格
    jsonStart += 5;
    while (*jsonStart == L' ' || *jsonStart == L'\t') jsonStart++;

    // 提取 JSON 字符串
    if (*jsonStart == L'"') {
        jsonStart++;
        WCHAR* jsonEnd = wcschr(jsonStart, L'"');
        if (jsonEnd) {
            wcsncpy_s(wJsonInput, sizeof(wJsonInput)/sizeof(WCHAR), jsonStart, jsonEnd - jsonStart);
        }
    } else {
        wcscpy_s(wJsonInput, sizeof(wJsonInput)/sizeof(WCHAR), jsonStart);
    }

    // 转换为 ANSI
    WideCharToMultiByte(CP_UTF8, 0, wJsonInput, -1, jsonInput, sizeof(jsonInput), NULL, NULL);
    
    printf("Extracted JSON: %s\n\n", jsonInput);

    // 解析参数
    if (ParseMountParams(jsonInput, &params)) {
        printf("✅ Parsing successful!\n");
        printf("  file_path: %s\n", params.file_path);
        printf("  drive: %s\n", params.drive);
        printf("  readonly: %s\n", params.readonly ? "true" : "false");
        printf("  partition: %d\n", params.partition);
    } else {
        printf("❌ Parsing failed!\n");
    }

    printf("\nPress any key to continue...\n");
    system("pause");
    return 0;
}
