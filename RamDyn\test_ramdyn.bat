@echo off
echo Testing RamDyn functionality...

echo.
echo 1. Checking if RamDyn64.exe exists...
if not exist "RamDyn64.exe" (
    echo ERROR: RamDyn64.exe not found!
    pause
    exit /b 1
)

echo RamDyn64.exe found.

echo.
echo 2. Displaying Ram<PERSON>yn help information...
RamDyn64.exe

echo.
echo 3. Testing basic functionality (this will show syntax help)...
echo Note: <PERSON><PERSON>yn requires specific parameters to run properly.
echo This test just verifies the executable can start without crashing.

echo.
echo 4. Checking file properties...
dir RamDyn64.exe

echo.
echo Test completed. RamDyn64.exe appears to be functional.
echo For actual RAM disk creation, use proper parameters as shown in the help.

pause
