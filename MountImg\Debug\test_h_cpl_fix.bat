@echo off
chcp 65001 >nul
echo ========================================
echo Testing h_cpl Variable Fix
echo ========================================

echo.
echo 这个测试验证 h_cpl 变量未声明错误的修复
echo.
echo 编译错误:
echo error C2065: 'h_cpl' : undeclared identifier
echo.
echo 问题原因:
echo h_cpl 原本是 wWinMain 函数中的局部变量
echo InitializeImDisk 函数无法访问局部变量
echo 导致编译时找不到 h_cpl 的声明
echo.

echo 修复方案:
echo 1. 将 h_cpl 声明为全局变量
echo 2. 移除 wWinMain 中的局部声明
echo 3. 在 MountImg.h 中添加外部声明
echo 4. 确保所有函数都能访问 h_cpl
echo.

echo 修复的关键代码:
echo ✅ 全局变量声明 (MountImg.c 第58行):
echo   HMODULE h_cpl = NULL;
echo.
echo ✅ 外部声明 (MountImg.h 第35行):
echo   extern HMODULE h_cpl;
echo.
echo ✅ 移除局部声明 (wWinMain 函数):
echo   // HMODULE h_cpl; // 已移除
echo.

echo 启动 VirtualDiskTool32.exe 测试编译修复...
echo ----------------------------------------

VirtualDiskTool32.exe --test-mount

echo.
echo ----------------------------------------
echo 程序执行完毕，退出码: %ERRORLEVEL%
echo.

if %ERRORLEVEL% EQU 0 (
    echo ✅ SUCCESS: h_cpl 变量问题已修复，编译成功
) else (
    echo ❌ FAILED: 程序运行出错，退出码: %ERRORLEVEL%
)

echo.
echo 检查目标 X: 驱动器...
if exist X:\ (
    echo ✅ SUCCESS: X: 驱动器已挂载 (h_cpl 修复成功)
    echo 目录列表:
    dir X: /w
) else (
    echo ❌ FAILED: X: 驱动器未挂载
)

echo.
echo ========================================
echo h_cpl 变量修复技术说明:
echo ========================================
echo.
echo ✅ 问题分析:
echo   原始代码中 h_cpl 是 wWinMain 函数的局部变量
echo   新增的 InitializeImDisk 函数需要使用 h_cpl
echo   局部变量的作用域限制导致编译错误
echo.
echo ✅ 修复实现:
echo   // 全局变量声明 (MountImg.c)
echo   FARPROC ImDisk_GetDeviceListEx, ...;
echo   HMODULE h_cpl = NULL;  // 新增全局变量
echo   
echo   // 外部声明 (MountImg.h)
echo   extern HANDLE mount_mutex;
echo   extern HMODULE h_cpl;  // 新增外部声明
echo   
echo   // wWinMain 函数修改
echo   int argc;
echo   LPWSTR *argv;
echo   WCHAR *cmdline_ptr, *exe_path, *opt;
echo   // HMODULE h_cpl;  // 移除局部声明
echo   HWND hwnd;
echo.
echo ✅ h_cpl 使用位置:
echo   1. InitializeImDisk() - 第765行: 加载 imdisk.cpl
echo   2. wWinMain() /NOTIF处理 - 第1536行: 临时加载
echo   3. wWinMain() 主初始化 - 第2066行: 主要加载
echo.
echo ✅ 优势:
echo   - 统一资源管理: 所有函数共享同一个库句柄
echo   - 避免重复加载: 不会多次加载 imdisk.cpl
echo   - 作用域清晰: 全局变量明确表示库的全局性质
echo   - 错误处理改进: 可以在任何函数中检查 h_cpl 有效性
echo   - 兼容性保持: 不影响原有代码功能
echo.
echo ✅ 资源管理:
echo   - 初始化: InitializeImDisk() 中加载库
echo   - 使用: 各个函数通过全局变量访问
echo   - 清理: 程序退出时自动释放
echo   - 同步: 通过 mount_mutex 提供基本保护
echo.

pause
