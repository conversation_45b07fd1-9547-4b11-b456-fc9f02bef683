@echo off
echo ImDisk Command Test Script
echo =========================

echo.
echo 1. Checking ImDisk installation...
where imdisk.exe
if %errorlevel% neq 0 (
    echo ERROR: imdisk.exe not found in PATH
    echo Checking common locations...
    if exist "C:\Windows\System32\imdisk.exe" (
        echo FOUND: C:\Windows\System32\imdisk.exe
        set IMDISK_PATH=C:\Windows\System32\imdisk.exe
    ) else if exist "C:\Program Files\ImDisk\imdisk.exe" (
        echo FOUND: C:\Program Files\ImDisk\imdisk.exe
        set IMDISK_PATH="C:\Program Files\ImDisk\imdisk.exe"
    ) else (
        echo ERROR: ImDisk not found
        pause
        exit /b 1
    )
) else (
    set IMDISK_PATH=imdisk.exe
)

echo.
echo 2. Testing ImDisk basic functionality...
%IMDISK_PATH% -l
if %errorlevel% neq 0 (
    echo ERROR: ImDisk list command failed
    pause
    exit /b 1
)

echo.
echo 3. Testing file existence...
set TEST_FILE=E:\2G.vmdk
if exist "%TEST_FILE%" (
    echo FOUND: %TEST_FILE%
    dir "%TEST_FILE%"
) else (
    echo NOT FOUND: %TEST_FILE%
    echo Please ensure the test file exists
    pause
    exit /b 1
)

echo.
echo 4. Testing mount command (dry run)...
set MOUNT_CMD=%IMDISK_PATH% -a -m "X:" -f "%TEST_FILE%" -o ro
echo Command: %MOUNT_CMD%
echo.
echo Press any key to execute the mount command...
pause

echo.
echo 5. Executing mount command...
%MOUNT_CMD%
set MOUNT_RESULT=%errorlevel%
echo Mount result: %MOUNT_RESULT%

if %MOUNT_RESULT% equ 0 (
    echo SUCCESS: Mount operation completed
    echo.
    echo 6. Checking mounted drive...
    if exist "X:\" (
        echo SUCCESS: Drive X: is accessible
        dir X:\
        echo.
        echo 7. Unmounting...
        %IMDISK_PATH% -d -m "X:"
        echo Unmount result: %errorlevel%
    ) else (
        echo WARNING: Drive X: not accessible
    )
) else (
    echo ERROR: Mount operation failed with code %MOUNT_RESULT%
    echo.
    echo Common error codes:
    echo   1 = General error
    echo   2 = File not found or access denied
    echo   3 = Invalid parameters
    echo   87 = Invalid parameter format
)

echo.
echo 8. Final ImDisk status...
%IMDISK_PATH% -l

echo.
echo Test completed.
pause
