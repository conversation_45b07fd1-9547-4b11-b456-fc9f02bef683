#include <windows.h>
#include <commctrl.h>
#include <afxres.h>
#include "resource.h"

// 1 RT_MANIFEST "manifest"  // 已注释以避免清单冲突

1 ICON "..\\VD.ico"

MOUNT_DLG DIALOGEX 0, 0, 230, 250
		  STYLE WS_SYSMENU | DS_SHELLFONT | DS_CENTER | WS_DISABLED
		  EXSTYLE WS_EX_ACCEPTFILES
		  FONT 9, "MS Shell Dlg"
		  CAPTION "ImDisk"
BEGIN
	GROUPBOX " Mount New Virtual Disk ", ID_TITLE, 6, 6, 218, 219
	LTEXT "Image File:", ID_TEXT1, 18, 23, 204, 10
	PUSHBUTTON "Unmount", ID_PBUTTON3, 172, 18, 48, 13
	COMBOBOX ID_COMBO1, 12, 34, 190, 24, CBS_DROPDOWN | CBS_AUTOHSCROLL | CBS_SORT | WS_TABSTOP
	PUSHBUTTON "...", ID_PBUTTON1, 204, 34, 16, 12
	AUTORA<PERSON>OB<PERSON><PERSON><PERSON> "Drive Letter", ID_RB1, 18, 59, 86, 10, WS_<PERSON><PERSON>UP
	AUTORADIOBUTTON "Mount Point", ID_RB2, 105, 59, 86, 10
	COMBOBOX ID_COMBO2, 28, 72, 30, 300, CBS_DROPDOWNLIST | WS_TABSTOP
	EDITTEXT ID_EDIT1, 18, 72, 178, 12, ES_AUTOHSCROLL
	PUSHBUTTON "...", ID_PBUTTON2, 198, 72, 16, 12
	AUTOCHECKBOX "Read-only", ID_CHECK1, 28, 98, 80, 10
	AUTOCHECKBOX "Removable", ID_CHECK2, 28, 113, 80, 10
	LTEXT "Device Type:", ID_TEXT2, 130, 92, 72, 43
	AUTORADIOBUTTON "Hard Disk", ID_RB3, 130, 103, 60, 10, WS_GROUP
	AUTORADIOBUTTON "CD/DVD", ID_RB4, 130, 113, 60, 10
	AUTORADIOBUTTON "Floppy", ID_RB5, 130, 123, 60, 10
	LTEXT "Partition:", ID_TEXT3, 18, 140, 200, 65
	EDITTEXT ID_UPDOWN, 60, 139, 29, 12, ES_NUMBER
	CONTROL "", ID_LISTVIEW, WC_LISTVIEW, LVS_REPORT | LVS_SINGLESEL | LVS_SHOWSELALWAYS | WS_BORDER | WS_TABSTOP, 18, 153, 194, 51
	AUTOCHECKBOX "Mount at Windows Startup", ID_CHECK3, 28, 209, 150, 10
	PUSHBUTTON "Switch to Driver Interface", ID_PBUTTON4, 5, 231, 100, 13
	DEFPUSHBUTTON "OK", IDOK, 136, 231, 43, 13
	PUSHBUTTON "Cancel", IDCANCEL, 183, 231, 43, 13
END

INVALID_FS DIALOGEX 0, 0, 170, 83
		   STYLE DS_SHELLFONT | DS_CENTER
		   FONT 9, "MS Shell Dlg"
		   CAPTION "Invalid File System"
BEGIN
	LTEXT "The volume does not contain a valid file system.\n\nWhat do you want to do?", ID_TEXT1, 44, 12, 120, 41
	DEFPUSHBUTTON "Unmount", ID_PBUTTON11, 14, 64, 43, 13
	PUSHBUTTON "Format", ID_PBUTTON12, 64, 64, 43, 13
	PUSHBUTTON "Exit", ID_PBUTTON13, 114, 64, 43, 13
	GROUPBOX "", IDC_STATIC, -5, 53, 180, 40
END

CREATE_FILE DIALOGEX 0, 0, 210, 126
			STYLE DS_SHELLFONT | DS_CENTER
			FONT 9, "MS Shell Dlg"
			CAPTION "Create New Image File"
BEGIN
	LTEXT "The file does not exist.\nDo you want to create a new RAW image file?", ID_TEXT1, 42, 12, 168, 30
	LTEXT "File Size:\n(Min 65KB)", ID_TEXT2, 20, 49, 64, 26
	EDITTEXT ID_EDIT21, 84, 51, 68, 12, ES_NUMBER
	AUTORADIOBUTTON "Bytes", ID_RB21, 156, 42, 52, 8
	AUTORADIOBUTTON "KB", ID_RB22, 156, 50, 52, 8
	AUTORADIOBUTTON "MB", ID_RB23, 156, 58, 52, 8
	AUTORADIOBUTTON "GB", ID_RB24, 156, 66, 52, 8
	AUTOCHECKBOX "Mount", ID_CHECK21, 84, 76, 68, 10
	DEFPUSHBUTTON "Create", IDOK, 116, 107, 43, 13
	PUSHBUTTON "Cancel", IDCANCEL, 163, 107, 43, 13
	GROUPBOX "", IDC_STATIC, -5, 96, 220, 40
END


1 VERSIONINFO
FILEVERSION 4,1,0,0
BEGIN
BLOCK "StringFileInfo"
	BEGIN
	BLOCK "040904b0"
		BEGIN
			VALUE "FileDescription", "Mount new virtual disk"
			VALUE "ProductName", "imdisk"
			VALUE "ProductVersion", "*******"
		END
	END
BLOCK "VarFileInfo"
	BEGIN
		VALUE "Translation", 0x0409, 0x04B0
	END
END
