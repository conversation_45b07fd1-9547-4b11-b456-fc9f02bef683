﻿C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\MSBuild\Microsoft\VC\v150\Platforms\Win32\PlatformToolsets\v141_xp\Toolset.targets(39,5): warning MSB8051: 面向 Windows XP 的支持已被弃用，将来的 Visual Studio 版本不再提供该支持。请访问 https://go.microsoft.com/fwlink/?linkid=2023588，获取详细信息。
  RamDiskUI.c
E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\RamDiskUI\RamDiskUI.c(432): error C4996: 'wcscpy': This function or variable may be unsafe. Consider using wcscpy_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\RamDiskUI\RamDiskUI.c(518): error C4996: 'wcscpy': This function or variable may be unsafe. Consider using wcscpy_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\RamDiskUI\RamDiskUI.c(522): error C4996: 'wcscpy': This function or variable may be unsafe. Consider using wcscpy_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\RamDiskUI\RamDiskUI.c(536): error C4996: 'wcscpy': This function or variable may be unsafe. Consider using wcscpy_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\RamDiskUI\RamDiskUI.c(605): error C4996: 'wcscpy': This function or variable may be unsafe. Consider using wcscpy_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\RamDiskUI\RamDiskUI.c(621): error C4996: '_snwprintf': This function or variable may be unsafe. Consider using _snwprintf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\RamDiskUI\RamDiskUI.c(624): error C4996: 'wcscpy': This function or variable may be unsafe. Consider using wcscpy_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\RamDiskUI\RamDiskUI.c(638): error C4996: '_snwprintf': This function or variable may be unsafe. Consider using _snwprintf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\RamDiskUI\RamDiskUI.c(649): error C4996: '_snwprintf': This function or variable may be unsafe. Consider using _snwprintf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\RamDiskUI\RamDiskUI.c(767): error C4996: '_snwprintf': This function or variable may be unsafe. Consider using _snwprintf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\RamDiskUI\RamDiskUI.c(1175): warning C4244: “=”: 从“DWORD”转换到“WCHAR”，可能丢失数据
E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\RamDiskUI\RamDiskUI.c(1004): error C4996: '_snwprintf': This function or variable may be unsafe. Consider using _snwprintf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\RamDiskUI\RamDiskUI.c(1012): error C4996: '_snwprintf': This function or variable may be unsafe. Consider using _snwprintf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\RamDiskUI\RamDiskUI.c(1037): error C4996: '_snwprintf': This function or variable may be unsafe. Consider using _snwprintf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\RamDiskUI\RamDiskUI.c(1047): error C4996: '_snwprintf': This function or variable may be unsafe. Consider using _snwprintf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\RamDiskUI\RamDiskUI.c(1067): error C4996: '_snwprintf': This function or variable may be unsafe. Consider using _snwprintf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\RamDiskUI\RamDiskUI.c(1068): error C4996: '_snwprintf': This function or variable may be unsafe. Consider using _snwprintf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\RamDiskUI\RamDiskUI.c(1069): error C4996: '_snwprintf': This function or variable may be unsafe. Consider using _snwprintf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\RamDiskUI\RamDiskUI.c(1070): error C4996: '_snwprintf': This function or variable may be unsafe. Consider using _snwprintf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\RamDiskUI\RamDiskUI.c(1071): error C4996: '_snwprintf': This function or variable may be unsafe. Consider using _snwprintf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\RamDiskUI\RamDiskUI.c(1081): error C4996: '_snwprintf': This function or variable may be unsafe. Consider using _snwprintf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\RamDiskUI\RamDiskUI.c(1084): error C4996: '_snwprintf': This function or variable may be unsafe. Consider using _snwprintf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\RamDiskUI\RamDiskUI.c(1092): error C4996: 'wcscpy': This function or variable may be unsafe. Consider using wcscpy_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\RamDiskUI\RamDiskUI.c(1113): error C4996: '_snwprintf': This function or variable may be unsafe. Consider using _snwprintf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\RamDiskUI\RamDiskUI.c(1115): error C4996: 'wcscpy': This function or variable may be unsafe. Consider using wcscpy_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\RamDiskUI\RamDiskUI.c(1128): error C4996: '_snwprintf': This function or variable may be unsafe. Consider using _snwprintf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\RamDiskUI\RamDiskUI.c(1144): error C4996: '_snwprintf': This function or variable may be unsafe. Consider using _snwprintf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\RamDiskUI\RamDiskUI.c(1152): error C4996: '_snwprintf': This function or variable may be unsafe. Consider using _snwprintf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\RamDiskUI\RamDiskUI.c(1153): error C4996: 'wcscat': This function or variable may be unsafe. Consider using wcscat_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\RamDiskUI\RamDiskUI.c(1154): error C4996: 'wcscat': This function or variable may be unsafe. Consider using wcscat_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\RamDiskUI\RamDiskUI.c(1157): error C4996: '_snwprintf': This function or variable may be unsafe. Consider using _snwprintf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\RamDiskUI\RamDiskUI.c(1158): error C4996: 'wcscat': This function or variable may be unsafe. Consider using wcscat_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\RamDiskUI\RamDiskUI.c(1221): error C4996: 'wcscpy': This function or variable may be unsafe. Consider using wcscpy_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\RamDiskUI\RamDiskUI.c(1383): warning C4244: “=”: 从“LRESULT”转换到“unsigned char”，可能丢失数据
E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\RamDiskUI\RamDiskUI.c(1303): error C4996: 'wcscpy': This function or variable may be unsafe. Consider using wcscpy_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\RamDiskUI\RamDiskUI.c(1342): error C4996: '_snwprintf': This function or variable may be unsafe. Consider using _snwprintf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\RamDiskUI\RamDiskUI.c(1346): error C4996: '_snwprintf': This function or variable may be unsafe. Consider using _snwprintf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\RamDiskUI\RamDiskUI.c(1382): error C4996: 'wcscpy': This function or variable may be unsafe. Consider using wcscpy_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\RamDiskUI\RamDiskUI.c(1477): error C4996: '_snwprintf': This function or variable may be unsafe. Consider using _snwprintf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\RamDiskUI\RamDiskUI.c(1567): error C4996: 'wcscat': This function or variable may be unsafe. Consider using wcscat_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\RamDiskUI\RamDiskUI.c(1568): error C4996: '_snwprintf': This function or variable may be unsafe. Consider using _snwprintf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\RamDiskUI\RamDiskUI.c(1870): warning C4244: “=”: 从“DWORD”转换到“WCHAR”，可能丢失数据
E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\RamDiskUI\RamDiskUI.c(1767): error C4996: '_snwprintf': This function or variable may be unsafe. Consider using _snwprintf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\RamDiskUI\RamDiskUI.c(1793): error C4996: 'wcscpy': This function or variable may be unsafe. Consider using wcscpy_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\RamDiskUI\RamDiskUI.c(1806): error C4996: 'wcscat': This function or variable may be unsafe. Consider using wcscat_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\RamDiskUI\RamDiskUI.c(1818): error C4996: 'wcscpy': This function or variable may be unsafe. Consider using wcscpy_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\RamDiskUI\RamDiskUI.c(1828): error C4996: 'wcscpy': This function or variable may be unsafe. Consider using wcscpy_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\RamDiskUI\RamDiskUI.c(1831): error C4996: 'wcscpy': This function or variable may be unsafe. Consider using wcscpy_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\RamDiskUI\RamDiskUI.c(1834): error C4996: 'wcscpy': This function or variable may be unsafe. Consider using wcscpy_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\RamDiskUI\RamDiskUI.c(1847): error C4996: 'wcscpy': This function or variable may be unsafe. Consider using wcscpy_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\RamDiskUI\RamDiskUI.c(1855): error C4996: 'wcscpy': This function or variable may be unsafe. Consider using wcscpy_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\RamDiskUI\RamDiskUI.c(1856): error C4996: 'wcscpy': This function or variable may be unsafe. Consider using wcscpy_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\RamDiskUI\RamDiskUI.c(1876): error C4996: '_snwprintf': This function or variable may be unsafe. Consider using _snwprintf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\RamDiskUI\RamDiskUI.c(1877): error C4996: '_snwprintf': This function or variable may be unsafe. Consider using _snwprintf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\RamDiskUI\RamDiskUI.c(1878): error C4996: '_snwprintf': This function or variable may be unsafe. Consider using _snwprintf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\RamDiskUI\RamDiskUI.c(1879): error C4996: '_snwprintf': This function or variable may be unsafe. Consider using _snwprintf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\RamDiskUI\RamDiskUI.c(1880): error C4996: '_snwprintf': This function or variable may be unsafe. Consider using _snwprintf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\RamDiskUI\RamDiskUI.c(1881): error C4996: '_snwprintf': This function or variable may be unsafe. Consider using _snwprintf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\RamDiskUI\RamDiskUI.c(1896): error C4996: '_snwprintf': This function or variable may be unsafe. Consider using _snwprintf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\RamDiskUI\RamDiskUI.c(1900): error C4996: '_snwprintf': This function or variable may be unsafe. Consider using _snwprintf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\RamDiskUI\RamDiskUI.c(1919): error C4996: '_snwprintf': This function or variable may be unsafe. Consider using _snwprintf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\RamDiskUI\RamDiskUI.c(1927): error C4996: 'wcscpy': This function or variable may be unsafe. Consider using wcscpy_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\RamDiskUI\RamDiskUI.c(1940): error C4996: '_snwprintf': This function or variable may be unsafe. Consider using _snwprintf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\RamDiskUI\RamDiskUI.c(1946): error C4996: '_snwprintf': This function or variable may be unsafe. Consider using _snwprintf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\RamDiskUI\RamDiskUI.c(1947): error C4996: 'wcscat': This function or variable may be unsafe. Consider using wcscat_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\RamDiskUI\RamDiskUI.c(1948): error C4996: 'wcscat': This function or variable may be unsafe. Consider using wcscat_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\RamDiskUI\RamDiskUI.c(1951): error C4996: '_snwprintf': This function or variable may be unsafe. Consider using _snwprintf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\RamDiskUI\RamDiskUI.c(1952): error C4996: 'wcscat': This function or variable may be unsafe. Consider using wcscat_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\RamDiskUI\RamDiskUI.c(2004): error C4996: 'wcscpy': This function or variable may be unsafe. Consider using wcscpy_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\RamDiskUI\RamDiskUI.c(2005): error C4996: 'wcscpy': This function or variable may be unsafe. Consider using wcscpy_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\RamDiskUI\RamDiskUI.c(2065): error C4996: 'wcscpy': This function or variable may be unsafe. Consider using wcscpy_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\RamDiskUI\RamDiskUI.c(2075): error C4996: '_snwprintf': This function or variable may be unsafe. Consider using _snwprintf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\RamDiskUI\RamDiskUI.c(2086): error C4996: 'wcscat': This function or variable may be unsafe. Consider using wcscat_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\RamDiskUI\RamDiskUI.c(2090): error C4996: 'wcscat': This function or variable may be unsafe. Consider using wcscat_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
