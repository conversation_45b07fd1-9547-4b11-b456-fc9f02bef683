#pragma once

#include <windows.h>
#include <stdio.h>

// 调试输出宏，同时输出到调试流和控制台
#ifdef _DEBUG
    #define DEBUG_PRINT(msg) do { \
        OutputDebugStringA(msg); \
        printf("%s", msg); \
        fflush(stdout); \
    } while(0)
    
    #define DEBUG_PRINTF(fmt, ...) do { \
        char debug_buffer[1024]; \
        sprintf(debug_buffer, fmt, __VA_ARGS__); \
        OutputDebugStringA(debug_buffer); \
        printf("%s", debug_buffer); \
        fflush(stdout); \
    } while(0)
#else
    #define DEBUG_PRINT(msg) OutputDebugStringA(msg)
    #define DEBUG_PRINTF(fmt, ...) do { \
        char debug_buffer[1024]; \
        sprintf(debug_buffer, fmt, __VA_ARGS__); \
        OutputDebugStringA(debug_buffer); \
    } while(0)
#endif

// 分隔线宏
#define DEBUG_SEPARATOR() DEBUG_PRINT("========================================\n")
#define DEBUG_SECTION(name) do { \
    DEBUG_SEPARATOR(); \
    DEBUG_PRINTF("=== %s ===\n", name); \
} while(0)
