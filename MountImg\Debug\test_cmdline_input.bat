@echo off
chcp 65001 >nul
echo ========================================
echo Testing MountImg.exe Command Line Input Mode
echo ========================================

echo.
echo 这个测试将启动 MountImg.exe 的控制台命令行输入模式
echo 程序会提示您选择输入方式：
echo.
echo 选项说明：
echo 1. 手动输入完整命令行参数
echo    - 可以输入自定义的命令行参数
echo    - 格式: MountImg.exe /JSON "json_string"
echo.
echo 2. 使用默认 JSON 测试命令
echo    - 自动使用预设的测试参数
echo    - 文件: E:\004_VMDK\666666.vmdk
echo    - 驱动器: X:
echo.
echo 3. 使用原始 GetCommandLine()
echo    - 使用系统原始的命令行参数
echo.

echo 启动 MountImg.exe...
echo ----------------------------------------

echo 2 | MountImg.exe

echo.
echo ----------------------------------------
echo 程序执行完毕，退出码: %ERRORLEVEL%
echo.

echo 检查挂载结果...
if exist X:\ (
    echo ✅ SUCCESS: X: 驱动器已挂载
    echo 目录列表:
    dir X: /w
) else (
    echo ❌ FAILED: X: 驱动器未挂载
)

echo.
echo ========================================
echo 控制台命令行输入功能说明:
echo ========================================
echo.
echo 功能特点:
echo 1. 在程序启动时通过控制台输入命令行参数
echo 2. 支持三种输入模式选择
echo 3. 自动处理字符编码转换 (ANSI ↔ Unicode)
echo 4. 提供用户确认机制
echo 5. 支持跳转到原始 GetCommandLine() 模式
echo.
echo 代码实现要点:
echo 1. 使用 static WCHAR cmdline_ptr_Input[2048] 作为输入缓冲区
echo 2. 通过 fgets() 读取用户输入
echo 3. 使用 MultiByteToWideChar() 进行编码转换
echo 4. 通过 goto skip_custom_input 处理不同分支
echo.
echo JSON 命令行格式示例:
echo MountImg.exe /JSON "{\"file_path\":\"E:\\004_VMDK\\666666.vmdk\",\"drive\":\"X:\",\"readonly\":false,\"partition\":1}"
echo.

pause
