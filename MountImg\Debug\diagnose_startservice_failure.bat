@echo off
chcp 65001 >nul
echo ========================================
echo VirtualDiskTool StartService 失败诊断
echo ========================================

echo.
echo 🔍 问题描述：
echo VirtualDiskTool32.exe 在管理员权限下运行时
echo 执行到 StartService(h_svc, 3, (void*)cmdline_ptr) 时服务无法开启
echo.

echo 📋 诊断步骤：
echo 1. 检查系统服务状态
echo 2. 验证服务句柄有效性
echo 3. 分析服务依赖关系
echo 4. 检查服务配置
echo 5. 测试服务启动
echo.

echo ----------------------------------------
echo 步骤1: 检查 ImDisk 相关服务状态
echo ----------------------------------------

echo.
echo 🔍 检查 ImDiskImg 服务 (主要服务):
sc query ImDiskImg >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ ImDiskImg 服务已安装
    sc query ImDiskImg
    echo.
    sc qc ImDiskImg
) else (
    echo ❌ ImDiskImg 服务未安装
    echo 💡 这是 StartService 失败的主要原因
)

echo.
echo 🔍 检查 ImDiskSvc 服务 (DiscUtils服务):
sc query ImDiskSvc >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ ImDiskSvc 服务已安装
    sc query ImDiskSvc
    echo.
    sc qc ImDiskSvc
) else (
    echo ❌ ImDiskSvc 服务未安装
)

echo.
echo 🔍 检查 ImDisk 驱动程序:
sc query ImDisk >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ ImDisk 驱动已安装
    sc query ImDisk
) else (
    echo ❌ ImDisk 驱动未安装
)

echo.
echo ----------------------------------------
echo 步骤2: 分析服务配置问题
echo ----------------------------------------

echo.
echo 🔍 可能的 StartService 失败原因分析:
echo.
echo ❌ 原因1: 服务句柄无效 (h_svc == NULL)
echo   - VirtualDiskTool 中 h_svc 可能未正确初始化
echo   - 需要在调用前确保 OpenService 或 CreateService 成功
echo   - 检查 InitializeVirtualDiskLib() 中的服务初始化代码
echo.
echo ❌ 原因2: 服务不存在 (ERROR_SERVICE_DOES_NOT_EXIST)
echo   - ImDiskImg 服务未安装或已被删除
echo   - 需要重新创建服务
echo   - 服务名称可能不匹配
echo.
echo ❌ 原因3: 权限不足 (ERROR_ACCESS_DENIED)
echo   - 虽然以管理员身份运行，但服务启动权限不足
echo   - 服务配置的用户账户问题
echo   - 服务依赖项未满足
echo.
echo ❌ 原因4: 服务已在运行 (ERROR_SERVICE_ALREADY_RUNNING)
echo   - 服务已经启动，重复启动失败
echo   - 需要检查服务状态后再决定是否启动
echo.
echo ❌ 原因5: 服务被禁用 (ERROR_SERVICE_DISABLED)
echo   - 服务启动类型设置为禁用
echo   - 需要修改服务配置
echo.
echo ❌ 原因6: 服务程序文件问题
echo   - 服务可执行文件不存在或损坏
echo   - 路径配置错误
echo   - 文件权限问题
echo.

echo ----------------------------------------
echo 步骤3: 检查服务依赖和环境
echo ----------------------------------------

echo.
echo 🔍 检查 .NET Framework (DiscUtils 依赖):
reg query "HKLM\SOFTWARE\Microsoft\.NETFramework\v4.0.30319" >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ .NET Framework 4.0+ 已安装
) else (
    echo ❌ .NET Framework 4.0+ 未安装
    echo 💡 DiscUtils 需要 .NET Framework 支持
)

echo.
echo 🔍 检查 ImDisk 核心文件:
if exist "%SystemRoot%\System32\drivers\imdisk.sys" (
    echo ✅ ImDisk 驱动文件存在
) else (
    echo ❌ ImDisk 驱动文件缺失
)

if exist "%SystemRoot%\System32\imdisk.exe" (
    echo ✅ ImDisk 命令行工具存在
) else (
    echo ❌ ImDisk 命令行工具缺失
)

echo.
echo 🔍 检查当前目录中的服务程序:
if exist "VirtualDiskTool32.exe" (
    echo ✅ VirtualDiskTool32.exe 存在
) else (
    echo ❌ VirtualDiskTool32.exe 不存在
)

if exist "MountImg32.exe" (
    echo ✅ MountImg32.exe 存在 (服务程序)
) else (
    echo ❌ MountImg32.exe 不存在 (这可能是问题所在)
)

echo.
echo ----------------------------------------
echo 步骤4: 常见错误码说明
echo ----------------------------------------

echo.
echo 📋 StartService 常见错误码:
echo.
echo 🔢 错误码 5 (ERROR_ACCESS_DENIED):
echo    - 访问被拒绝，权限不足
echo    - 解决方案: 确保以管理员身份运行
echo.
echo 🔢 错误码 6 (ERROR_INVALID_HANDLE):
echo    - 服务句柄无效
echo    - 解决方案: 检查 h_svc 是否正确初始化
echo.
echo 🔢 错误码 87 (ERROR_INVALID_PARAMETER):
echo    - 参数无效
echo    - 解决方案: 检查传递给服务的参数
echo.
echo 🔢 错误码 1056 (ERROR_SERVICE_ALREADY_RUNNING):
echo    - 服务已在运行
echo    - 解决方案: 检查服务状态，避免重复启动
echo.
echo 🔢 错误码 1058 (ERROR_SERVICE_DISABLED):
echo    - 服务被禁用
echo    - 解决方案: 修改服务启动类型
echo.
echo 🔢 错误码 1060 (ERROR_SERVICE_DOES_NOT_EXIST):
echo    - 服务不存在
echo    - 解决方案: 重新安装或创建服务
echo.
echo 🔢 错误码 1069 (ERROR_SERVICE_LOGON_FAILED):
echo    - 服务登录失败
echo    - 解决方案: 检查服务账户配置
echo.

echo ----------------------------------------
echo 步骤5: 建议的解决方案
echo ----------------------------------------

echo.
echo 🛠️  解决方案建议:
echo.
echo ✅ 方案1: 检查服务初始化
echo   1. 确保 VirtualDiskTool 中正确调用了 InitializeVirtualDiskLib()
echo   2. 验证 h_svc 在 StartService 调用前不为 NULL
echo   3. 添加调试输出显示 h_svc 的值
echo.
echo ✅ 方案2: 重新创建 ImDiskImg 服务
echo   1. 删除现有服务: sc delete ImDiskImg
echo   2. 重新运行 MountImg32.exe 让它自动创建服务
echo   3. 验证服务创建成功: sc query ImDiskImg
echo.
echo ✅ 方案3: 检查服务程序路径
echo   1. 确保 MountImg32.exe 存在且可执行
echo   2. 检查服务配置中的程序路径是否正确
echo   3. 验证程序文件权限
echo.
echo ✅ 方案4: 使用 DebugView 查看详细错误
echo   1. 下载并运行 DebugView
echo   2. 运行 VirtualDiskTool32.exe
echo   3. 查看 OutputDebugString 输出的详细错误信息
echo.
echo ✅ 方案5: 手动测试服务启动
echo   1. 尝试手动启动服务: net start ImDiskImg
echo   2. 观察错误信息
echo   3. 检查事件查看器中的系统日志
echo.

echo ========================================
echo StartService 失败诊断完成
echo ========================================

echo.
echo 💡 下一步建议:
echo 1. 运行 DebugView 查看详细错误信息
echo 2. 检查 h_svc 是否为 NULL
echo 3. 验证 ImDiskImg 服务是否正确安装
echo 4. 如有需要，重新安装 ImDisk 组件
echo.

pause
