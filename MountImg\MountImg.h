﻿/*
 * MountImg.h
 * MountImg.c中导出函数的声明
 * 供VirtualDiskLib项目使用
 */

#ifndef MOUNTIMG_H
#define MOUNTIMG_H

#include <windows.h>

#ifdef __cplusplus
extern "C" {
#endif

/*
 * MountImg.c中的全局变量声明
 * 这些变量需要在VirtualDiskLib中访问
 */

// 全局变量（在MountImg.c中定义）
extern WCHAR filename[MAX_PATH];
extern WCHAR drive[MAX_PATH + 2];
extern WCHAR mountdir[MAX_PATH];
extern BYTE net_installed;
extern UINT dev_type;
extern UINT partition;
extern BOOL readonly;
extern BOOL removable;
extern BOOL win_boot;
extern BOOL mount_point;
extern BOOL new_file;
extern long device_number;
extern HANDLE mount_mutex;
extern HMODULE h_cpl;
extern BYTE net_installed;
extern BYTE init_ok;
extern SC_HANDLE h_svc;
//
//// ImDisk API函数指针（在MountImg.c中定义）
//extern FARPROC ImDisk_GetDeviceListEx;
//extern FARPROC ImDisk_ForceRemoveDevice;
//extern FARPROC ImDisk_NotifyShellDriveLetter;
//
//// 设备类型和选项数组（在MountImg.c中定义）
//extern WCHAR dev_list[];
//extern WCHAR ro_list[];
//extern WCHAR *ro_discutils_list[];
//extern WCHAR *rm_list[];
//extern WCHAR *boot_list[];
//
//// wWinMain初始化相关变量（在MountImg.c中定义）
//extern OSVERSIONINFO os_ver;
//extern DWORD show_explorer;
//extern HKEY registry_key;
//extern HANDLE mount_mutex;
//extern PROCESS_INFORMATION pi_discutilsdevio;
//extern HINSTANCE hinst;
//extern SC_HANDLE h_scman_global;

/*
 * MountImg.c中的函数声明
 * 这些函数已从static改为public，供VirtualDiskLib调用
 */

/*
 * 启动进程（完全按照MountImg.c第135-163行）
 * 
 * 参数：
 *   cmd: 命令行
 *   flag: 标志（0=不等待，1=等待，2=用户会话）
 * 
 * 返回值：
 *   进程退出码
 */
DWORD start_process(WCHAR *cmd, BYTE flag);

///*
// * 获取可用的ImDisk设备号（完全按照MountImg.c第175-184行）
// * 
// * 返回值：
// *   可用的设备号，失败返回-1
// */
//long get_imdisk_unit(void);

/*
 * ImDisk挂载（完全按照MountImg.c第688-710行）
 *
 * 参数：
 *   no_check_fs: 是否跳过文件系统检查
 *
 * 返回值：
 *   0: 成功，非0: 失败
 */
int Imdisk_Mount(BYTE no_check_fs);

/*
 * DiscUtils挂载（完全按照MountImg.c第712-732行）
 *
 * 返回值：
 *   0: 成功，非0: 失败
 */
int DiscUtils_Mount(void);

/*
 * 保存注册表配置
 */
void reg_save(void);

/*
 * 初始化 ImDisk 相关资源
 * 必须在调用挂载函数之前调用
 *
 * 返回值：
 *   0: 成功
 *   1: 失败
 */
int InitializeImDisk(void);

///*
// * 初始化MountImg模块
// * 加载ImDisk API和DiscUtils服务
// * 
// * 返回值：
// *   TRUE: 成功，FALSE: 失败
// */
//BOOL InitializeMountImg(void);

///*
// * 基于wWinMain1的完整初始化MountImg模块
// * 完全参考wWinMain1(第1295-1480行)的初始化流程，确保VHDX/VMDK挂载能力
// *
// * 返回值：
// *   TRUE: 成功，FALSE: 失败
// */
//BOOL InitializeMountImg_Complete(void);

///*
// * 清理MountImg模块资源
// * 在DLL卸载时调用，清理所有句柄和资源
// */
//void CleanupMountImgResources(void);

///*
// * 检测当前进程是否以管理员身份运行
// * 返回值：TRUE=管理员权限，FALSE=普通用户权限
// */
//BOOL IsRunAsAdministrator(void);

///*
// * 检查ImDiskImg服务状态
// * 返回值：0=服务运行中，1=服务已停止，2=服务不存在，3=权限不足
// */
//int CheckImDiskImgServiceStatus(void);

///*
// * 检查文件格式是否需要管理员权限
// * 返回值：TRUE=需要管理员权限，FALSE=普通权限即可
// */
//BOOL RequiresAdminPrivileges(const WCHAR* filename);

///*
// * 生成权限提示信息
// * 为需要管理员权限的文件格式提供用户友好的提示
// */
//void GeneratePrivilegeWarning(const WCHAR* filename, char* warning_buffer, size_t buffer_size);

///*
// * 设置挂载参数
// * 供VirtualDiskLib调用以设置全局变量
// *
// * 参数：
// *   imagePath: 镜像文件路径
// *   driveLetter: 目标驱动器号
// *   isReadonly: 是否只读挂载
// *   partitionNum: 分区号
// */
//void SetMountParameters(const WCHAR* imagePath, const WCHAR* driveLetter,
//                       BOOL isReadonly, UINT partitionNum);

///*
// * 增强的卸载函数
// * 基于MountImg.c的UnmountDrive实现，使用ImDisk-Dlg RM命令
// *
// * 参数：
// *   driveLetter: 要卸载的驱动器号（如L"Z:"）
// *
// * 返回：
// *   0: 成功
// *   非0: 失败
// */
//int UnmountDrive_Enhanced(const WCHAR* driveLetter);

///*
// * 简化的卸载函数
// * 直接使用imdisk命令行工具
// *
// * 参数：
// *   driveLetter: 要卸载的驱动器号（如L"Z:"）
// *
// * 返回：
// *   0: 成功
// *   非0: 失败
// */
//int UnmountDrive_Simple(const WCHAR* driveLetter);

///*
// * 完全兼容MountImg.c的挂载函数
// * 基于MountImg.c的Mount函数实现，包含完整的双重挂载策略
// *
// * 前提条件：
// *   必须先调用SetMountParameters设置挂载参数
// *
// * 返回：
// *   0: 成功
// *   非0: 失败
// */
//int Mount_Enhanced(void);

///*
// * 验证挂载结果
// * 基于MountImg.c第694-712行的验证逻辑
// *
// * 参数：
// *   driveLetter: 要验证的驱动器号（如L"Z:"）
// *
// * 返回：
// *   0: 成功
// *   1: 超时失败
// *   2: 无法识别的卷
// */
//int VerifyMountResult(const WCHAR* driveLetter);

///*
// * 完全符合MountImg.c IDOK响应的挂载函数
// * 基于MountImg.c第1051-1095行的完整逻辑
// *
// * 参数：
// *   imagePath: 镜像文件路径
// *   driveLetter: 目标驱动器号或挂载目录
// *   isReadonly: 是否只读挂载
// *   partitionNum: 分区号
// *   useMountPoint: 是否使用目录挂载（FALSE=驱动器号，TRUE=目录）
// *   winBoot: 是否启动时自动挂载
// *
// * 返回：
// *   0: 成功
// *   1: 无效参数
// *   2: 文件不存在
// *   3: 不支持重解析点
// *   4: 目录已经是挂载点
// *   5: 目录不为空
// *   6: 驱动器号已被占用
// *   其他: Mount_Enhanced的返回值
// */
//int MountVirtualDisk_IDOK_Compatible(const WCHAR* imagePath, const WCHAR* driveLetter,
//                                    BOOL isReadonly, UINT partitionNum,
//                                    BOOL useMountPoint, BOOL winBoot);

#ifdef __cplusplus
}
#endif

#endif // MOUNTIMG_H
