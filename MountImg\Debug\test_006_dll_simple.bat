@echo off
echo === VirtualDiskLib 006_Dll Interface Test ===

echo.
echo Testing if VirtualDiskLib32.dll exists...
if exist "VirtualDiskLib32.dll" (
    echo [OK] VirtualDiskLib32.dll found
) else (
    echo [ERROR] VirtualDiskLib32.dll not found
    pause
    exit /b 1
)

echo.
echo Testing if VirtualDiskLib32.lib exists...
if exist "VirtualDiskLib32.lib" (
    echo [OK] VirtualDiskLib32.lib found
) else (
    echo [ERROR] VirtualDiskLib32.lib not found
    pause
    exit /b 1
)

echo.
echo Checking DLL exports using dumpbin...
dumpbin /exports VirtualDiskLib32.dll | findstr /i "GetLibraryInfo InitializeVirtualDiskLib CleanupVirtualDiskLib MountVirtualDisk UnmountVirtualDisk GetMountStatus"

echo.
echo === Test completed ===
pause
