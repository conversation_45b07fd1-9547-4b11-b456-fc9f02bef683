﻿/*
 * mount_core_new2.cpp
 * 直接使用MountImg.c中的函数，不重复实现
 * 
 * 设计原则：
 * 1. 直接调用MountImg.c中的现有函数
 * 2. 不重复实现任何MountImg.c中已有的功能
 * 3. 只提供简单的包装和接口转换
 */

#define _WIN32_WINNT 0x0601
#define OEMRESOURCE
#define _CRT_SECURE_NO_WARNINGS

#include <windows.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <shlwapi.h>

#include "mount_core.h"
#include "../MountImg.h"  // 使用MountImg.c的函数声明

// ===== 简单的包装函数，直接调用MountImg.c =====

// 初始化函数
BOOL InitializeVirtualDiskLib(void)
{
    OutputDebugStringA("🔧 Initializing VirtualDiskLib (using MountImg.c)...\n");
    
    BOOL result = InitializeMountImg();
    
    if (result) {
        OutputDebugStringA("✅ VirtualDiskLib initialized successfully\n");
    } else {
        OutputDebugStringA("❌ VirtualDiskLib initialization failed\n");
    }
    
    return result;
}

// 挂载虚拟磁盘（直接使用MountImg.c的逻辑）
MOUNT_RESULT MountVirtualDisk_Core(const char* imagePath, const char* driveLetter, int isReadonly, int partitionNum)
{
    if (!imagePath || !driveLetter) {
        OutputDebugStringA("❌ Invalid parameters\n");
        return MOUNT_ERROR_INVALID_PARAMS;
    }
    
    OutputDebugStringA("\n🚀 ═══════════════════════════════════════\n");
    OutputDebugStringA("    VirtualDiskLib Mount (using MountImg.c)\n");
    OutputDebugStringA("═══════════════════════════════════════\n");

    // 转换为宽字符
    WCHAR wImagePath[MAX_PATH];
    WCHAR wDriveLetter[8];
    
    MultiByteToWideChar(CP_UTF8, 0, imagePath, -1, wImagePath, _countof(wImagePath));
    MultiByteToWideChar(CP_UTF8, 0, driveLetter, -1, wDriveLetter, _countof(wDriveLetter));
    
    // 检查文件是否存在
    if (GetFileAttributesW(wImagePath) == INVALID_FILE_ATTRIBUTES) {
        OutputDebugStringA("❌ Image file not found\n");
        return MOUNT_ERROR_FILE_NOT_FOUND;
    }
    
    // 通过MountImg.c的SetMountParameters函数设置参数
    SetMountParameters(wImagePath, wDriveLetter, isReadonly ? TRUE : FALSE, partitionNum);

    char debug_info[512];
    WideCharToMultiByte(CP_UTF8, 0, wImagePath, -1, debug_info, sizeof(debug_info), NULL, NULL);
    OutputDebugStringA("📁 Image file: ");
    OutputDebugStringA(debug_info);
    OutputDebugStringA("\n");

    WideCharToMultiByte(CP_UTF8, 0, wDriveLetter, -1, debug_info, sizeof(debug_info), NULL, NULL);
    OutputDebugStringA("💿 Drive letter: ");
    OutputDebugStringA(debug_info);
    OutputDebugStringA("\n");

    sprintf(debug_info, "🔒 Read-only: %s\n", readonly ? "YES" : "NO");
    OutputDebugStringA(debug_info);

    sprintf(debug_info, "📊 Partition: %d\n", partition);
    OutputDebugStringA(debug_info);

    // 完全按照MountImg.c第659行的逻辑
    BOOL new_file = FALSE;  // 简化处理，假设文件已存在
    int error = Imdisk_Mount(new_file || !net_installed);
    
    if (error && !new_file && net_installed) {
        OutputDebugStringA("\n🔄 ImDisk failed, trying DiscUtils...\n");
        device_number = -1;
        error = DiscUtils_Mount();
    }

    if (error) {
        OutputDebugStringA("\n💥 ═══ MOUNT FAILED ═══\n");
        return MOUNT_ERROR_MOUNT_FAILED;
    }

    OutputDebugStringA("\n✅ Mount successful, verifying drive access...\n");

    // 验证挂载结果（完全按照MountImg.c第684-703行）
    int i = 0;
    do {
        if (GetVolumeInformation(drive, NULL, 0, NULL, NULL, NULL, NULL, 0)) {
            OutputDebugStringA("🎉 ╔═══════════════════════════════════════╗\n");
            OutputDebugStringA("   ║        MOUNT SUCCESS!                 ║\n");
            OutputDebugStringA("   ╚═══════════════════════════════════════╝\n");
            return MOUNT_SUCCESS;
        } else if (GetLastError() == ERROR_UNRECOGNIZED_VOLUME) {
            OutputDebugStringA("⚠️  Unrecognized volume detected\n");
            return MOUNT_ERROR_MOUNT_FAILED;
        }
        Sleep(100);
    } while (++i < 100);

    OutputDebugStringA("⏰ Timeout waiting for drive to become accessible\n");
    return MOUNT_ERROR_MOUNT_FAILED;
}

// 卸载虚拟磁盘（直接使用MountImg.c的start_process函数）
MOUNT_RESULT UnmountVirtualDisk_Core(const char* driveLetter)
{
    if (!driveLetter) {
        return MOUNT_ERROR_INVALID_PARAMS;
    }
    
    WCHAR wDriveLetter[8];
    MultiByteToWideChar(CP_UTF8, 0, driveLetter, -1, wDriveLetter, _countof(wDriveLetter));
    
    WCHAR cmdline[MAX_PATH];
    _snwprintf(cmdline, _countof(cmdline), L"imdisk -d -m \"%s\"", wDriveLetter);
    
    OutputDebugStringA("🗑️  Unmounting drive: ");
    OutputDebugStringA(driveLetter);
    OutputDebugStringA("\n");
    
    // 直接调用MountImg.c的start_process函数
    DWORD result = start_process(cmdline, 1);
    
    if (result == 0) {
        OutputDebugStringA("✅ Unmount successful\n");
        return MOUNT_SUCCESS;
    } else {
        OutputDebugStringA("❌ Unmount failed\n");
        return MOUNT_ERROR_UNMOUNT_FAILED;
    }
}

// 清理函数
void CleanupVirtualDiskLib(void)
{
    OutputDebugStringA("🧹 Cleaning up VirtualDiskLib...\n");
    
    CleanupMountImg();
    
    OutputDebugStringA("✅ VirtualDiskLib cleanup complete\n");
}

// ===== 兼容性函数（为VirtualDiskLib.cpp提供） =====

// 初始化挂载核心模块
int InitMountCore(void)
{
    OutputDebugStringA("🔧 InitMountCore called (delegating to MountImg.c)\n");
    return InitializeVirtualDiskLib() ? 0 : 1;
}

// 清理挂载核心模块
void CleanupMountCore(void)
{
    OutputDebugStringA("🧹 CleanupMountCore called (delegating to MountImg.c)\n");
    CleanupVirtualDiskLib();
}

// 获取磁盘信息
int GetDiskInfo(const char* filePath, DiskInfo* diskInfo)
{
    if (!filePath || !diskInfo) {
        return 1;
    }
    
    OutputDebugStringA("📊 GetDiskInfo called\n");
    
    // 清零结构体
    memset(diskInfo, 0, sizeof(DiskInfo));
    
    // 检查文件是否存在
    DWORD fileAttrib = GetFileAttributesA(filePath);
    if (fileAttrib == INVALID_FILE_ATTRIBUTES) {
        OutputDebugStringA("❌ File not found\n");
        return 1;
    }
    
    // 获取文件大小
    HANDLE hFile = CreateFileA(filePath, GENERIC_READ, FILE_SHARE_READ, NULL, OPEN_EXISTING, 0, NULL);
    if (hFile != INVALID_HANDLE_VALUE) {
        LARGE_INTEGER fileSize;
        if (GetFileSizeEx(hFile, &fileSize)) {
            diskInfo->size_bytes = fileSize.QuadPart;
            diskInfo->size_mb = (int)(fileSize.QuadPart / (1024 * 1024));
        }
        CloseHandle(hFile);
    }
    
    // 复制文件路径
    strncpy(diskInfo->file_path, filePath, sizeof(diskInfo->file_path) - 1);
    diskInfo->file_path[sizeof(diskInfo->file_path) - 1] = '\0';
    
    // 检测文件格式
    const char* ext = strrchr(filePath, '.');
    if (ext) {
        if (_stricmp(ext, ".vhd") == 0) {
            strcpy(diskInfo->format_type, "VHD");
        } else if (_stricmp(ext, ".vhdx") == 0) {
            strcpy(diskInfo->format_type, "VHDX");
        } else if (_stricmp(ext, ".vmdk") == 0) {
            strcpy(diskInfo->format_type, "VMDK");
        } else if (_stricmp(ext, ".iso") == 0) {
            strcpy(diskInfo->format_type, "ISO");
        } else if (_stricmp(ext, ".img") == 0) {
            strcpy(diskInfo->format_type, "IMG");
        } else {
            strcpy(diskInfo->format_type, "Unknown");
        }
    } else {
        strcpy(diskInfo->format_type, "Unknown");
    }
    
    // 默认值
    diskInfo->partition_count = 1;
    diskInfo->is_readonly = 0;
    strcpy(diskInfo->file_system, "Unknown");
    
    char debug_info[256];
    sprintf(debug_info, "✅ File: %s, Size: %lld bytes (%d MB), Format: %s\n",
            filePath, diskInfo->size_bytes, diskInfo->size_mb, diskInfo->format_type);
    OutputDebugStringA(debug_info);
    
    return 0;
}
