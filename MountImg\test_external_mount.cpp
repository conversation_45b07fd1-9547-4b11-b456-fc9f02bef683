/*
 * 测试外部进程挂载模式
 */

#include <windows.h>
#include <stdio.h>
#include "VirtualDiskLib/VirtualDiskLib.h"

int main()
{
    printf("Testing External Process Mount Mode\n");
    printf("===================================\n\n");
    
    // 初始化库
    if (InitializeVirtualDiskLib() != VDL_SUCCESS) {
        printf("Failed to initialize VirtualDiskLib\n");
        return 1;
    }
    
    printf("VirtualDiskLib initialized successfully\n\n");
    
    // 测试挂载VHD文件
    const char* jsonInput = "{\"file_path\":\"E:\\\\002_VHD\\\\vhd.vhd\",\"drive\":\"Z:\",\"readonly\":false,\"partition\":1}";
    char jsonOutput[2048];
    
    printf("Testing VHD mount...\n");
    printf("Input JSON: %s\n", jsonInput);
    
    int result = MountVirtualDisk(jsonInput, jsonOutput, sizeof(jsonOutput));
    
    printf("Mount result: %d\n", result);
    printf("Output JSON: %s\n", jsonOutput);
    
    if (result == VDL_SUCCESS) {
        printf("✅ VHD mount test PASSED\n");
    } else {
        printf("❌ VHD mount test FAILED\n");
    }
    
    printf("\n");
    
    // 测试挂载VHDX文件
    const char* jsonInput2 = "{\"file_path\":\"E:\\\\003_VHDX\\\\VHDX.vhdx\",\"drive\":\"Y:\",\"readonly\":false,\"partition\":1}";
    
    printf("Testing VHDX mount...\n");
    printf("Input JSON: %s\n", jsonInput2);
    
    result = MountVirtualDisk(jsonInput2, jsonOutput, sizeof(jsonOutput));
    
    printf("Mount result: %d\n", result);
    printf("Output JSON: %s\n", jsonOutput);
    
    if (result == VDL_SUCCESS) {
        printf("✅ VHDX mount test PASSED\n");
    } else {
        printf("❌ VHDX mount test FAILED\n");
    }
    
    printf("\n");
    
    // 清理
    CleanupVirtualDiskLib();
    
    printf("Test completed. Press any key to exit...\n");
    getchar();
    
    return 0;
}
