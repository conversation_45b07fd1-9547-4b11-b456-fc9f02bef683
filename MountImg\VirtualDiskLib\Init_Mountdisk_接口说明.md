# Init_Mountdisk 一体化虚拟磁盘管理接口说明

## 📋 **接口概述**

`Init_Mountdisk` 是一个新增的一体化虚拟磁盘管理接口，按照006_Dll标准格式进行封装。该接口整合了初始化、挂载、卸载、状态查询和清理等操作，提供了统一的管理入口。

## 🔧 **函数签名**

```cpp
VIRTUALDISKLIB_API const char* Init_Mountdisk(
    const char* params,
    ProgressCallback progressCallback = nullptr,
    const char* taskId = "",
    QueryTaskControlCallback queryTaskControlCb = nullptr
);
```

## 📝 **输入参数格式**

### JSON输入参数结构
```json
{
  "operation": "mount",                        // 操作类型: "mount", "unmount", "status", "full_cycle"
  "file_path": "C:\\path\\to\\disk.vmdk",     // 镜像文件路径（mount操作必需）
  "drive": "Z:",                              // 驱动器号（mount/unmount操作必需）
  "readonly": false,                          // 是否只读挂载（mount操作可选，默认false）
  "partition": 1,                             // 分区号（mount操作可选，默认1）
  "auto_cleanup": true,                       // 操作完成后是否自动清理（可选，默认true）
  "force_unmount": false,                     // 是否强制卸载（unmount操作可选，默认false）
  "check_dependencies": true                  // 是否检查依赖项（可选，默认true）
}
```

### 参数说明

| 参数名 | 类型 | 必需 | 默认值 | 说明 |
|--------|------|------|--------|------|
| `operation` | string | 是 | "mount" | 操作类型 |
| `file_path` | string | mount时必需 | - | 虚拟磁盘文件路径 |
| `drive` | string | mount/unmount时必需 | - | 目标驱动器号 |
| `readonly` | boolean | 否 | false | 是否只读挂载 |
| `partition` | number | 否 | 1 | 分区号 |
| `auto_cleanup` | boolean | 否 | true | 是否自动清理资源 |
| `force_unmount` | boolean | 否 | false | 是否强制卸载 |
| `check_dependencies` | boolean | 否 | true | 是否检查依赖项 |

## 📤 **输出结果格式**

### 成功响应结构
```json
{
  "status": "success",
  "message": "Operation completed successfully",
  "operation": "mount",
  "results": {
    "initialization": {
      "success": true,
      "dependencies_checked": true
    },
    "mount": {
      "success": true,
      "drive_letter": "Z:",
      "readonly": false,
      "partition": 1
    },
    "unmount": {
      "success": true,
      "unmounted_drive": "Z:",
      "force_used": false
    },
    "status": {
      "success": true,
      "mounted_drives": [
        {
          "drive_letter": "Z:",
          "image_path": "C:\\path\\to\\disk.vmdk",
          "file_system": "NTFS",
          "size_mb": 1024,
          "readonly": false
        }
      ]
    },
    "cleanup": {
      "success": true,
      "resources_freed": true
    }
  }
}
```

## 🎯 **支持的操作类型**

### 1. **mount** - 挂载操作
- 执行：初始化 → 挂载 → 清理（可选）
- 必需参数：`file_path`, `drive`
- 可选参数：`readonly`, `partition`, `auto_cleanup`, `check_dependencies`

### 2. **unmount** - 卸载操作
- 执行：初始化 → 卸载 → 清理（可选）
- 必需参数：`drive`
- 可选参数：`force_unmount`, `auto_cleanup`

### 3. **status** - 状态查询
- 执行：初始化 → 状态查询 → 清理（可选）
- 可选参数：`drive`（为空则查询所有）

### 4. **full_cycle** - 完整周期
- 执行：初始化 → 挂载 → 卸载 → 清理（可选）
- 必需参数：`file_path`, `drive`
- 可选参数：所有挂载和卸载相关参数

## 💡 **使用示例**

### 示例1：挂载虚拟磁盘
```cpp
const char* mount_params = 
    "{"
    "\"operation\":\"mount\","
    "\"file_path\":\"C:\\\\test\\\\disk.vmdk\","
    "\"drive\":\"Z:\","
    "\"readonly\":false,"
    "\"partition\":1,"
    "\"auto_cleanup\":true"
    "}";

const char* result = Init_Mountdisk(mount_params, nullptr, "mount_task", nullptr);
```

### 示例2：卸载虚拟磁盘
```cpp
const char* unmount_params = 
    "{"
    "\"operation\":\"unmount\","
    "\"drive\":\"Z:\","
    "\"force_unmount\":false,"
    "\"auto_cleanup\":true"
    "}";

const char* result = Init_Mountdisk(unmount_params, nullptr, "unmount_task", nullptr);
```

### 示例3：查询挂载状态
```cpp
const char* status_params = 
    "{"
    "\"operation\":\"status\","
    "\"auto_cleanup\":false"
    "}";

const char* result = Init_Mountdisk(status_params, nullptr, "status_task", nullptr);
```

### 示例4：完整周期操作
```cpp
const char* full_cycle_params = 
    "{"
    "\"operation\":\"full_cycle\","
    "\"file_path\":\"C:\\\\test\\\\disk.vmdk\","
    "\"drive\":\"Z:\","
    "\"readonly\":false,"
    "\"auto_cleanup\":true"
    "}";

const char* result = Init_Mountdisk(full_cycle_params, nullptr, "cycle_task", nullptr);
```

## 🔄 **执行流程**

### 通用执行流程
1. **参数解析** (0% - 5%)
2. **库初始化** (5% - 15%)
3. **主要操作** (15% - 85%)
   - mount: 挂载虚拟磁盘
   - unmount: 卸载虚拟磁盘
   - status: 查询挂载状态
   - full_cycle: 挂载后立即卸载
4. **资源清理** (85% - 95%)（如果启用auto_cleanup）
5. **结果返回** (95% - 100%)

## ⚠️  **注意事项**

1. **参数验证**：函数会验证必需参数的存在性
2. **错误处理**：任何步骤失败都会返回详细的错误信息
3. **资源管理**：建议启用`auto_cleanup`确保资源正确释放
4. **线程安全**：函数内部使用互斥锁保证线程安全
5. **兼容性**：完全兼容Windows XP及以上系统

## 🚀 **优势特点**

- **一体化管理**：单一接口完成所有虚拟磁盘操作
- **标准化接口**：符合006_Dll标准，支持进度回调和任务控制
- **灵活配置**：支持多种操作模式和参数组合
- **详细反馈**：提供完整的操作结果和状态信息
- **错误恢复**：自动处理异常情况并进行资源清理
- **高性能**：直接调用底层函数，无额外进程开销

## 📚 **相关文件**

- `VirtualDiskLib.h` - 接口声明
- `VirtualDiskLib.cpp` - 接口实现
- `VirtualDiskLib.def` - DLL导出定义
- `test_init_mountdisk.cpp` - 测试示例程序
