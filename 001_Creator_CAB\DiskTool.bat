@echo off
rem 磁盘挂载工具启动脚本 - 参考原始脚本结构
rem 兼容Windows XP及以上系统

if not "%1"=="7" start /min cmd /c ""%~0" 7 %*" & exit /b

set F=%TEMP%\DiskUp%TIME::=%

rem 使用VBScript解压ZIP文件 (兼容XP)
echo Set objShell = CreateObject("Shell.Application") > "%TEMP%\unzip.vbs"
echo Set objFolder = objShell.NameSpace("%F%") >> "%TEMP%\unzip.vbs"
echo Set objZip = objShell.NameSpace("%~dp0files.zip") >> "%TEMP%\unzip.vbs"
echo objFolder.CopyHere objZip.Items, 20 >> "%TEMP%\unzip.vbs"

mkdir "%F%"
cscript //nologo "%TEMP%\unzip.vbs"
del "%TEMP%\unzip.vbs"

rem 等待解压完成
timeout /t 3 /nobreak >nul 2>&1

rem 查找config.exe (可能在子目录中)
if exist "%F%\config.exe" (
    "%F%\config.exe" %2 %3 %4
) else (
    for /r "%F%" %%i in (config.exe) do (
        if exist "%%i" "%%i" %2 %3 %4 & goto :cleanup
    )
)

:cleanup
rd /s /q "%F%"
