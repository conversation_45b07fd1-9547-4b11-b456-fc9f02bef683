/*
 * test_unified_interface.cpp
 * 测试VirtualDiskLib统一操作接口
 * 
 * 功能：演示如何使用基于action字段的统一操作接口
 * 编译：cl test_unified_interface.cpp /I. VirtualDiskLib32.lib /Fe:test_unified.exe
 */

#include <iostream>
#include <string>
#include <windows.h>
#include "VirtualDiskLib.h"

// ========================================
// 测试用回调函数
// ========================================

/*
 * 简化的进度回调函数
 */
void TestProgressCallback(const std::string& taskId, int progress) {
    std::cout << "[PROGRESS] Task: " << taskId << ", Progress: " << progress << "%" << std::endl;
}

/*
 * 简化的任务控制回调函数
 */
bool TestQueryTaskControlCallback(const std::string& taskId, int controlType) {
    std::cout << "[CONTROL] Task: " << taskId << ", Control Type: " << controlType << std::endl;
    return false; // 不取消任何任务
}

// ========================================
// 统一接口测试函数
// ========================================

/*
 * 测试获取库信息
 */
void TestGetLibraryInfo() {
    std::cout << "\n=== 测试获取库信息 (action: info) ===" << std::endl;
    
    std::string params = R"({
        "action": "info"
    })";
    
    std::string result = VirtualDiskOperation(
        params,
        TestProgressCallback,
        "test_info",
        TestQueryTaskControlCallback
    );
    
    std::cout << "结果: " << result << std::endl;
}

/*
 * 测试初始化库
 */
void TestInitializeLibrary() {
    std::cout << "\n=== 测试初始化库 (action: initialize) ===" << std::endl;
    
    std::string params = R"({
        "action": "initialize",
        "check_dependencies": true,
        "enable_debug": true
    })";
    
    std::string result = VirtualDiskOperation(
        params,
        TestProgressCallback,
        "test_init",
        TestQueryTaskControlCallback
    );
    
    std::cout << "结果: " << result << std::endl;
}

/*
 * 测试挂载虚拟磁盘
 */
void TestMountVirtualDisk() {
    std::cout << "\n=== 测试挂载虚拟磁盘 (action: mount) ===" << std::endl;
    
    std::string params = R"({
        "action": "mount",
        "image_path": "C:\\test\\disk.vmdk",
        "drive_letter": "Z:",
        "readonly": false,
        "partition": 1
    })";
    
    std::string result = VirtualDiskOperation(
        params,
        TestProgressCallback,
        "test_mount",
        TestQueryTaskControlCallback
    );
    
    std::cout << "挂载结果: " << result << std::endl;
}

/*
 * 测试获取挂载状态
 */
void TestGetMountStatus() {
    std::cout << "\n=== 测试获取挂载状态 (action: status) ===" << std::endl;
    
    // 查询所有挂载的驱动器
    std::string params = R"({
        "action": "status",
        "drive": ""
    })";
    
    std::string result = VirtualDiskOperation(
        params,
        TestProgressCallback,
        "test_status",
        TestQueryTaskControlCallback
    );
    
    std::cout << "挂载状态: " << result << std::endl;
    
    // 查询特定驱动器
    std::cout << "\n--- 查询特定驱动器 Z: ---" << std::endl;
    std::string specificParams = R"({
        "action": "status",
        "drive": "Z:"
    })";
    
    std::string specificResult = VirtualDiskOperation(
        specificParams,
        TestProgressCallback,
        "test_status_z"
    );
    
    std::cout << "Z盘状态: " << specificResult << std::endl;
}

/*
 * 测试卸载虚拟磁盘
 */
void TestUnmountVirtualDisk() {
    std::cout << "\n=== 测试卸载虚拟磁盘 (action: unmount) ===" << std::endl;
    
    std::string params = R"({
        "action": "unmount",
        "drive": "Z:",
        "force": false
    })";
    
    std::string result = VirtualDiskOperation(
        params,
        TestProgressCallback,
        "test_unmount",
        TestQueryTaskControlCallback
    );
    
    std::cout << "卸载结果: " << result << std::endl;
}

/*
 * 测试清理库
 */
void TestCleanupLibrary() {
    std::cout << "\n=== 测试清理库 (action: cleanup) ===" << std::endl;
    
    std::string params = R"({
        "action": "cleanup",
        "force_cleanup": false
    })";
    
    std::string result = VirtualDiskOperation(
        params,
        TestProgressCallback,
        "test_cleanup",
        TestQueryTaskControlCallback
    );
    
    std::cout << "清理结果: " << result << std::endl;
}

/*
 * 测试不支持的操作
 */
void TestUnsupportedAction() {
    std::cout << "\n=== 测试不支持的操作 (action: invalid) ===" << std::endl;
    
    std::string params = R"({
        "action": "invalid_action",
        "some_param": "test"
    })";
    
    std::string result = VirtualDiskOperation(
        params,
        TestProgressCallback,
        "test_invalid"
    );
    
    std::cout << "结果: " << result << std::endl;
}

/*
 * 测试缺少action字段
 */
void TestMissingAction() {
    std::cout << "\n=== 测试缺少action字段 ===" << std::endl;
    
    std::string params = R"({
        "image_path": "C:\\test\\disk.vmdk",
        "drive_letter": "Z:"
    })";
    
    std::string result = VirtualDiskOperation(
        params,
        TestProgressCallback,
        "test_no_action"
    );
    
    std::cout << "结果: " << result << std::endl;
}

// ========================================
// 主函数
// ========================================

int main() {
    std::cout << "VirtualDiskLib 统一操作接口测试程序" << std::endl;
    std::cout << "========================================" << std::endl;
    std::cout << "支持的action值: mount, unmount, cleanup, status, info, initialize" << std::endl;
    
    try {
        // 1. 获取库信息
        TestGetLibraryInfo();
        
        // 2. 初始化库
        TestInitializeLibrary();
        
        // 3. 测试挂载
        TestMountVirtualDisk();
        
        // 4. 获取挂载状态
        TestGetMountStatus();
        
        // 5. 测试卸载
        TestUnmountVirtualDisk();
        
        // 6. 清理库
        TestCleanupLibrary();
        
        // 7. 测试错误情况
        TestUnsupportedAction();
        TestMissingAction();
        
        std::cout << "\n========================================" << std::endl;
        std::cout << "所有测试完成！" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "测试过程中发生异常: " << e.what() << std::endl;
        return 1;
    } catch (...) {
        std::cerr << "测试过程中发生未知异常" << std::endl;
        return 1;
    }
    
    return 0;
}
