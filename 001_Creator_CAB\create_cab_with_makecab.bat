@echo off
rem 使用Windows内置的makecab工具创建CAB文件
echo 使用makecab创建CAB文件...

rem 检查makecab是否可用
makecab /? >nul 2>&1
if not %ERRORLEVEL% EQU 0 (
    echo 错误: makecab工具不可用
    echo 请安装Windows SDK或确保makecab在PATH中
    pause
    exit /b 1
)

rem 清理旧文件
if exist files.cab del files.cab
if exist files.ddf del files.ddf

echo 创建DDF指令文件...

rem 创建DDF文件
echo .OPTION EXPLICIT > files.ddf
echo .Set CabinetNameTemplate=files.cab >> files.ddf
echo .Set DiskDirectoryTemplate=. >> files.ddf
echo .Set CompressionType=MSZIP >> files.ddf
echo .Set UniqueFiles=OFF >> files.ddf
echo .Set Cabinet=ON >> files.ddf
echo .Set Compress=ON >> files.ddf
echo .Set SourceDir=files >> files.ddf
echo. >> files.ddf

rem 添加所有文件
echo 添加文件到DDF...
for /r files %%f in (*.*) do (
    set "filepath=%%f"
    setlocal enabledelayedexpansion
    set "relpath=!filepath:*files\=!"
    echo "!relpath!" >> files.ddf
    endlocal
)

echo 执行makecab...
makecab /F files.ddf

if %ERRORLEVEL% EQU 0 (
    echo 成功创建files.cab
    dir files.cab
    
    rem 测试CAB文件
    echo 测试CAB文件...
    mkdir test_cab
    extrac32.exe /e /l test_cab files.cab
    
    if exist test_cab\config.exe (
        echo 测试成功: config.exe已解压
    ) else (
        echo 测试失败: config.exe未找到
    )
    
    rmdir /s /q test_cab 2>nul
) else (
    echo makecab执行失败
)

rem 清理DDF文件
del files.ddf 2>nul

echo 完成
pause
