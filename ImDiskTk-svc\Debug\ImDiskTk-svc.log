﻿C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\MSBuild\Microsoft\VC\v150\Platforms\Win32\PlatformToolsets\v141_xp\Toolset.targets(39,5): warning MSB8051: 面向 Windows XP 的支持已被弃用，将来的 Visual Studio 版本不再提供该支持。请访问 https://go.microsoft.com/fwlink/?linkid=2023588，获取详细信息。
libcmtd.lib(init.obj) : error LNK2019: 无法解析的外部符号 __CrtDbgReport，函数 __CRT_RTC_INIT 中引用了该符号
libcmtd.lib(init.obj) : error LNK2019: 无法解析的外部符号 __CrtDbgReportW，函数 __CRT_RTC_INITW 中引用了该符号
libcmtd.lib(error.obj) : error LNK2019: 无法解析的外部符号 _strcpy_s，函数 "void __cdecl _RTC_StackFailure(void *,char const *)" (?_RTC_StackFailure@@YAXPAXPBD@Z) 中引用了该符号
libcmtd.lib(error.obj) : error LNK2019: 无法解析的外部符号 _strcat_s，函数 "void __cdecl _RTC_StackFailure(void *,char const *)" (?_RTC_StackFailure@@YAXPAXPBD@Z) 中引用了该符号
libcmtd.lib(error.obj) : error LNK2019: 无法解析的外部符号 ___stdio_common_vsprintf_s，函数 __vsprintf_s_l 中引用了该符号
libcmtd.lib(pdblkup.obj) : error LNK2019: 无法解析的外部符号 __wmakepath_s，函数 "int __cdecl GetPdbDllPathFromFilePath(wchar_t const *,wchar_t *,unsigned int)" (?GetPdbDllPathFromFilePath@@YAHPB_WPA_WI@Z) 中引用了该符号
libcmtd.lib(pdblkup.obj) : error LNK2019: 无法解析的外部符号 __wsplitpath_s，函数 "int __cdecl GetPdbDllPathFromFilePath(wchar_t const *,wchar_t *,unsigned int)" (?GetPdbDllPathFromFilePath@@YAHPB_WPA_WI@Z) 中引用了该符号
libcmtd.lib(pdblkup.obj) : error LNK2019: 无法解析的外部符号 _wcscpy_s，函数 "int __cdecl GetPdbDllPathFromFilePath(wchar_t const *,wchar_t *,unsigned int)" (?GetPdbDllPathFromFilePath@@YAHPB_WPA_WI@Z) 中引用了该符号
libcmtd.lib(pdblkup.obj) : error LNK2019: 无法解析的外部符号 ___vcrt_GetModuleFileNameW，函数 "struct HINSTANCE__ * __cdecl GetPdbDll(void)" (?GetPdbDll@@YAPAUHINSTANCE__@@XZ) 中引用了该符号
libcmtd.lib(pdblkup.obj) : error LNK2019: 无法解析的外部符号 ___vcrt_GetModuleHandleW，函数 "struct HINSTANCE__ * __cdecl GetPdbDll(void)" (?GetPdbDll@@YAPAUHINSTANCE__@@XZ) 中引用了该符号
libcmtd.lib(pdblkup.obj) : error LNK2019: 无法解析的外部符号 ___vcrt_LoadLibraryExW，函数 "struct HINSTANCE__ * __cdecl GetPdbDll(void)" (?GetPdbDll@@YAPAUHINSTANCE__@@XZ) 中引用了该符号
libcmtd.lib(chandler4_noexcept.obj) : error LNK2019: 无法解析的外部符号 _terminate，函数 __except_handler4_noexcept 中引用了该符号
libcmtd.lib(chandler4_noexcept.obj) : error LNK2019: 无法解析的外部符号 ___current_exception，函数 __except_handler4_noexcept 中引用了该符号
libcmtd.lib(chandler4_noexcept.obj) : error LNK2019: 无法解析的外部符号 ___current_exception_context，函数 __except_handler4_noexcept 中引用了该符号
libcmtd.lib(chandler4_noexcept.obj) : error LNK2019: 无法解析的外部符号 __except_handler4，函数 __except_handler4_noexcept 中引用了该符号
E:\Work\002_Project\005_VirtualDiskMount_ProjectAll\001_Code\005_VirtualDiskMount_imdisktk\001_imdisktk_source_2020.11.20\Debug\ImDiskTk-svc32.exe : fatal error LNK1120: 15 个无法解析的外部命令
