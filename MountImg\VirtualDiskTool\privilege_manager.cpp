﻿/*
 * privilege_manager.cpp
 * VirtualDiskTool 权限管理模块实现
 * 
 * 参考：MountImg.c 中的权限提升实现
 */

#define _CRT_SECURE_NO_WARNINGS
#include "privilege_manager.h"
#include <stdio.h>
#include <string.h>
#include <shellapi.h>

/*
 * 检查当前进程是否以管理员权限运行
 */
BOOL IsRunningAsAdministrator(void)
{
    BOOL isAdmin = FALSE;
    PSID administratorsGroup = NULL;
    SID_IDENTIFIER_AUTHORITY ntAuthority = SECURITY_NT_AUTHORITY;
    
    // 创建管理员组的SID
    if (AllocateAndInitializeSid(&ntAuthority, 2, SECURITY_BUILTIN_DOMAIN_RID, 
                                 DOMAIN_ALIAS_RID_ADMINS, 0, 0, 0, 0, 0, 0, 
                                 &administratorsGroup)) {
        // 检查当前用户是否属于管理员组
        if (!CheckTokenMembership(NULL, administratorsGroup, &isAdmin)) {
            isAdmin = FALSE;
        }
        FreeSid(administratorsGroup);
    }
    
    return isAdmin;
}

/*
 * 检查当前操作系统版本是否需要UAC
 */
BOOL IsUACRequired(void)
{
    OSVERSIONINFO osVersion;
    if (!GetOSVersion(&osVersion)) {
        return FALSE;
    }
    
    // Vista (6.0) 及以上版本需要UAC
    return (osVersion.dwMajorVersion >= 6);
}

/*
 * 请求管理员权限并重新启动程序
 * 完全参照 MountImg.c 中的实现
 */
int RequestAdministratorPrivileges(int argc, wchar_t* argv[], const wchar_t* cmdline_ptr)
{
    WCHAR txt[MAX_PATH * 2];
    WCHAR exePath[MAX_PATH];
    DWORD logicalDrives;

    // 获取当前可执行文件路径
    if (GetModuleFileNameW(NULL, exePath, MAX_PATH) == 0) {
        printf("ERROR: Failed to get executable path\n");
        return 1;
    }

    // 获取逻辑驱动器掩码 (完全参照 MountImg.c)
    logicalDrives = GetLogicalDrives();

    // 构建参数字符串 (完全参照 MountImg.c 格式)
    // 格式: /UAC <logical_drives> <cmdline_ptr>
    // 注意：cmdline_ptr 是原始命令行参数，不是可执行文件路径！
    if (cmdline_ptr && wcslen(cmdline_ptr) > 0) {
        _snwprintf(txt, _countof(txt) - 1, L"/UAC %d %s", logicalDrives, cmdline_ptr);
    }
    else {
        _snwprintf(txt, _countof(txt) - 1, L"/UAC %d", logicalDrives);
    }

    printf("Requesting administrator privileges...\n");
    printf("Executable: %S\n", exePath);
    printf("Parameters: %S\n", txt);
    printf("Logical Drives: %d (0x%08X)\n", logicalDrives, logicalDrives);
    wprintf(L"Original cmdline_ptr: %ls\n", cmdline_ptr ? cmdline_ptr : L"(null)");

//#ifdef DEBUG_InCMD
//    // 调试模式：不实际执行权限提升，只显示信息
//    printf("DEBUG: Would execute ShellExecute with runas\n");
//    printf("DEBUG: This is debug mode, not actually elevating\n");
//    return 0;
//#else
    // 使用 ShellExecute 请求管理员权限 (完全参照 MountImg.c 实现)
    HINSTANCE result = ShellExecuteW(NULL, L"runas", exePath, txt, NULL, SW_SHOWDEFAULT);

    if ((INT_PTR)result <= 32) {
        printf("ERROR: Failed to request administrator privileges (error: %d)\n", (int)(INT_PTR)result);
        printf("ShellExecute failed, returning to normal execution...\n");
        return 1;  // 失败时返回错误码，不退出进程
    }

    printf("Administrator privileges requested successfully\n");
    printf("Current process will exit, elevated process will continue...\n");

    printf("*********************************************************************\n");

    // 成功启动提升权限的进程，退出当前进程 (完全参照 MountImg.c 实现)
    ExitProcess(0);

    // 这里不会执行到
    return 0;
//#endif
}

/*
 * 检查命令行参数中是否包含UAC标志
 */
BOOL IsElevatedProcess(int argc, wchar_t* argv[])
{
    if (argc < 2) {
        return FALSE;
    }

    // 检查第一个参数是否为 "/UAC"
    return (wcscmp(argv[1], L"/UAC") == 0);
}

/*
 * 从UAC参数中提取原始命令行参数
 */
int ExtractOriginalCommandLine(int argc, wchar_t* argv[], wchar_t* originalCmdLine, int bufferSize)
{
    if (!IsElevatedProcess(argc, argv)) {
        return 1; // 不是提升权限的进程
    }
    
    if (argc < 4) {
        return 2; // 参数不足
    }
    
    // UAC 参数格式: /UAC <logical_drives> <original_params> [additional_params]
    // argv[0] = 程序名
    // argv[1] = "/UAC"
    // argv[2] = logical_drives
    // argv[3] = original_params
    // argv[4] = additional_params (可选)
    
    // 提取原始参数
    if (argc >= 4 && wcslen(argv[3]) > 0) {
        wcsncpy(originalCmdLine, argv[3], bufferSize - 1);
        originalCmdLine[bufferSize - 1] = L'\0';

        // 如果有额外参数，也添加进去
        if (argc >= 5 && wcslen(argv[4]) > 0) {
            wcsncat(originalCmdLine, L" ", bufferSize - wcslen(originalCmdLine) - 1);
            wcsncat(originalCmdLine, argv[4], bufferSize - wcslen(originalCmdLine) - 1);
        }

        return 0;
    }

    // 没有原始参数
    originalCmdLine[0] = L'\0';
    return 0;
}

/*
 * 获取操作系统版本信息
 */
BOOL GetOSVersion(OSVERSIONINFO* osVersion)
{
    if (!osVersion) {
        return FALSE;
    }
    
    ZeroMemory(osVersion, sizeof(OSVERSIONINFO));
    osVersion->dwOSVersionInfoSize = sizeof(OSVERSIONINFO);
    
    return GetVersionEx(osVersion);
}
