@echo off
chcp 65001 >nul
echo ========================================
echo 测试 VirtualDiskTool 管理员权限检查
echo ========================================

echo.
echo 🔍 功能说明：
echo VirtualDiskTool 现在要求必须以管理员权限运行
echo 非管理员权限时会显示错误信息并退出
echo.

echo 📋 实现特点：
echo ✅ 使用 IsRunningAsAdministrator() 检查权限
echo ✅ 非管理员权限时显示详细错误信息
echo ✅ 提供解决方案指导
echo ✅ 优雅退出，返回错误码 1
echo.

echo ----------------------------------------
echo 步骤1: 检查当前用户权限状态
echo ----------------------------------------

echo 检查当前用户权限...
net session >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo ✅ 当前以管理员权限运行
    echo   VirtualDiskTool 应该正常启动
    set CURRENT_ADMIN=1
) else (
    echo ❌ 当前未以管理员权限运行
    echo   VirtualDiskTool 应该显示错误信息并退出
    set CURRENT_ADMIN=0
)

echo.
echo ----------------------------------------
echo 步骤2: 运行 VirtualDiskTool 测试权限检查
echo ----------------------------------------

echo 执行命令: VirtualDiskTool32.exe --test-mount
echo 观察权限检查结果...
echo.

echo === 程序输出开始 ===
VirtualDiskTool32.exe --test-mount
set TOOL_EXIT_CODE=%ERRORLEVEL%
echo === 程序输出结束 ===

echo.
echo 程序退出码: %TOOL_EXIT_CODE%

echo.
echo ----------------------------------------
echo 步骤3: 验证权限检查结果
echo ----------------------------------------

echo.
echo 📊 结果分析：

if %CURRENT_ADMIN% EQU 1 (
    echo 🔍 当前环境：管理员权限
    if %TOOL_EXIT_CODE% EQU 0 (
        echo ✅ SUCCESS: 程序正常运行 (退出码: %TOOL_EXIT_CODE%)
        echo    权限检查通过，程序继续执行
    ) else (
        echo ❌ UNEXPECTED: 程序异常退出 (退出码: %TOOL_EXIT_CODE%)
        echo    可能存在其他问题，需要进一步调试
    )
) else (
    echo 🔍 当前环境：非管理员权限
    if %TOOL_EXIT_CODE% EQU 1 (
        echo ✅ SUCCESS: 程序正确拒绝运行 (退出码: %TOOL_EXIT_CODE%)
        echo    权限检查工作正常，显示了错误信息
    ) else if %TOOL_EXIT_CODE% EQU 0 (
        echo ❌ FAILED: 程序意外成功运行 (退出码: %TOOL_EXIT_CODE%)
        echo    权限检查可能未生效
    ) else (
        echo ⚠️  UNKNOWN: 程序异常退出 (退出码: %TOOL_EXIT_CODE%)
        echo    可能存在其他错误
    )
)

echo.
echo ----------------------------------------
echo 步骤4: 权限检查功能说明
echo ----------------------------------------

echo.
echo 🛠️  权限检查实现：
echo.
echo ✅ 检查函数：
echo    BOOL isAdmin = IsRunningAsAdministrator();
echo    - 使用 Windows API 检查当前进程权限
echo    - 检查是否属于管理员组
echo    - 返回 TRUE/FALSE
echo.
echo ✅ 错误处理：
echo    if (!isAdmin) {
echo        printf("❌ 当前未以管理员权限运行");
echo        printf("🚫 错误：VirtualDiskTool 需要管理员权限才能运行");
echo        printf("解决方案指导...");
echo        return 1;  // 返回错误码
echo    }
echo.
echo ✅ 用户指导：
echo    - 显示详细错误信息
echo    - 提供解决方案步骤
echo    - 等待用户确认后退出
echo.

echo 🎯 预期行为：
echo.
echo 📋 管理员权限环境：
echo    ✅ 显示 "当前以管理员权限运行"
echo    ✅ 程序继续正常执行
echo    ✅ 退出码为 0 (成功)
echo.
echo 📋 非管理员权限环境：
echo    ❌ 显示 "当前未以管理员权限运行"
echo    ❌ 显示详细错误信息和解决方案
echo    ❌ 等待用户按键后退出
echo    ❌ 退出码为 1 (错误)
echo.

echo 🚀 使用建议：
echo.
echo 1. 开发测试时：
echo    - 在普通命令提示符中测试权限检查
echo    - 在管理员命令提示符中测试正常功能
echo.
echo 2. 用户使用时：
echo    - 右键程序选择 "以管理员身份运行"
echo    - 或在管理员命令提示符中运行
echo.
echo 3. 部署时：
echo    - 可以考虑添加 UAC 清单文件
echo    - 或提供批处理脚本自动请求权限
echo.

echo ========================================
echo 管理员权限检查测试完成
echo ========================================

pause
