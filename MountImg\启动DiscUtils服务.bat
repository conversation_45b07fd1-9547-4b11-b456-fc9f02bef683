@echo off
chcp 65001 >nul
echo.
echo ═══════════════════════════════════════════════════════════════
echo                    启动DiscUtils服务
echo ═══════════════════════════════════════════════════════════════
echo.

cd /d "%~dp0"

echo 📋 DiscUtils服务用于挂载VHDX和VMDK文件
echo    - VHDX: Virtual Hard Disk v2 (Hyper-V格式)
echo    - VMDK: VMware Virtual Disk (VMware格式)
echo.

echo 🔍 检查当前服务状态...
sc query ImDiskSvc >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ ImDiskSvc服务已安装
    sc query ImDiskSvc | findstr "STATE" | findstr "RUNNING" >nul
    if %errorlevel% equ 0 (
        echo ✅ ImDiskSvc服务正在运行
        goto :test_mount
    ) else (
        echo ⚠️  ImDiskSvc服务已安装但未运行
        goto :start_service
    )
) else (
    echo ❌ ImDiskSvc服务未安装
    goto :install_service
)

:install_service
echo.
echo 🔧 安装DiscUtils服务...
if exist "Debug\ImDiskTk-svc.exe" (
    echo 📁 使用本地ImDiskTk-svc.exe
    Debug\ImDiskTk-svc.exe /install
) else if exist "..\ImDiskTk-svc\ImDiskTk-svc32.exe" (
    echo 📁 使用ImDiskTk-svc目录中的服务程序
    ..\ImDiskTk-svc\ImDiskTk-svc32.exe /install
) else (
    echo ❌ 找不到ImDiskTk-svc.exe服务程序
    echo 💡 请确保ImDiskTk-svc.exe在以下位置之一：
    echo    - Debug\ImDiskTk-svc.exe
    echo    - ..\ImDiskTk-svc\ImDiskTk-svc32.exe
    goto :end
)

:start_service
echo.
echo 🚀 启动DiscUtils服务...
net start ImDiskSvc
if %errorlevel% equ 0 (
    echo ✅ ImDiskSvc服务启动成功
) else (
    echo ❌ ImDiskSvc服务启动失败
    echo 💡 可能的原因：
    echo    - 权限不足（请以管理员身份运行）
    echo    - 服务程序文件损坏
    echo    - 系统兼容性问题
    goto :end
)

:test_mount
echo.
echo 🧪 测试VHDX/VMDK挂载功能...
echo.
echo ═══ 测试VHDX文件挂载 ═══
if exist "E:\003_VHDX\VHDX.vhdx" (
    echo 📁 测试文件: E:\003_VHDX\VHDX.vhdx
    Debug\VirtualDiskTool32.exe mount --file "E:\003_VHDX\VHDX.vhdx" --drive "X:" --readonly
    echo.
    echo 🔍 检查X:驱动器是否挂载成功...
    if exist "X:\" (
        echo ✅ VHDX挂载成功！X:驱动器可访问
        echo 📊 驱动器信息:
        dir X:\ /A
        echo.
        echo 🗑️  卸载测试驱动器...
        Debug\VirtualDiskTool32.exe unmount --drive "X:"
    ) else (
        echo ❌ VHDX挂载失败，X:驱动器不可访问
    )
) else (
    echo ⚠️  测试文件E:\003_VHDX\VHDX.vhdx不存在
)

echo.
echo ═══ 测试有效VMDK文件挂载 ═══
echo ⚠️  当前VMDK测试文件(E:\666666.vmdk)只有395字节，无效
echo 💡 建议：
echo    - 使用VMware创建有效的VMDK文件
echo    - 或从其他来源获取有效的VMDK文件进行测试
echo    - 有效的VMDK文件通常至少几MB大小

:end
echo.
echo ✅ DiscUtils服务配置完成
echo.
echo 💡 使用提示：
echo    - VHDX和VMDK文件现在应该可以正常挂载
echo    - 如果仍有问题，请检查文件是否有效且未损坏
echo    - 可以使用DebugView查看详细的挂载过程
echo.
pause
