@echo off
echo Creating complete CAB file with makecab...

rem Clean up
if exist files_complete.cab del files_complete.cab
if exist complete.ddf del complete.ddf

echo Creating DDF file...

rem Create DDF header
echo .OPTION EXPLICIT > complete.ddf
echo .Set CabinetNameTemplate=files_complete.cab >> complete.ddf
echo .Set DiskDirectoryTemplate=. >> complete.ddf
echo .Set CompressionType=MSZIP >> complete.ddf
echo .Set UniqueFiles=OFF >> complete.ddf
echo .Set Cabinet=ON >> complete.ddf
echo .Set Compress=ON >> complete.ddf
echo. >> complete.ddf

rem Add main executable files
echo "files\config.exe" "config.exe" >> complete.ddf
echo "files\config32.exe" "config32.exe" >> complete.ddf
echo "files\config32_uninstall.exe" "config32_uninstall.exe" >> complete.ddf
echo "files\devio.exe" "devio.exe" >> complete.ddf
echo "files\ImDisk-Dlg.exe" "ImDisk-Dlg.exe" >> complete.ddf
echo "files\ImDiskTk-svc.exe" "ImDiskTk-svc.exe" >> complete.ddf
echo "files\ImDiskTk-svc32.exe" "ImDiskTk-svc32.exe" >> complete.ddf
echo "files\ImDiskTk-svc64.exe" "ImDiskTk-svc64.exe" >> complete.ddf
echo "files\MountImg.exe" "MountImg.exe" >> complete.ddf
echo "files\MountImg32.exe" "MountImg32.exe" >> complete.ddf
echo "files\RamDiskUI.exe" "RamDiskUI.exe" >> complete.ddf
echo "files\RamDyn.exe" "RamDyn.exe" >> complete.ddf
echo "files\RamDyn32.exe" "RamDyn32.exe" >> complete.ddf
echo "files\RamDyn64.exe" "RamDyn64.exe" >> complete.ddf
echo "files\DiscUtilsDevio.exe" "DiscUtilsDevio.exe" >> complete.ddf

rem Add DLL files
echo "files\DevioNet.dll" "DevioNet.dll" >> complete.ddf
echo "files\ImDiskNet.dll" "ImDiskNet.dll" >> complete.ddf
echo "files\DiscUtils.Core.dll" "DiscUtils.Core.dll" >> complete.ddf
echo "files\DiscUtils.Dmg.dll" "DiscUtils.Dmg.dll" >> complete.ddf
echo "files\DiscUtils.Streams.dll" "DiscUtils.Streams.dll" >> complete.ddf
echo "files\DiscUtils.Vdi.dll" "DiscUtils.Vdi.dll" >> complete.ddf
echo "files\DiscUtils.Vhd.dll" "DiscUtils.Vhd.dll" >> complete.ddf
echo "files\DiscUtils.Vhdx.dll" "DiscUtils.Vhdx.dll" >> complete.ddf
echo "files\DiscUtils.Vmdk.dll" "DiscUtils.Vmdk.dll" >> complete.ddf
echo "files\DiscUtils.Xva.dll" "DiscUtils.Xva.dll" >> complete.ddf

rem Add other files
echo "files\cp-admin.lnk" "cp-admin.lnk" >> complete.ddf
echo "files\cp.lnk" "cp.lnk" >> complete.ddf
echo "files\lang.txt" "lang.txt" >> complete.ddf
echo "files\filelist.txt" "filelist.txt" >> complete.ddf

echo Running makecab...
makecab /F complete.ddf

if exist files_complete.cab (
    echo SUCCESS: files_complete.cab created!
    dir files_complete.cab
    
    echo Testing CAB file...
    mkdir test_complete
    extrac32.exe /e /l test_complete files_complete.cab
    
    if exist test_complete\config.exe (
        echo SUCCESS: config.exe found in root!
    ) else (
        echo ERROR: config.exe not found
    )
    
    echo Files in CAB:
    dir test_complete
    
    rmdir /s /q test_complete
) else (
    echo ERROR: CAB file not created
)

rem Clean up
del complete.ddf

echo Done!
pause
