@echo off
chcp 65001 >nul
echo ========================================
echo Testing MountImg.exe Console Input Mode
echo ========================================

echo.
echo 这个测试将启动 MountImg.exe 的控制台输入模式
echo 您可以选择：
echo 1. 手动输入 JSON 参数
echo 2. 使用默认测试参数
echo 3. 退出程序
echo.

echo 启动 MountImg.exe...
echo ----------------------------------------

MountImg.exe

echo.
echo ----------------------------------------
echo 程序执行完毕，退出码: %ERRORLEVEL%
echo.

echo 检查挂载结果...
if exist X:\ (
    echo ✅ SUCCESS: X: 驱动器已挂载
    echo 目录列表:
    dir X: /w
) else (
    echo ❌ FAILED: X: 驱动器未挂载
)

echo.
echo ========================================
echo 控制台输入模式功能说明:
echo ========================================
echo 1. 程序启动后会显示选择菜单
echo 2. 选择 1: 可以手动输入自定义 JSON 参数
echo 3. 选择 2: 使用预设的测试参数
echo 4. 选择 3: 退出程序
echo 5. 输入参数后会询问是否继续执行挂载
echo.
echo JSON 参数格式示例:
echo {"file_path":"E:\\004_VMDK\\666666.vmdk","drive":"X:","readonly":false,"partition":1}
echo.
echo 注意事项:
echo - 文件路径中的反斜杠需要双重转义 \\\\
echo - 字符串值需要用双引号包围
echo - 布尔值使用 true/false (小写)
echo - 数字值不需要引号
echo.

pause
