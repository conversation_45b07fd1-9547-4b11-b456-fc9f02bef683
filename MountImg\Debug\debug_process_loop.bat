@echo off
chcp 65001 >nul
echo ========================================
echo 深度调试 VirtualDiskTool 进程循环问题
echo ========================================

echo.
echo 🔍 问题现象：
echo - VirtualDiskTool执行后开启了好多VirtualDiskTool32.exe进程
echo - 进程数量一直在增加
echo - 形成无限循环
echo.

echo 📋 调试步骤：
echo 1. 检查当前进程状态
echo 2. 单步执行并观察输出
echo 3. 分析参数传递过程
echo 4. 确定循环原因
echo.

echo ----------------------------------------
echo 步骤1: 检查当前进程状态
echo ----------------------------------------

echo 当前 VirtualDiskTool32.exe 进程:
tasklist /fi "imagename eq VirtualDiskTool32.exe" /fo table
echo.

echo 进程数量:
tasklist /fi "imagename eq VirtualDiskTool32.exe" | find /c "VirtualDiskTool32.exe"
echo.

echo ----------------------------------------
echo 步骤2: 执行程序并观察输出
echo ----------------------------------------

echo 执行命令: VirtualDiskTool32.exe --test-mount
echo 请仔细观察以下输出信息:
echo.

echo === 程序输出开始 ===
VirtualDiskTool32.exe --test-mount
echo === 程序输出结束 ===

echo.
echo 程序退出码: %ERRORLEVEL%

echo.
echo ----------------------------------------
echo 步骤3: 检查执行后的进程状态
echo ----------------------------------------

echo 等待3秒...
timeout /t 3 /nobreak >nul

echo 执行后 VirtualDiskTool32.exe 进程:
tasklist /fi "imagename eq VirtualDiskTool32.exe" /fo table
echo.

echo 执行后进程数量:
tasklist /fi "imagename eq VirtualDiskTool32.exe" | find /c "VirtualDiskTool32.exe"
echo.

echo ----------------------------------------
echo 步骤4: 分析可能的问题原因
echo ----------------------------------------

echo.
echo 🔍 可能的问题原因分析:
echo.
echo ❌ 原因1: 权限提升逻辑错误
echo   - 条件判断: (os_ver.dwMajorVersion >= 6) && (argc <= 1 || strcmp(argv[1], "/UAC") != 0)
echo   - 如果条件始终为真，会无限触发权限提升
echo.
echo ❌ 原因2: 参数传递格式错误
echo   - 当前格式: /UAC <logical_drives> <cmdline_ptr>
echo   - 如果参数解析失败，新进程无法识别自己是UAC进程
echo.
echo ❌ 原因3: ExitProcess(0) 未正确调用
echo   - 原进程应该在启动UAC进程后立即退出
echo   - 如果没有退出，会继续执行导致循环
echo.
echo ❌ 原因4: ShellExecute 调用问题
echo   - 可能ShellExecute失败但程序继续执行
echo   - 或者启动的进程参数不正确
echo.
echo ❌ 原因5: 字符编码问题
echo   - ANSI/Unicode 字符串转换问题
echo   - 导致参数传递或比较失败
echo.

echo 🔧 建议的调试方法:
echo.
echo 1. 添加更多调试输出
echo   - 显示每个关键判断的结果
echo   - 显示参数构建和传递过程
echo   - 显示进程创建和退出状态
echo.
echo 2. 使用调试模式
echo   - 启用 DEBUG_InCMD 宏定义
echo   - 不实际执行 ShellExecute，只显示信息
echo.
echo 3. 手动测试参数解析
echo   - 手动构造 /UAC 参数调用程序
echo   - 验证参数解析逻辑是否正确
echo.
echo 4. 检查编译配置
echo   - 确认是否正确编译了最新代码
echo   - 检查字符集设置 (Unicode/ANSI)
echo.

echo ========================================
echo 深度调试完成
echo ========================================

pause
