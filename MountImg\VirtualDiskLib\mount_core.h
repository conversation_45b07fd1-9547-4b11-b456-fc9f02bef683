﻿/*
 * mount_core.h
 * 虚拟磁盘挂载核心功能
 * 
 * 功能：基于MountImg_Simple的核心挂载逻辑
 * 支持：ImDisk和DiscUtils两种挂载方式
 */

#ifndef MOUNT_CORE_H
#define MOUNT_CORE_H

#include <windows.h>

#ifdef __cplusplus
extern "C" {
#endif

// 挂载结果枚举
typedef enum {
    MOUNT_SUCCESS = 0,              // 挂载成功
    MOUNT_ERROR_INVALID_PARAMS,     // 无效参数
    MOUNT_ERROR_FILE_NOT_FOUND,     // 文件不存在
    MOUNT_ERROR_MOUNT_FAILED,       // 挂载失败
    MOUNT_ERROR_UNMOUNT_FAILED,     // 卸载失败
    MOUNT_ERROR_DRIVE_IN_USE,       // 驱动器正在使用
    MOUNT_ERROR_INSUFFICIENT_PRIVILEGES, // 权限不足
    MOUNT_ERROR_UNKNOWN             // 未知错误
} MOUNT_RESULT;

// 挂载策略枚举
typedef enum {
    MOUNT_STRATEGY_AUTO = 0,        // 自动选择最佳策略
    MOUNT_STRATEGY_IMDISK_FIRST,    // 优先使用ImDisk
    MOUNT_STRATEGY_DISCUTILS_FIRST  // 优先使用DiscUtils
} MountStrategy;

// 磁盘信息结构
typedef struct {
    char file_path[512];            // 文件路径
    char file_system[32];           // 文件系统类型
    long long size_bytes;           // 磁盘大小（字节）
    int size_mb;                    // 磁盘大小（MB）
    int partition_count;            // 分区数量
    int is_readonly;                // 是否只读
    char format_type[16];           // 格式类型（VMDK/VHD/ISO等）
} DiskInfo;

// 挂载信息结构
typedef struct {
    char drive_letter[8];           // 驱动器号
    char image_path[512];           // 镜像文件路径
    char file_system[32];           // 文件系统类型
    int device_number;              // 设备号
    int readonly;                   // 是否只读
    int partition;                  // 分区号
    SYSTEMTIME mount_time;          // 挂载时间
    MountStrategy strategy_used;    // 使用的挂载策略
} MountInfo;

///*
// * 初始化挂载核心模块
// * 
// * 返回值：
// *   0: 成功，非0: 失败
// */
//int InitMountCore(void);

///*
// * 清理挂载核心模块
// */
//void CleanupMountCore(void);

/*
 * 检测文件格式
 * 
 * 参数：
 *   filePath: 文件路径
 * 
 * 返回值：
 *   文件格式字符串，失败返回NULL
 */
const char* DetectFileFormat(const char* filePath);

///*
// * 获取磁盘信息
// * 
// * 参数：
// *   filePath: 镜像文件路径
// *   diskInfo: 输出的磁盘信息
// * 
// * 返回值：
// *   0: 成功，非0: 失败
// */
//int GetDiskInfo(const char* filePath, DiskInfo* diskInfo);

/*
 * 注意：旧的API函数已废弃，请使用新的简化API
 * 新API：InitializeVirtualDiskLib, MountVirtualDisk, UnmountVirtualDisk, CleanupVirtualDiskLib
 */

///*
// * 注意：以下工具函数已简化，主要API请使用新的简化接口
// */
//
///*
// * 兼容的GetTickCount函数（支持XP）
// *
// * 返回值：
// *   时间戳（毫秒）
// */
//__int64 GetTickCount_Compatible(void);

/*
 * ===== 新的简化API（完全按照MountImg.c流程） =====
 */

///*
// * 初始化VirtualDiskLib
// *
// * 返回值：
// *   TRUE: 成功，FALSE: 失败
// */
//BOOL InitializeVirtualDiskLib(void);

///*
// * 挂载虚拟磁盘（完全按照MountImg.c流程）
// *
// * 参数：
// *   imagePath: 镜像文件路径
// *   driveLetter: 目标驱动器号（如"Z:"）
// *   readonly: 是否只读挂载（0=读写，1=只读）
// *   partition: 分区号（默认1）
// *
// * 返回值：
// *   MOUNT_RESULT枚举值
// */
//MOUNT_RESULT MountVirtualDisk_Core(const char* imagePath, const char* driveLetter, int readonly, int partition);

/*
 * 卸载虚拟磁盘
 *
 * 参数：
 *   driveLetter: 驱动器号（如"Z:"）
 *
 * 返回值：
 *   MOUNT_RESULT枚举值
 */
MOUNT_RESULT UnmountVirtualDisk_Core(const char* driveLetter);

///*
// * 清理VirtualDiskLib
// */
//void CleanupVirtualDiskLib(void);

#ifdef __cplusplus
}
#endif

#endif // MOUNT_CORE_H
